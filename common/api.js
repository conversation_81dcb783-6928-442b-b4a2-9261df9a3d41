// 获取菜单
export const fetchMenu = (params, config = {}) => uni.$u.http.post('/ebapi/public_api/index', params, config)

// 获取微信支付
export const getWxPay = (params = {}) => {
	return uni.$u.http.post('/v3/wapPay', params)
}

// 获取支付宝支付
export const getAliPay = (params = {}) => {
	return uni.$u.http.post('/aliPay/wapPay', params)
}

// pc微信扫码支付
export const wxScanPay = (params = {}) => {
	return uni.$u.http.post('/v3/nativePay', params)
}

// pc支付宝扫码支付
export const aliPayScanPay = (params = {}) => {
	return uni.$u.http.post('/aliPay/tradePreCreatePay', params)
}

// 获取套餐列表
export const getPackageList = (params = {}) => {
	return uni.$u.http.get('/external/goodGroupInfo/list', {
		params
	})
}

// 获取套餐详情
export const getPackageDetail = (id) => {
	return uni.$u.http.get(`/external/goodGroupInfo/${id}`)
}

// 生成订单
export const creatOrder = (params = {}) => {
	return uni.$u.http.post('/external/orderInfo/add', params)
}

// 获取我的订单列表
export const getOrderList = (params = {}) => {
	return uni.$u.http.get('/external/orderInfo/list', {
		params
	})
}

// 获取订单详情
export const getOrderDetail = (id) => {
	return uni.$u.http.get(`/external/orderInfo/${id}`)
}


// 取消订单
export const closeOrder = (params = {}) => {
	return uni.$u.http.post('/external/orderInfo/close', params)
}

// 删除订单
export const delOrder = (orderId) => {
	return uni.$u.http.delete(`/external/order/${orderId}`)
}


// 获取用户信息
export const getUserInfo = (params = {}) => {
	return uni.$u.http.post('/user/info/getLoginUserInfo', params)
}

// 用token获取用户信息
export const getUserInfoByToken = () => {
	return uni.$u.http.get(`/user/info/getUserInfoFromToken`)
}

// 查询用户的服务详情
export const getServiceDetail = () => {
	return uni.$u.http.get(`/external/getUserBalance`)
}


 