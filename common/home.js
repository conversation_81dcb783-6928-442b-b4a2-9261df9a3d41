// 获取商品

export const getPackageList = (params = {}) => {
	return uni.$u.http.get(`/app/goods/list`, {
		params
	})
}

export const getCategoryList = (params = {}) => {
	return uni.$u.http.get('/app/category/list', {
		params
	})
}

export const findGoogs = (id) => {
	return uni.$u.http.get(`/app/goods/${id}`)
}

export const login = (params) => {
	return uni.$u.http.post(`/app/user/login`, params)
}


export const dPhone = (params) => {
	return uni.$u.http.post(`/app/user/decrypt/phone`, params)
}


export const getSwiper = () => {
	return uni.$u.http.get(`/app/rollImage/list`)
}


export const updateUser = (params) => {
	return uni.$u.http.put(`/app/user`, params)
}


export const getUserById = (id) => {
	return uni.$u.http.get(`/app/user/${id}`)
}


export const weixinPay = (params) => {
	return uni.$u.http.post(`/wechat/pay/unified`, params)
}


export const getCard = (params = {}) => {
	return uni.$u.http.get(`/app/cardSecret/getPassword`, {params})
}

