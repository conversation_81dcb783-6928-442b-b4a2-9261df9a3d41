<script>
import {
  mapState,
  mapActions,
  mapMutations
} from 'vuex'
import {
  getShareAndScanQuery
} from '@/util/tools.js'
import {
  bindDistributor
} from '@/api/common.js'

import {
  getDistrictList
} from '@/api/order.js'
export default {
  onShow(options) {
    console.log('onShowoptions', options);
    this.setDistribute(options);
    this.getUserInfo();
  },
  onLaunch: function (options) {
    // 获取地址数据并存储到 Vuex
    this.getDistrict();

    this.login().then(res => {
      this.setDistribute(options);
    });
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.User.userInfo
    })
  },
  methods: {
    ...mapActions(['login', 'getUserInfo']),
    getDistrict() {
      getDistrictList().then(res => {
        console.log('getDistrictList', res);
        if (res.code === 200) {


          // // 存储到 Vuex
          // this.$store.commit('SET_ADDRESS_LISTS', {
          //   provinceList,
          //   cityList,
          //   districtList
          // });

          // 存储到本地缓存
          uni.setStorageSync('addressLists', res.data);
        }
      })
    },
    /**
     * 如果从分销商分享或扫分销商的码进入小程序要绑定分销的用户
     */
    setDistribute(options) {
      console.log('options', options);
      let query = {}
      console.log('process.env.EVN', process.env.EVN);
      if (process.env.EVN === 'development') {
        query = {
          distributorId: '1866319618574921729'
        }
        // query = getShareAndScanQuery({
        // 	query: {
        // 		q: 'https%3A%2F%2Fmillenia.frp.yn.asqy.net%3FdistributorId%3D1866319618574921729'
        // 	}
        // })

      } else {
        query = getShareAndScanQuery(options)
      }
      if (query.distributorId && this.userInfo.id) {
        console.log('query', query);
        bindDistributor({
          distributorId: query.distributorId
        }).then(res => {
          console.log('bindDistributor', res);
          if (res.code !== 200) {
            uni.$u.toast(`${res.msg || '绑定失败'},请重新扫码`)
          }
        })
      }
    }
  },
}
</script>

<style lang="scss">
/*每个页面公共css */
@import "@/uni_modules/uview-ui/index.scss";
@import "@/common/common.scss";
</style>