
在package.josn中uni-app中配置不同环境的全局变量
// "VUE_APP_BASE_API": 
//http://19***********:9010  本地地址
//http://charge.yn.asqy.net/prod-api  测试环境线上地址

```json
"dev-h5": {//自定义编译平台配置，可通过cli方式调用
	"title": "开发环境",// 在HBuilderX中会显示在 运行/发行 菜单中
	"browser": "",//运行到的目标浏览器，仅当UNI_PLATFORM为h5时有效
	"env": {//环境变量
		"UNI_PLATFORM": "h5", //基准平台，UNI_PLATFORM仅支持填写uni-app默认支持的基准平台，目前仅限如下枚举值：h5、mp-weixin、mp-alipay、mp-baidu、mp-toutiao、mp-qq
		"VUE_APP_BASE_API": "http://*************:9010",//接口请求的地址
		"IS_PAGE_NAV":true//所有页面是否有头部导航
	},
	"define": { //自定义条件编译
	    "CUSTOM-CONST": true //自定义条件编译常量，建议为大写
	}
}
```
