import {
		mapState
	} from 'vuex';
const getCurrentRoute = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] || {}
  // console.log('currentPage', currentPage);
  return currentPage
}
export default {
	onShow() {
		// 该对象已集成到this.$u中，内部属性如下
		uni.$u.mpShare = {
			// title: '测试', // 默认为小程序名称，可自定义
			// path: this.joinParams('pages/home/<USER>'), // 默认为当前页面路径，一般无需修改，QQ小程序不支持
			path: this.joinParams(), // 默认为当前页面路径，一般无需修改，QQ小程序不支持
			// 分享图标，路径可以是本地文件路径、代码包文件路径或者网络图片路径。
			// 支持PNG及JPG，默认为当前页面的截图
			imageUrl: 'https://millenia.oss-cn-wuhan-lr.aliyuncs.com/2024-12-24/1735005871017_b7fdeb0f.png'
		}
	},
	computed: {
		...mapState({
			userInfo: (state) => {
				// console.log('state.User', state.User);
				return state.User.userInfo
			},
		}),
	},
	methods: {
		joinParams() {
			console.log('getCurrentRoute', getCurrentRoute());
			const {options = {},route} = getCurrentRoute();
			let params = {
				...options,
				distributorId: process.env.NODE_ENV === 'production' ? this.userInfo.distributor.id :
					'1866319618574921729'
			}
			let query = uni.$u.queryParams(params)
			return `${route}${query}`
		},
	}
}