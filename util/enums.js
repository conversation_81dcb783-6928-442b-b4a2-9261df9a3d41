// 协议类型
export const privacyEnums = [{
	value: 'service',
	label: '北信源会员服务协议',
}, {
	value: 'privacy',
	label: '隐私协议',
}, {
	value: 'buy',
	label: '购买协议',
}, ]

// 订单状态
export const orderStatusEnums = [{
		value: '1',
		label: '待付款',
		// label: '待支付',
		color: '#FD3131',
		type: 'payment'
	},
	{
		value: '2',
		label: '待发货',
		color: '#C9C9C9',
		type: 'delivery'
	},

	{
		value: '7',
		label: '待收货',
		// label: '已发货',
		color: '#ffba00',
		type: 'receiving'
	},
	{
		value: '8',
		label: '已完成',
		color: '#ffba00',
	},
	{
		value: '5',
		label: '已退款',
		color: '#F1C887',
		type: 'refund'
	},
	{
		value: '3',
		label: '已取消',
		color: '#F1C887',
	},
]

// 卡密的使用状态
export const useStatusEnums = [{
	value: '1',
	color: '#FD3131',
	label: '未使用',
}, {
	value: '2',
	color: '#C9C9C9',
	label: '已使用',
}]

//  售后状态
export const afterSaleStatus = [{
		value: '4',
		label: '退款审核中'
	},
	{
		value: '5',
		label: '已退款'
	},
	{
		value: '6',
		label: '退款已驳回'
	},
]

// 性别
export const sexEnums = [{
	value: '1',
	label: '男',
}, {
	value: '0',
	label: '女',
}]

export const yesNoEnums = [{
	value: '02',
	label: '是',
	prefix: 'A.'
}, {
	value: '01',
	label: '否',
	prefix: 'B.'
}]

//  分销商审核状态
export const distributeAuditStatus = [{
		value: '1',
		label: '待审核'
	},
	{
		value: '2',
		label: '已通过',
	},
	{
		value: '3',
		label: '已驳回',
	}
]

// 时长24小时制
export const timeList = new Array(24).fill(0).map((item, index) => ({
        value: index + 1,
        label: `${index + 1}小时`,
      }))