// import CryptoJS from 'crypto-js';
// import config from '@/common/config.js';

// const cryptoPk = 'javax.crypto.spec.SecretKeySpec@17fd7'; //密钥

// export const aesEncrypt = (data) => {
// 	const iv = CryptoJS.lib.WordArray.random(16); //初始化向量
// 	// 密钥和初始化向量（需要和服务器端保持一致）
// 	const key = CryptoJS.enc.Utf8.parse(cryptoPk);
// 	// 加密
// 	const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), key, {
// 		iv,
// 		mode: CryptoJS.mode.CBC,
// 		padding: CryptoJS.pad.Pkcs7
// 	});

// 	// 转换为字符串（Base64编码）
// 	return {
// 		iv: CryptoJS.enc.Base64.stringify(iv),
// 		encrypt: encrypted.toString()
// 	};
// }