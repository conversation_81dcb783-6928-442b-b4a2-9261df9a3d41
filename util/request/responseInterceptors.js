import store from '@/store/index.js';

// 防重复弹框
const throttle = () => {
	let timer = null;
	return (callback) => {
		if (timer) {
			return
		}
		callback()
		timer = setTimeout(() => {
			clearTimeout(timer);
			timer = null;
		}, 1000)
	}
}

const stopRepeatTip = throttle()

/**
 * 响应拦截
 * @param {Object} http 
 */
module.exports = (vm) => {
	uni.$u.http.interceptors.response.use((response) => {
		/* 对响应成功做点什么 可使用async await 做异步操作*/
		const data = response.data
		if (typeof data !== 'object') {
			return data
		}

		return data || {}
	}, (response) => {
		/*  对响应错误做点什么 （statusCode !== 200）*/
		return Promise.reject(response)
	})
}