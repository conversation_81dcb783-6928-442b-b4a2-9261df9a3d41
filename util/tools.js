/**
 * 存用户信息的名称
 */
// export const TOKEN_NAME = 'token';

export const USERINFO_NAME = 'weixin';

/**
 * 设置缓存的数据
 */
export const setStore = (key, value) => {
  uni.setStorageSync(key, value);
}

/**
 * 获取缓存的数据
 */
export const getStore = (key) => {
  return uni.getStorageSync(key);
}


/**
 * 获取当前的路由
 */
export const getCurrentRoute = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] || {}
  console.log('currentPage', currentPage);
  return currentPage.route
}

/**
 * 判断用户有没有登录
 */
export const isLogin = (url) => {
  // 不用登录的页面
  const whiteList = ['/pages/login/login'];
  const currentUrl = url || getCurrentRoute();
  const route = currentUrl.split('?')[0];
  let token = getStore(TOKEN_NAME);
  if (!token && !whiteList.includes(route)) {
    uni.$u.route('/pages/login/login');
    return false
  } else {
    return true
  }
}

// 乘法
export function mul(a, b) {
  let m = 0;
  const s1 = a.toString();
  const s2 = b.toString();
  try {
    m += s1.split(".")[1].length;
  } catch (e) { }
  try {
    m += s2.split(".")[1].length;
  } catch (e) { }
  return (Number(s1.replace(".", "")) * Number(s2.replace(".", ""))) / Math.pow(10, m);
}

/**
 * 预览pdf
 */
export const previewPdf = (url) => {
  if (!url) {
    uni.$u.toast('文件不存在');
    return
  }
  uni.showLoading({
    title: '加载中'
  });
  uni.downloadFile({
    url,
    success: function (res) {
      var filePath = res.tempFilePath;
      uni.openDocument({
        filePath: filePath,
        showMenu: true,
        fail: function (res) {
          uni.$u.toast('预览失败');
          uni.hideLoading();
        },
        complete: function (res) {
          uni.hideLoading();
        },
      });
    }
  });
}

export const getQueryByUrl = (url) => {
  console.log('url', url);
  if (!url) {
    return {}
  }
  let queryStr = url.split('?')[1];
  if (!queryStr) {
    return {}
  }
  return queryStr.split('&').reduce((prev, val) => {
    let [key, value] = val.split('=');
    prev[key] = value;
    return prev
  }, {})
}

export const getShareAndScanQuery = (options = {},) => {
  if ([1007, 1008].includes(options.scene)) {
    // 小程序分享进入
    return options.query
  } else if (options.scene === 1011 || options.scene === 1012) {
    // 小程序扫码进入
    const url = decodeURIComponent(options.query.q);
    return getQueryByUrl(url);
  } else {
    return {}
  }
}