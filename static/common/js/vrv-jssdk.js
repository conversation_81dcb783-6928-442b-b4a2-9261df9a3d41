!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.vrv=t():e.vrv=t()}(window,(function(){return t=[function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(9);function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,r(o.key),o)}}e.exports=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(t){return e.exports=n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(5).default,o=n(14);e.exports=function(e,t){if(t&&("object"==r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(15);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(t){return e.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){n=n(20)(),e.exports=n;try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},function(e,t,n){var r="object"==typeof Reflect?Reflect:null,o=r&&"function"==typeof r.apply?r.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)},i=r&&"function"==typeof r.ownKeys?r.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)},s=Number.isNaN||function(e){return e!=e};function u(){u.init.call(this)}e.exports=u,e.exports.once=function(e,t){return new Promise((function(n,r){function o(n){e.removeListener(t,i),r(n)}function i(){"function"==typeof e.removeListener&&e.removeListener("error",o),n([].slice.call(arguments))}var s,u,a;v(e,t,i,{once:!0}),"error"!==t&&(u=o,a={once:!0},"function"==typeof(s=e).on)&&v(s,"error",u,a)}))},(u.EventEmitter=u).prototype._events=void 0,u.prototype._eventsCount=0,u.prototype._maxListeners=void 0;var a=10;function l(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function c(e){return void 0===e._maxListeners?u.defaultMaxListeners:e._maxListeners}function p(e,t,n,r){var o,i;return l(n),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,n.listener||n),o=e._events),i=o[t]),void 0===i?(i=o[t]=n,++e._eventsCount):("function"==typeof i?i=o[t]=r?[n,i]:[i,n]:r?i.unshift(n):i.push(n),0<(o=c(e))&&i.length>o&&!i.warned&&(i.warned=!0,(r=new Error("Possible EventEmitter memory leak detected. "+i.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit")).name="MaxListenersExceededWarning",r.emitter=e,r.type=t,r.count=i.length,n=r,console)&&console.warn&&console.warn(n)),e}function f(e,t,n){return(t=function(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}.bind(e={fired:!1,wrapFn:void 0,target:e,type:t,listener:n})).listener=n,e.wrapFn=t}function h(e,t,n){if(void 0===(e=e._events))return[];if(void 0===(e=e[t]))return[];if("function"==typeof e)return n?[e.listener||e]:[e];if(n){for(var r=e,o=new Array(r.length),i=0;i<o.length;++i)o[i]=r[i].listener||r[i];return o}return d(e,e.length)}function y(e){var t=this._events;if(void 0!==t){if("function"==typeof(t=t[e]))return 1;if(void 0!==t)return t.length}return 0}function d(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e[r];return n}function v(e,t,n,r){if("function"==typeof e.on)r.once?e.once(t,n):e.on(t,n);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function o(i){r.once&&e.removeEventListener(t,o),n(i)}))}}Object.defineProperty(u,"defaultMaxListeners",{enumerable:!0,get:function(){return a},set:function(e){if("number"!=typeof e||e<0||s(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");a=e}}),u.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},u.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||s(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},u.prototype.getMaxListeners=function(){return c(this)},u.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var r="error"===e,i=this._events;if(void 0!==i)r=r&&void 0===i.error;else if(!r)return!1;if(r){if((s=0<t.length?t[0]:s)instanceof Error)throw s;throw(r=new Error("Unhandled error."+(s?" ("+s.message+")":""))).context=s,r}var s=i[e];if(void 0===s)return!1;if("function"==typeof s)o(s,this,t);else{var u=s.length,a=d(s,u);for(n=0;n<u;++n)o(a[n],this,t)}return!0},u.prototype.on=u.prototype.addListener=function(e,t){return p(this,e,t,!1)},u.prototype.prependListener=function(e,t){return p(this,e,t,!0)},u.prototype.once=function(e,t){return l(t),this.on(e,f(this,e,t)),this},u.prototype.prependOnceListener=function(e,t){return l(t),this.prependListener(e,f(this,e,t)),this},u.prototype.off=u.prototype.removeListener=function(e,t){var n,r,o,i,s;if(l(t),void 0!==(r=this._events)&&void 0!==(n=r[e]))if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(o=-1,i=n.length-1;0<=i;i--)if(n[i]===t||n[i].listener===t){s=n[i].listener,o=i;break}if(o<0)return this;if(0===o)n.shift();else{for(var u=n,a=o;a+1<u.length;a++)u[a]=u[a+1];u.pop()}1===n.length&&(r[e]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",e,s||t)}return this},u.prototype.removeAllListeners=function(e){var t,n=this._events;if(void 0!==n)if(void 0===n.removeListener)0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]);else if(0===arguments.length){for(var r,o=Object.keys(n),i=0;i<o.length;++i)"removeListener"!==(r=o[i])&&this.removeAllListeners(r);this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0}else if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(i=t.length-1;0<=i;i--)this.removeListener(e,t[i]);return this},u.prototype.listeners=function(e){return h(this,e,!0)},u.prototype.rawListeners=function(e){return h(this,e,!1)},u.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):y.call(e,t)},u.prototype.listenerCount=y,u.prototype.eventNames=function(){return 0<this._eventsCount?i(this._events):[]}},function(e,t){function n(e,t,n,r,o,i,s){try{var u=e[i](s),a=u.value}catch(e){return n(e)}u.done?t(a):Promise.resolve(a).then(r,o)}e.exports=function(e){return function(){var t=this,r=arguments;return new Promise((function(o,i){var s=e.apply(t,r);function u(e){n(s,o,i,u,a,"next",e)}function a(e){n(s,o,i,u,a,"throw",e)}u(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(5).default,o=n(13);e.exports=function(e){return e=o(e,"string"),"symbol"==r(e)?e:e+""},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(16),o=n(17),i=n(18),s=n(19);e.exports=function(e){return r(e)||o(e)||i(e)||s()},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(9);e.exports=function(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(5).default;e.exports=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);if(n=n.call(e,t||"default"),"object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(t,r){return e.exports=n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,n(t,r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(10);e.exports=function(e){if(Array.isArray(e))return r(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(10);e.exports=function(e,t){var n;if(e)return"string"==typeof e?r(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(5).default;e.exports=function(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.exports=function(){return n},e.exports.__esModule=!0,e.exports.default=e.exports;var t,n={},o=Object.prototype,i=o.hasOwnProperty,s=Object.defineProperty||function(e,t,n){e[t]=n.value},u=(b="function"==typeof Symbol?Symbol:{}).iterator||"@@iterator",a=b.asyncIterator||"@@asyncIterator",l=b.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(t){c=function(e,t,n){return e[t]=n}}function p(e,n,r,o){var i,u,a,l;n=n&&n.prototype instanceof m?n:m,n=Object.create(n.prototype),o=new P(o||[]);return s(n,"_invoke",{value:(i=e,u=r,a=o,l=h,function(e,n){if(l===d)throw Error("Generator is already running");if(l===v){if("throw"===e)throw n;return{value:t,done:!0}}for(a.method=e,a.arg=n;;){var r=a.delegate;if(r&&(r=function e(n,r){var o=r.method,i=n.iterator[o];return i===t?(r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),g):"throw"===(o=f(i,n.iterator,r.arg)).type?(r.method="throw",r.arg=o.arg,r.delegate=null,g):(i=o.arg)?i.done?(r[n.resultName]=i.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}(r,a))){if(r===g)continue;return r}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(l===h)throw l=v,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);if(l=d,"normal"===(r=f(i,u,a)).type){if(l=a.done?v:y,r.arg===g)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(l=v,a.method="throw",a.arg=r.arg)}})}),n}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=p;var h="suspendedStart",y="suspendedYield",d="executing",v="completed",g={};function m(){}function k(){}function _(){}var b,B,A=((B=(B=(c(b={},u,(function(){return this})),Object.getPrototypeOf))&&B(B(L([]))))&&B!==o&&i.call(B,u)&&(b=B),_.prototype=m.prototype=Object.create(b));function w(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){var n;s(this,"_invoke",{value:function(o,s){function u(){return new t((function(n,u){!function n(o,s,u,a){var l;if("throw"!==(o=f(e[o],e,s)).type)return(s=(l=o.arg).value)&&"object"==r(s)&&i.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,u,a)}),(function(e){n("throw",e,u,a)})):t.resolve(s).then((function(e){l.value=e,u(l)}),(function(e){return n("throw",e,u,a)}));a(o.arg)}(o,s,n,u)}))}return n=n?n.then(u,u):u()}})}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function L(e){if(e||""===e){var n,o=e[u];if(o)return o.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return n=-1,(o=function r(){for(;++n<e.length;)if(i.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r}).next=o}throw new TypeError(r(e)+" is not iterable")}return s(A,"constructor",{value:k.prototype=_,configurable:!0}),s(_,"constructor",{value:k,configurable:!0}),k.displayName=c(_,l,"GeneratorFunction"),n.isGeneratorFunction=function(e){return!!(e="function"==typeof e&&e.constructor)&&(e===k||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,c(e,l,"GeneratorFunction")),e.prototype=Object.create(A),e},n.awrap=function(e){return{__await:e}},w(x.prototype),c(x.prototype,a,(function(){return this})),n.AsyncIterator=x,n.async=function(e,t,r,o,i){void 0===i&&(i=Promise);var s=new x(p(e,t,r,o),i);return n.isGeneratorFunction(t)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},w(A),c(A,l,"Generator"),c(A,u,(function(){return this})),c(A,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t,n=Object(e),r=[];for(t in n)r.push(t);return r.reverse(),function e(){for(;r.length;){var t=r.pop();if(t in n)return e.value=t,e.done=!1,e}return e.done=!0,e}},n.values=L,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&i.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(r,o){return u.type="throw",u.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var o=this.tryEntries.length-1;0<=o;--o){var s=this.tryEntries[o],u=s.completion;if("root"===s.tryLoc)return r("end");if(s.tryLoc<=this.prev){var a=i.call(s,"catchLoc"),l=i.call(s,"finallyLoc");if(a&&l){if(this.prev<s.catchLoc)return r(s.catchLoc,!0);if(this.prev<s.finallyLoc)return r(s.finallyLoc)}else if(a){if(this.prev<s.catchLoc)return r(s.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return r(s.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}var s=(o=o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc?null:o)?o.completion:{};return s.type=e,s.arg=t,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var n,r,o=this.tryEntries[t];if(o.tryLoc===e)return"throw"===(n=o.completion).type&&(r=n.arg,O(o)),r}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:L(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},n},e.exports.__esModule=!0,e.exports.default=e.exports},function(e){e.exports=JSON.parse('{"name":"jssdk","version":"2.0.0","description":"jssdk","main":"lib/vrv-jssdk.js","scripts":{"loading":"webpack --config webpack.config.loading.js","lib":"babel src --out-dir lib --copy-files","build":"npm run loading && webpack","build-auth":"npm run loading && cross-env useAuth=1 webpack","build:hsg":"npm run loading && cross-env envType=hsg webpack","build:hsg-auth":"npm run loading && cross-env useAuth=1 envType=hsg webpack","build:zf":"npm run loading && cross-env envType=zf  webpack","build:zhongtie":"npm run loading && cross-env envType=zhongtie webpack","build:bjyjy":"npm run loading && cross-env envType=zbjyjyf webpack","build:gd":"npm run loading && cross-env envType=gd webpack","build:htsy":"npm run loading && cross-env envType=htsy webpack","build:zjw":"npm run loading && cross-env envType=zjw webpack","build:scchj":"npm run loading && cross-env envType=scchj webpack","build:gayp":"npm run loading && cross-env envType=gayp webpack","build:sljj":"npm run loading && cross-env envType=sljj webpack","build:klyy":"npm run loading && cross-env envType=klyy webpack"},"devDependencies":{"@babel/core":"^7.10.3","@babel/plugin-proposal-class-properties":"^7.10.1","@babel/plugin-proposal-decorators":"^7.10.5","@babel/plugin-transform-runtime":"^7.10.3","@babel/preset-env":"^7.10.3","@babel/runtime":"^7.10.3","babel-loader":"^8.1.0","babel-plugin-add-module-exports":"^1.0.4","babel-polyfill":"^6.26.0","clean-webpack-plugin":"^3.0.0","copy-webpack-plugin":"^6.1.0","cross-env":"^7.0.3","css-loader":"^4.3.0","filemanager-webpack-plugin":"^2.0.5","mini-css-extract-plugin":"^0.11.2","optimize-css-assets-webpack-plugin":"^5.0.4","postcss-loader":"^4.0.2","style-loader":"^1.2.1","uglifyjs-webpack-plugin":"^2.2.0","webpack":"^4.44.2","webpack-cli":"^3.3.12"},"author":"vrv","license":"ISC","engines":{"node":"~10.16.2","npm":"~6.9.0"},"dependencies":{}}')},function(e,t,n){n.r(t);var r=n(0),o=n.n(r),i=(r=n(1),n.n(r)),s=(r=n(3),n.n(r)),u=(r=n(2),n.n(r)),a=(r=n(4),n.n(r)),l=(r=n(11),n.n(r)),c=(r=n(8),n.n(r)),p=(r=n(6),n.n(r)),f=function(){return i()((function e(){function t(){}var n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};o()(this,e),this.success=n.success||t,this.error=n.error||t,this.progress=n.progress||t,this.url=n.url,this.method=n.method,this.dataType=n.dataType||"text",this.params=n.params||{},this.headers=n.headers||{},this.chunkRetry=n.chunkRetry,this.isBreakpoint=n.isBreakpoint,this.isVerification=n.isVerification,this.verificationUrl=n.verificationUrl,this.verificationCallback=n.verificationCallback||function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e.isFile},this.fileData=Object.assign({},{fileSize:0,burstSize:0,fileNumber:0,fileId:"",fileName:"",filePath:"",fileHash:""},n.fileData),this.storeKey="upload_"+this.fileData.fileId,this.status=0,this.currentIndex=0,this.totalNumber=this.fileData.fileNumber,this.burstData=Array.from({length:this.totalNumber}).map((function(e,t){return t})),this.exeQueueMap=new Map,this.exeQueueSize=n.exeQueueSize||5,this.progressData={},this.uploadBurstData=[],this.init()}),[{key:"init",value:(n=c()(p.a.mark((function e(){var t;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isBreakpoint||vrv.jssdk.deleteStore({key:this.storeKey,success:function(){}}),this.isVerification)return e.next=4,this.verificationFileAjax();e.next=9;break;case 4:if(t=e.sent,this.verificationCallback(t))return this.progress(100),this.success(t),e.abrupt("return");e.next=9;break;case 9:return e.next=11,this.initData();case 11:this.initDef(),this.initQueue();case 13:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"initData",value:(t=c()(p.a.mark((function e(){var t,n,r=this;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isBreakpoint)return e.next=3,this.getUploadInfo();e.next=5;break;case 3:(t=e.sent)&&(t=t.split(","),n=new Set(this.burstData),t.forEach((function(e){e=Number(e),n.delete(e),r.progressData[e]=100,r.uploadBurstData.push(e)})),this.currentIndex=t.length,this.burstData=Array.from(n));case 5:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"initDef",value:function(){var e=this;h.defineProperty(this.burstData,"push",(function(t){e.exeQueueMap.size<e.exeQueueSize||-1==e.exeQueueSize?e.getBurstData(t):Array.prototype.push.call(this,t)})),h.defineProperty(this.exeQueueMap,"set",(function(t,n){0==e.status&&(Map.prototype.set.call(this,t,n),e.uploadSection(n))})),h.defineProperty(this.exeQueueMap,"delete",(function(t){0==e.status&&(Map.prototype.delete.call(this,t),0<e.burstData.length)&&(t=e.burstData.shift(),e.getBurstData(t))}))}},{key:"getUploadInfo",value:(e=c()(p.a.mark((function e(){var t=this;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,n){vrv.jssdk.getStore({key:t.storeKey,success:function(t){e(t.value)}})})));case 1:case"end":return e.stop()}}),e)}))),function(){return e.apply(this,arguments)})},{key:"initQueue",value:function(){var e=this.burstData.splice(0,this.exeQueueSize);this.getBurstData.apply(this,l()(e))}},{key:"getBurstData",value:function(){for(var e=this,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];vrv.jssdk.getLargeFilePartToLocal({burstSize:this.fileData.burstSize,filePath:this.fileData.filePath,fileIndex:n,success:function(t){(t.response||t||[]).forEach((function(t){e.exeQueueMap.set(t.fileIndex,Object.assign({},t,e.fileData))}))}})}},{key:"getProgress",value:function(){var e,t=0;for(e in this.progressData)t+=this.progressData[e];return Math.floor(t/this.totalNumber*100)/100}},{key:"verificationFileAjax",value:function(){var e,t,n=this,r=new XMLHttpRequest,o=new FormData;for(e in this.fileData)o.append(e,this.fileData[e]);for(t in this.params)o.append(t,this.params[t]);return new Promise((function(e,t){for(var i in r.open(n.method,n.verificationUrl+"?d="+(new Date).getTime()),r.onload=function(t){if(4==r.readyState)if(200==r.status){var o=r.responseText;if("json"==n.dataType)try{o=JSON.parse(r.responseText)}catch(t){console.error(t)}e(o)}else n.error(r.status,r)},n.headers)r.setRequestHeader(i,n.headers[i]);r.onerror=function(e){n.error(r.status,r)},r.send(o)}))}},{key:"uploadSection",value:function(e){var t,n,r,o=this,i=new XMLHttpRequest,s=new FormData;for(t in e)s.append(t,e[t]);for(n in this.params)s.append(n,this.params[n]);for(r in i.open(this.method,this.url+"?d="+(new Date).getTime()),i.upload.addEventListener("progress",(function(t){t.lengthComputable&&(t=Math.floor(t.loaded/t.total*100*100)/100,o.progressData[e.fileIndex]=t,t=o.getProgress(),o.progress(t))}),!1),i.onload=function(t){if(4==i.readyState&&0==o.status)if(o.exeQueueMap.delete(e.fileIndex),200==i.status)if(o.currentIndex++,o.uploadBurstData.push(e.fileIndex),o.currentIndex==o.totalNumber){var n=i.responseText;if("json"==o.dataType)try{n=JSON.parse(i.responseText)}catch(t){console.error(t)}vrv.jssdk.deleteStore({key:o.storeKey,success:function(){}}),o.success(n)}else vrv.jssdk.setStore({key:o.storeKey,value:o.uploadBurstData.join(","),success:function(){}});else 403==i.status||404==i.status?o.xhrError(i):o.chunkRetry?o.xhrRetry(e.fileIndex):(o.status=i.status,o.error(i.status,i));delete e.base64},this.headers)i.setRequestHeader(r,this.headers[r]);i.onerror=function(e){o.error(i.status,i)},i.send(s)}},{key:"xhrRetry",value:function(e){delete this.progressData[e],this.burstData.push(e)}},{key:"xhrError",value:function(e){this.status=e.status,this.error(e.status,e)}}]);var e,t,n}(),h={defineProperty:function(e,t,n){Reflect.defineProperty(e,t,{value:n,enumerable:!1,writable:!0,configurable:!0})}},y=(r=n(12),n.n(r)),d=(r=n(5),n.n(r)),v=(r=n(7),n.n(r));function g(e,t){var n,r=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)),r}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){y()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var k=["registerMessageNotice","registerNFCNotice","registerProgressNotice","registerRequestStreamNotice"],_=[].concat(k,["multiScan","setRightNavBarItem","setLeftNavBarItem"]),b="request",B="done",A={DEFAULT:"default",SUPPORT:"support",NOT_SUPPORT:"notSupport"},w="clientSuccess",x="initVersionSuccess";function S(e,t){return(2147483647&e)+(2147483647&t)^2147483648&e^2147483648&t}function O(e){for(var t="",n=7;0<=n;n--)t+="0123456789abcdef".charAt(e>>4*n&15);return t}function P(e,t){return e<<t|e>>>32-t}var L=function(e){for(var t,n,r,o=function(e){for(var t=1+(e.length+8>>6),n=new Array(16*t),r=0;r<16*t;r++)n[r]=0;for(r=0;r<e.length;r++)n[r>>2]|=e.charCodeAt(r)<<24-8*(3&r);return n[r>>2]|=128<<24-8*(3&r),n[16*t-1]=8*e.length,n}(e),i=new Array(80),s=1732584193,u=-271733879,a=-1732584194,l=271733878,c=-1009589776,p=0;p<o.length;p+=16){for(var f=s,h=u,y=a,d=l,v=c,g=0;g<80;g++){i[g]=g<16?o[p+g]:P(i[g-3]^i[g-8]^i[g-14]^i[g-16],1);var m=S(S(P(s,5),(m=u,n=a,r=l,(t=g)<20?m&n|~m&r:!(t<40)&&t<60?m&n|m&r|n&r:m^n^r)),S(S(c,i[g]),(t=g)<20?1518500249:t<40?1859775393:t<60?-1894007588:-899497514));c=l,l=a,a=P(u,30),u=s,s=m}s=S(s,f),u=S(u,h),a=S(a,y),l=S(l,d),c=S(c,v)}return O(s)+O(u)+O(a)+O(l)+O(c)};function j(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(j=function(){return!!e})()}function M(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(M=function(){return!!e})()}function C(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(C=function(){return!!e})()}function T(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(T=function(){return!!e})()}(r=new(function(e){function t(){return o()(this,t),e=this,n=t,r=arguments,n=u()(n),s()(e,T()?Reflect.construct(n,r||[],u()(e).constructor):n.apply(e,r));var e,n,r}return a()(t,e),i()(t,[{key:"registerProgressNotice",value:function(e){return this._callByApp("registerProgressNotice",e)}},{key:"takePhoto",value:function(e){return this._callByApp("takePhoto",e)}},{key:"getAccountInfo",value:function(e){return this._callByApp("getAccountInfo",e)}},{key:"sendMessage",value:function(e){return this._callByApp("sendMessage",e)}},{key:"getContactList",value:function(e){return this._callByApp("getContactList",e)}},{key:"getLocalFiles",value:function(e){return e=this._setDefault(e,[{type:"number",key:"size",val:10,min:1,max:15}]),this._callByApp("getLocalFiles",e)}},{key:"getLocalPhotos",value:function(e){return e=this._setDefault(e,[{type:"number",key:"size",val:10,min:1,max:15}]),this._callByApp("getLocalPhotos",e)}},{key:"getPosition",value:function(e){return this._callByApp("getPosition",e)}},{key:"showNavigationBar",value:function(e){return this._callByApp("showNavigationBar",e)}},{key:"getInfoWithSweep",value:function(e){return this._callByApp("getInfoWithSweep",e)}},{key:"getOrganization",value:function(e){return this._callByApp("getOrganization",e)}},{key:"getOrgInfo",value:function(e){return this._callByApp("getOrgInfo",e)}},{key:"closeView",value:function(e){return this._callByApp("closeView",e)}},{key:"getLanguage",value:function(e){return this._callByApp("getLanguage",e)}},{key:"getVersionMark",value:function(e){return this._callByApp("getVersionMark",e)}},{key:"getVersion",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t={version:this.sdkVersion,platform:this.deviceName};t=this._completionData(t);return e.success&&e.success(t),Promise.resolve(t)}},{key:"getClientInfo",value:function(e){return this._callByApp("getClientInfo",e)}},{key:"getClientKey",value:function(e){return this._callByApp("getClientKey",e)}},{key:"copyTextToPaste",value:function(e){return this._callByApp("copyTextToPaste",e)}},{key:"openFileURL",value:function(e){return this._callByApp("openFileURL",e)}},{key:"openImageURL",value:function(e){return this._callByApp("openImageURL",e)}},{key:"openChat",value:function(e){return this._callByApp("openChat",e)}},{key:"openMsgListView",value:function(e){return this._callByApp("openMsgListView",e)}},{key:"createGroup",value:function(e){return this._callByApp("createGroup",e)}},{key:"transferGroup",value:function(e){return this._callByApp("transferGroup",e)}},{key:"joinGroup",value:function(e){return this._callByApp("joinGroup",e)}},{key:"inviteMembers",value:function(e){return this._callByApp("inviteMembers",e)}},{key:"getGroupList",value:function(e){return this._callByApp("getGroupList",e)}},{key:"removeMembers",value:function(e){return this._callByApp("removeMembers",e)}},{key:"getGroupMembers",value:function(e){return this._callByApp("getGroupMembers",e)}},{key:"deleteGroup",value:function(e){return this._callByApp("deleteGroup",e)}},{key:"openAudioCall",value:function(e){return this._callByApp("openAudioCall",e)}},{key:"openVideoCall",value:function(e){return this._callByApp("openVideoCall",e)}},{key:"openVideoCallVendor",value:function(e){return this._callByApp("openVideoCallVendor",e)}},{key:"getOAuthCode",value:function(e){return this._callByApp("getOAuthCode",e)}},{key:"getUserIdMap",value:function(e){return this._callByApp("getUserIdMap",e)}},{key:"callPhone",value:function(e){return this._callByApp("callPhone",e)}},{key:"scanQrCode",value:function(e){return this._callByApp("scanQrCode",e)}},{key:"shareTo",value:function(e){return this._callByApp("shareTo",e)}},{key:"downloadFile",value:function(e){return this._callByApp("downloadFile",e)}},{key:"openFilePath",value:function(e){return this._callByApp("openFilePath",e)}},{key:"speech2Text",value:function(e){return this._callByApp("speech2Text",e)}},{key:"openExternalUrl",value:function(e){return this._callByApp("openExternalUrl",e)}},{key:"readNfc",value:function(e){return this._callByApp("readNfc",e)}},{key:"faceRecognize",value:function(e){return this._callByApp("faceRecognize",e)}},{key:"faceRegister",value:function(e){return this._callByApp("faceRegister",e)}},{key:"faceRemove",value:function(e){return this._callByApp("faceRemove",e)}},{key:"getServerInfo",value:function(e){return this._callByApp("getServerInfo",e)}},{key:"showTabBar",value:function(e){return this._callByApp("showTabBar",e)}},{key:"loadWorkBenchH5",value:function(e){return this._callByApp("loadWorkBenchH5",e)}},{key:"openBuyServerView",value:function(e){return this._callByApp("openBuyServerView",e)}},{key:"openBuyMyServerView",value:function(e){return this._callByApp("openBuyMyServerView",e)}},{key:"openDoodView",value:function(e){return this._callByApp("openDoodView",e)}},{key:"statusBarColor",value:function(e){return this._callByApp("statusBarColor",e)}},{key:"getAcRoles",value:function(e){return this._callByApp("getAcRoles",e)}},{key:"setStore",value:function(e){return this._callByApp("setStore",e)}},{key:"getStore",value:function(e){return this._callByApp("getStore",e)}},{key:"deleteStore",value:function(e){return this._callByApp("deleteStore",e)}},{key:"openShareView",value:function(e){return this._callByApp("openShareView",e)}},{key:"saveImagetoSysAlbum",value:function(e){return this._callByApp("saveImagetoSysAlbum",e)}},{key:"hasMethod",value:function(e){return this._callByApp("hasMethod",e)}},{key:"getUnreadCount",value:function(e){return this._callByApp("getUnreadCount",e)}},{key:"allowSlideBack",value:function(e){return this._callByApp("allowSlideBack",e)}},{key:"changeTab",value:function(e){return this._callByApp("changeTab",e)}},{key:"registerBoxManager",value:function(e){return this._callByApp("registerBoxManager",e)}},{key:"getLargeFileToLocal",value:function(e){var t;2==e.type?this._callByApp("getLargeFileToLocal",e):(t=Object.assign({},{url:"",method:"POST",exeQueueSize:5,isBreakpoint:!0,chunkRetry:!0,isVerification:!1,verificationUrl:"",verificationCallback:function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e.isFile},progress:function(){},success:function(){},error:function(){},sectionInfoSuccess:function(){}},e),this._callByApp("getLargeFileToLocal",{burstSize:t.burstSize,path:t.path,success:function(e){e.fileId&&(t.sectionInfoSuccess(e),new f(Object.assign({},t,{fileData:e})))}}))}},{key:"getLargeFileToLocalByPath",value:function(e){this._callByApp("getLargeFileToLocalByPath",e)}},{key:"getLargeFilePartToLocal",value:function(e){return this._callByApp("getLargeFilePartToLocal",e)}},{key:"setWatermarkEnable",value:function(e){return this._callByApp("setWatermarkEnable",e)}},{key:"setFuncAreaButtons",value:function(e){return this._callByApp("setFuncAreaButtons",e)}},{key:"openSystemPermissions",value:function(e){return this._callByApp("openSystemPermissions",e)}},{key:"getSystemPermissions",value:function(e){return this._callByApp("getSystemPermissions",e)}},{key:"reportException",value:function(e){return this._callByApp("reportException",e)}},{key:"audioRecord",value:function(e){return this._callByApp("audioRecord",e)}},{key:"toRegister",value:function(e){return this._callByApp("toRegister",e)}},{key:"showToast",value:function(e){return this._callByApp("showToast",e)}},{key:"showConfirm",value:function(e){return this._callByApp("showConfirm",e)}},{key:"showAlert",value:function(e){return this._callByApp("showAlert",e)}},{key:"showActionSheet",value:function(e){return this._callByApp("showActionSheet",e)}},{key:"pay",value:function(e){return this._callByApp("pay",e)}},{key:"setNavBar",value:function(e){return this._callByApp("setNavBar",e)}},{key:"setLeftNavBarItem",value:function(e){return this._callByApp("setLeftNavBarItem",e)}},{key:"setRightNavBarItem",value:function(e){return this._callByApp("setRightNavBarItem",e)}},{key:"checkAppVersion",value:function(e){return this._callByApp("checkAppVersion",e)}},{key:"excuteCMD",value:function(e){return this._callByApp("excuteCMD",e)}},{key:"openVideoMonitor",value:function(e){return this._callByApp("openVideoMonitor",e)}},{key:"updataNickName",value:function(e){return this._callByApp("updataNickName",e)}},{key:"updateHeadImage",value:function(e){return this._callByApp("updateHeadImage",e)}},{key:"showSetting",value:function(e){return this._callByApp("showSetting",e)}},{key:"startMeeting",value:function(e){return this._callByApp("startMeeting",e)}},{key:"startAudioRecord",value:function(e){return this._callByApp("startAudioRecord",e)}},{key:"stopAudioRecord",value:function(e){return this._callByApp("stopAudioRecord",e)}},{key:"cancelAudioRecord",value:function(e){return this._callByApp("cancelAudioRecord",e)}},{key:"saveBackupData",value:function(e){return this._callByApp("saveBackupData",e)}},{key:"getBackupData",value:function(e){return this._callByApp("getBackupData",e)}},{key:"openExternalUrlByNativeBrowser",value:function(e){return this._callByApp("openExternalUrlByNativeBrowser",e)}},{key:"openPosition",value:function(e){return this._callByApp("openPosition",e)}},{key:"updateGroupInfo",value:function(e){return this._callByApp("updateGroupInfo",e)}},{key:"logout",value:function(e){return this._callByApp("logout",e)}},{key:"takeVideo",value:function(e){return this._callByApp("takeVideo",e)}},{key:"getTabList",value:function(e){return this._callByApp("getTabList",e)}},{key:"changeTabByCode",value:function(e){return this._callByApp("changeTabByCode",e)}},{key:"updateTabCountByCode",value:function(e){return this._callByApp("updateTabCountByCode",e)}},{key:"launchNativeApp",value:function(e){return this._callByApp("launchNativeApp",e)}},{key:"getInstalledAppList",value:function(e){return this._callByApp("getInstalledAppList",e)}},{key:"selectPosition",value:function(e){return this._callByApp("selectPosition",e)}},{key:"setTabs",value:function(e){return this._callByApp("setTabs",e)}},{key:"getInviteQRCode",value:function(e){return this._callByApp("getInviteQRCode",e)}},{key:"getGroupMemberCount",value:function(e){return this._callByApp("getGroupMemberCount",e)}},{key:"setGroupForbidChatStatus",value:function(e){return this._callByApp("setGroupForbidChatStatus",e)}},{key:"getLoginType",value:function(e){return this._callByApp("getLoginType",e)}},{key:"login",value:function(e){return this._callByApp("login",e)}},{key:"getSelectedMenu",value:function(e){return this._callByApp("getSelectedMenu",e)}},{key:"getAppListByMenuCode",value:function(e){return this._callByApp("getAppListByMenuCode",e)}},{key:"isFriend",value:function(e){return this._callByApp("isFriend",e)}},{key:"createRoom",value:function(e){return this._callByApp("createRoom",e)}},{key:"joinRoom",value:function(e){return this._callByApp("joinRoom",e)}},{key:"exitRoom",value:function(e){return this._callByApp("exitRoom",e)}},{key:"deleteRoom",value:function(e){return this._callByApp("deleteRoom",e)}},{key:"joinMeeting",value:function(e){return this._callByApp("joinMeeting",e)}},{key:"getAdminRoleInfo",value:function(e){return this._callByApp("getAdminRoleInfo",e)}},{key:"showVerifyPassword",value:function(e){return this._callByApp("showVerifyPassword",e)}},{key:"getUserTags",value:function(e){return this._callByApp("getUserTags",e)}},{key:"launchBoxBluetooth",value:function(e){this._callByApp("launchBoxBluetooth",e)}},{key:"pickMeetingUsers",value:function(e){this._callByApp("pickMeetingUsers",e)}},{key:"getH5OpenChannel",value:function(e){this._callByApp("getH5OpenChannel",e)}},{key:"changeAppScreenOrientation",value:function(e){return this._callByApp("changeAppScreenOrientation",e)}},{key:"getAppInfoByPackageName",value:function(e){return this._callByApp("getAppInfoByPackageName",e)}},{key:"getInviteCodeInfo",value:function(e){return this._callByApp("getInviteCodeInfo",e)}},{key:"minimizeApp",value:function(e){return this._callByApp("minimizeApp",e)}},{key:"faceRecognition",value:function(e){return this._callByApp("faceRecognition",e)}},{key:"refreshTabByCode",value:function(e){return this._callByApp("refreshTabByCode",e)}},{key:"updateMeeting",value:function(e){return this._callByApp("updateMeeting",e)}},{key:"revokeMeeting",value:function(e){return this._callByApp("revokeMeeting",e)}},{key:"getUnreadCountById",value:function(e){return this._callByApp("getUnreadCountById",e)}},{key:"selectFolder",value:function(e){return this._callByApp("selectFolder",e)}},{key:"allowScreenshot",value:function(e){return this._callByApp("allowScreenshot",e)}},{key:"dismissMeeting",value:function(e){return this._callByApp("dismissMeeting",e)}},{key:"getMeetingInfo",value:function(e){return this._callByApp("getMeetingInfo",e)}},{key:"floatApp",value:function(e){return this._callByApp("floatApp",e)}},{key:"floatMeeting",value:function(e){return this._callByApp("floatMeeting",e)}},{key:"getAppH5Info",value:function(e){return this._callByApp("getAppH5Info",e)}},{key:"downloadFileForH5",value:function(e){return this._callByApp("downloadFileForH5",e)}},{key:"setMeeting",value:function(e){return this._callByApp("setMeeting",e)}},{key:"installEmoticon",value:function(e){return this._callByApp("installEmoticon",e)}},{key:"getInstalledEmoticonList",value:function(e){return this._callByApp("getInstalledEmoticonList",e)}},{key:"getEnabledPermissions",value:function(e){return this._callByApp("getEnabledPermissions",e)}},{key:"getPermissionSwitch",value:function(e){return this._callByApp("getPermissionSwitch",e)}},{key:"setPermissionSwitch",value:function(e){return this._callByApp("setPermissionSwitch",e)}},{key:"getBluetoothList",value:function(e){return this._callByApp("getBluetoothList",e)}},{key:"getBoxWifiList",value:function(e){return this._callByApp("getBoxWifiList",e)}},{key:"setBoxWifi",value:function(e){return this._callByApp("setBoxWifi",e)}},{key:"setBluetooth",value:function(e){return this._callByApp("setBluetooth",e)}},{key:"clearBoxBluetoothCache",value:function(e){return this._callByApp("clearBoxBluetoothCache",e)}}])}(function(e){function t(){return o()(this,t),e=this,n=t,r=arguments,n=u()(n),s()(e,C()?Reflect.construct(n,r||[],u()(e).constructor):n.apply(e,r));var e,n,r}return a()(t,e),i()(t,[{key:"launchMiniApp",value:function(e){return this._callByApp("launchMiniApp",e)}},{key:"getMiniAppSavePath",value:function(e){return this._callByApp("getMiniAppSavePath",e)}},{key:"openMiniAppPage",value:function(e){return this._callByApp("openMiniAppPage",e)}},{key:"checkMiniAppUpdate",value:function(e){return this._callByApp("checkMiniAppUpdate",e)}},{key:"checkMiniAppDownloadProgress",value:function(e){return this._callByApp("checkMiniAppDownloadProgress",e)}}])}(function(e){function t(){return o()(this,t),e=this,n=t,r=arguments,n=u()(n),s()(e,M()?Reflect.construct(n,r||[],u()(e).constructor):n.apply(e,r));var e,n,r}return a()(t,e),i()(t,[{key:"request",value:function(e){var t;return e.url?(e.header||(e.header["Content-Type"]="application/json"),e.timeout||(e.timeout=15e3),this._callByApp("request",e)):(t={resultCode:1001},e.success&&e.success(t),Promise.resolve(t))}},{key:"post",value:function(e){return e.method="post",this._callByApp("request",e)}},{key:"put",value:function(e){return e.method="put",this._callByApp("request",e)}},{key:"get",value:function(e){return e.method="get",e.data=e.data||e.params,this._callByApp("request",e)}},{key:"delete",value:function(e){return e.method="delete",this._callByApp("request",e)}},{key:"patch",value:function(e){return e.method="patch",this._callByApp("request",e)}},{key:"requestStream",value:function(e){var t;return e.url?(e.header||(e.header["Content-Type"]="application/json"),e.timeout||(e.timeout=15e3),this._callByApp("requestStream",e)):(t={resultCode:1001},e.success&&e.success(t),Promise.resolve(t))}},{key:"registerRequestStreamNotice",value:function(e){return this._callByApp("registerRequestStreamNotice",e)}},{key:"abortRequest",value:function(e){return this._callByApp("abortRequest",e)}}])}(function(e){function t(e){var n,r;return o()(this,t),n=this,r=t,e=[e],r=u()(r),(r=s()(n,j()?Reflect.construct(r,e||[],u()(n).constructor):r.apply(n,e))).configEmitter=new v.a,r}return a()(t,e),i()(t,[{key:"getSignatureInfo",value:function(e,t){var n,r;return e&&t?(n=(new Date).getTime()+"",r=Object.assign({},{jsapi_ticket:e,nonceStr:t,timeStamp:n,url:location.href.split("#")[0]}),e=Object.keys(r).sort().map((function(e){return"".concat(e.toLowerCase(),"=").concat(r[e])})).join("&"),{resultCode:0,resultData:{nonceStr:t,timeStamp:n,signature:L(e)}}):{resultCode:-1}}},{key:"getSignatureInfoTest",value:function(e,t,n){var r,o;return e&&t?(r=Object.assign({},{jsapi_ticket:e,nonceStr:t,timeStamp:n,url:location.href.split("#")[0]}),e=Object.keys(r).sort().map((function(e){return"".concat(e.toLowerCase(),"=").concat(r[e])})).join("&"),o=L(e),console.log("signature::::",e,o),{resultCode:0,resultData:{nonceStr:t,timeStamp:n,signature:o}}):{resultCode:-1}}},{key:"config",value:function(e){var t=this;return"boolean"==typeof e.debug&&(this.isDebug=e.debug),setTimeout((function(){t.configEmitter.emit("configError",-101),t.configEmitter.removeAllListeners("configSuccess")}),1e4),this._callByApp("config",e).then((function(e){return t.configEmitter.emit("configSuccess"),t.configEmitter.removeAllListeners("configError"),Promise.resolve(e)})).catch((function(e){return t.configEmitter.emit("configError",e),t.configEmitter.removeAllListeners("configSuccess"),Promise.reject(e)}))}},{key:"success",value:function(e){this.configEmitter.once("configSuccess",(function(){e()}))}},{key:"error",value:function(e){this.configEmitter.once("configError",(function(t){e(t)}))}}])}(i()((function e(){o()(this,e),this.isDebug=!1,this.timeout=4e3,this.useOpenId=!1,this.newResult=!1,this.appId="",this.deviceType=0,this.deviceName="",this.sdkVersion=n(21).version,this.client=null,this.clientEmitter=new v.a,this.emitterDoneKey=w,this.initVersionStatus="default",this.initVersionOpenIdStatus=A.DEFAULT;var t=navigator.userAgent.toLowerCase();t.includes("imlinkdood")&&!t.includes("pc_imlinkdood")?(this.initMobile(),t.includes("android")?(this.deviceType=1,this.deviceName="android"):(this.deviceType=2,this.deviceName="ios")):t.includes("electron")&&(this.initPC(),this.deviceType=5,this.deviceName="pc-h5")}),[{key:"init",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};this.isDebug=e.debug||!1,this.timeout=Number.isInteger(e.timeout)?e.timeout:4e3,this.newResult=e.newResult||!1,this.appId=e.appId||"",this.useOpenId=e.useOpenId||!1,e.useOpenId&&(this.emitterDoneKey=x),this._initJSSDKVersion()}},{key:"_initJSSDKVersion",value:function(){function e(){var e={version:t.sdkVersion,useAuth:"0",useOpenId:t.useOpenId||!1},n=setTimeout((function(){t.initVersionStatus=B,t.clientEmitter.emit(x)}),2e3);t.initVersionStatus=b,t._callByApp("initJSSDKVersion",e).then((function(e){t.initVersionOpenIdStatus=1==e.resultCode?A.NOT_SUPPORT:A.SUPPORT})).catch((function(){t.initVersionOpenIdStatus=A.NOT_SUPPORT})).finally((function(){clearTimeout(n),t.initVersionStatus!=B&&(t.initVersionStatus=B,t.clientEmitter.emit(x))}))}var t=this;null==this.client?this.clientEmitter.once(w,(function(){e()})):e()}},{key:"_isPreCompletion",value:function(){return!(!this.client||this.useOpenId&&this.initVersionStatus==b)}},{key:"ready",value:function(e){function t(){n.isDebug&&alert("jssdk initialization successful"),"function"==typeof e&&e.call(n)}var n=this;this._isPreCompletion()?t():this.clientEmitter.once(this.emitterDoneKey,(function(){t()}))}},{key:"initMobile",value:function(){var e,t=this,n=function(e){t.client=e,t.clientEmitter.emit(w)};window.WebViewJavascriptBridge?n(WebViewJavascriptBridge):window.WVJBCallbacks?window.WVJBCallbacks.push(n):(window.WVJBCallbacks=[n],(e=document.createElement("iframe")).style.display="none",e.src="wvjbscheme://__BRIDGE_LOADED__",document.documentElement.appendChild(e),setTimeout((function(){document.documentElement.removeChild(e)}),0))}},{key:"initPC",value:function(){var e;window.nodeRequire||(window.nodeRequire=window.require,delete window.require,delete window.exports,delete window.module),window.callByDDIOApp?this.client=window.callByDDIOApp:(e=window.nodeRequire("electron").remote.getGlobal("callByDDIOApp"),this.client=e),this.clientEmitter.emit(w)}},{key:"_setDefault",value:function(e,t){for(var n in(e=e||{}).constructor!==Object&&(e={}),t)n=t[n],d()(e[n.key])!=n.type&&(e[n.key]=n.val),(e[n.key]>n.max||e[n.key]<n.min)&&(e[n.key]=n.val);return e}},{key:"_callByApp",value:function(e){var t,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return n.success=n.success||function(){},null==e?(alert("jssdk method error"),n.success(t={resultCode:-100,resultMsg:"jssdk method error"}),Promise.reject(t)):this.useOpenId&&this.initVersionStatus==B&&[A.DEFAULT,A.NOT_SUPPORT].includes(this.initVersionOpenIdStatus)?(n.success(t={resultCode:-105,resultMsg:"客户端不支持返回openId，请升级客户端"}),Promise.reject(t)):this._callByAppJudgeFront(e,n)}},{key:"_callByAppJudgeFront",value:function(e,t){var n=this;return"initJSSDKVersion"==e||this._isPreCompletion()?this._callByAppRun(e,t):new Promise((function(r,o){var i=!0;n.clientEmitter.once(n.emitterDoneKey,(function(){var s;i&&(i=null,n.useOpenId&&n.initVersionStatus==B&&[A.DEFAULT,A.NOT_SUPPORT].includes(n.initVersionOpenIdStatus)?(t.success(s={resultCode:-105,resultMsg:"客户端不支持返回openId，请升级客户端"}),o(s)):r(n._callByAppRun(e,t)))})),setTimeout((function(){var e;i&&(i=null,t.success(e={resultCode:-100,resultMsg:"jssdk method timeout callByAppJudgeFront"}),o(e))}),n.timeout)}))}},{key:"_callByAppRun",value:function(e,t){var n=this,r={resultCode:-100,resultMsg:"jssdk method timeout callByAppRun"},o=t.success||function(){};return t.appId=this.appId,new Promise((function(i,s){switch(n.deviceType){case 1:case 2:var u=-1,a=!1,l=("hasMethod"===e&&(u=setTimeout((function(){a||(a=!0,o(r),s(r))}),1e3)),function(t,r){if(t=n._completionData(t),"hasMethod"===e){if(a)return;a=!0,clearTimeout(u)}n.isDebug&&alert(e+":"+JSON.stringify(t)),o(t),(!t.hasOwnProperty("resultCode")||0<=t.resultCode?i:s)(t)});_.includes(e)&&n.client.registerHandler(e,l),k.includes(e)||n.client.callHandler(e,t,l);break;case 5:n.client[e]?(l=Object.assign({},t,{success:function(){var r=n._completionData(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{});n.isDebug&&alert(e+":"+JSON.stringify(r)),t.success(r),(!r.hasOwnProperty("resultCode")||0<=r.resultCode?i:s)(r)}}),n.client[e](l)):(o(r),s(r))}}))}},{key:"_completionMsg",value:function(e){return Object.assign({},{resultMsg:""},e)}},{key:"_completionData",value:function(e){var t;return!0!==this.newResult?e:"object"==d()(e)&&/^{.*}$/.test(JSON.stringify(e))?e.hasOwnProperty("resultCode")&&e.hasOwnProperty("resultData")?this._completionMsg(e):e.hasOwnProperty("resultCode")&&!e.hasOwnProperty("resultData")?(delete(t=m({},e)).resultCode,this._completionMsg({resultCode:e.resultCode,resultData:0==Object.keys(t).length?void 0:t})):!e.hasOwnProperty("resultCode")&&e.hasOwnProperty("resultData")?this._completionMsg(m({resultCode:0},e)):this._completionMsg({resultCode:0,resultData:e}):this._completionMsg({resultCode:0,resultData:e})}}]))))))).jssdk=r,console.log("envType:",""),console.log("useAuth:","0"),console.log("sdkVersion:",r.sdkVersion),t.default=r}],n={},e.m=t,e.c=n,e.d=function(t,n,r){e.o(t,n)||Object.defineProperty(t,n,{enumerable:!0,get:r})},e.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},e.t=function(t,n){if(1&n&&(t=e(t)),8&n)return t;if(4&n&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(e.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&n&&"string"!=typeof t)for(var o in t)e.d(r,o,function(e){return t[e]}.bind(null,o));return r},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},e.p="./",e(e.s=22).default;function e(r){var o;return(n[r]||(o=n[r]={i:r,l:!1,exports:{}},t[r].call(o.exports,o,o.exports,e),o.l=!0,o)).exports}var t,n}));