<template>
  <u-upload
    :fileList="fileList"
    @afterRead="afterRead"
    @delete="deletePic"
    name="1"
    :width="width"
    :height="height"
    :maxCount="maxCount"
  >
    <slot></slot>
  </u-upload>
</template>

<script>
import { ossUpload } from "@/js_sdk/jason-alioss-upload/oss.js";
export default {
  props: {
    value: {
      type: [Array, String],
      default: () => [],
    },
    maxCount: {
      type: Number,
      default: 52,
    },
    width: {
      type: String,
      default:80,
    },
    height: {
      type: String,
      default: 80,
    },
  },
  data() {
    return {
      fileList: [],
    };
  },
  watch: {
    value: {
      handler(newValue) {
        this.fileList = this.maxCount === 1 ? [{ url: newValue }] : newValue;
        if (this.maxCount === 1) {
          if (newValue) {
            this.fileList = [{ url: newValue }];
          } else {
            this.fileList = [];
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    updateValue() {
      let value = this.fileList;
      if (this.maxCount === 1) {
        value = value[0]?.url || "";
      }
      this.$emit("input", value);
    },
    // 删除图片
    deletePic(event) {
      console.log("deletePic", event);
      this.fileList.splice(event.index, 1);
      this.updateValue();
    },
    // 新增图片
    async afterRead(event) {
      console.log("event", event);
      const result = await this.uploadFilePromise(event.file.url);
      this.fileList.push({
        status: "success",
        message: "",
        url: result,
      });
      this.updateValue();
    },
    uploadFilePromise(url) {
      return new Promise((resolve, reject) => {
        ossUpload(url, url, "wx/").then((res) => {
          const { data, success } = res;
          if (success) {
            console.log("dddd1", data);
            resolve(data);
          }
        });
      });
    },
  },
};
</script>
