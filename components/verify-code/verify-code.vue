<template>
	<u-input :value="value" border="none" :placeholder="placeholder" @change="valueChange">
		<template slot="suffix">
			<u-code ref="uCode" @change="codeChange" seconds="60" changeText="X秒重新获取"></u-code>
			<u-button @tap="getCode" :text="tips" color="#117ACD" size="mini"></u-button>
		</template>
	</u-input>
</template>

<script>
	import {
		getCode
	} from '@/api/common.js';
	export default {
		props: {
			value: {
				type: String
			},
			placeholder: {
				type: String,
				default: '请输入验证码'
			},
			phone: {
				type: String
			},
		},
		data() {
			return {
				tips: '',
			}
		},
		methods: {
			valueChange(value) {
				this.$emit('input', value);
			},
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				let {
					phone
				} = this;
				if (!phone) {
					uni.$u.toast('请输入手机号');
					return;
				}
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					getCode({
						phone
					}).then(res => {
						console.log('res', res);
						uni.hideLoading();
						if (res.code === 200) {
							// 这里此提示会被this.start()方法中的提示覆盖
							uni.$u.toast('验证码已发送');
							// 通知验证码组件内部开始倒计时
							this.$refs.uCode.start();
						} else {
							uni.$u.toast(res.msg || '验证码发送失败');
						}

					})

				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
		},
	}
</script>

<style lang="scss" scoped>

</style>