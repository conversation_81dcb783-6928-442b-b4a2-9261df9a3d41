<template>
  <block>
    <u-input
      :value="inputText"
      readonly
      :border="border"
      inputAlign="right"
      :placeholder="placeholder"
      :customStyle="inputStyle"
      @click.native="handleOpen"
    >
      <template  slot="suffix">
        <u-icon v-if="!readonly" color="#C5C6C6" name="arrow-right" size="19"></u-icon>
      </template>
    </u-input>
    <u-picker
      :show="show"
      :columns="columns"
      keyName="label"
      @cancel="close"
      @close="close"
      @confirm="confirm"
    ></u-picker>
  </block>
</template>

<script>
export default {
  props: {
    border: {
      type: String,
      default: "none",
    },
    inputStyle: {
      type: Object,
      default: () => ({}),
    },
    value: {
      type: [String, Number],
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    url: {
      type: String,
    },
    valueName: {
      type: String,
      default: "id",
    },
    labelName: {
      type: String,
      default: "name",
    },
    dataPath: {
      type: String,
      default: "rows",
    },
    options: {
      type: Array,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      show: false,
      columns: [],
    };
  },
  computed: {
    inputText() {
      let list = this.columns[0] || [];
      let row = list.find((item) => item.id === this.value);
      console.log("inputText", row);
      return row ? row.label : "";
    },
  },
  watch: {
    options: {
      immediate: true,
      handler() {
        let { options } = this;
        console.log("初始监听 options", options);
        if (!this.url) {
          this.columns = [
            options.map((item) => ({
              ...item,
              id: item[this.valueName],
              label: item[this.labelName],
            })),
          ];
        }
      },
    },
  },
  mounted() {
    if (this.url) {
      this.initData();
    }
  },
  methods: {
    close() {
      this.show = false;
    },
    handleOpen() {
      if (this.readonly) {
        return;
      }
      this.show = true;
    },
    confirm(val) {
      let row = val.value[0] || {};
      console.log("确认选择", row);
      this.$emit("input", row.id);
      this.$emit("change", row);
      this.show = false;
    },
    getDataPath(res) {
      let pathArr = this.dataPath.split(".");
      return pathArr.reduce((prev, next) => {
        return prev[next] || [];
      }, res);
    },
    initData() {
      uni.$u.http
        .get(this.url, {
          params: {},
        })
        .then((res) => {
          if (res.code === 200) {
            let list = this.getDataPath(res);
            let formartList = list.map((item) => ({
              ...item,
              id: item[this.valueName],
              label: item[this.labelName],
            }));
            this.columns = [formartList];
          }
        });
    },
  },
};
</script>

<style lang="scss" scoped></style>
