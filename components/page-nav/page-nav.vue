<template>
  <view>
    <u-navbar
      :placeholder="placeholder"
      :bgColor="bgColor"
      :titleStyle="titleStyle"
      :title="title"
    >
      <view slot="left">
        <u-icon
          v-if="isLeft"
          slot="left"
          :color="leftIconColor"
          name="arrow-left"
          size="19"
          @click="handleLeft"
        ></u-icon>
      </view>
    </u-navbar>
    <u-line v-if="border"></u-line>
  </view>
</template>

<script>
import { getCurrentRoute } from "@/util/tools.js";
export default {
  props: {
    desc: String,
    title: String,
    url: String,
    border: {
      type: Boolean,
      default: true,
    },
    bgColor: {
      type: String,
      default: "#fff",
    },
    isLeft: {
      type: Boolean,
      default: true,
    },
    leftIconColor: {
      type: String,
    },
    navigatorType: {
      type: String,
      default: "navigateTo",
    },
    titleStyle: {
      type: Object,
    },
    placeholder: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {};
  },
  methods: {
    getParams() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1] || {};
      let params = currentPage.options;
      console.log("params", params);
      return params;
    },
    handleLeft() {
      let { sourcePage } = this.getParams();
      this.$emit("back");
      uni.$u.route({
        url: sourcePage || this.url,
        type: this.url ? this.navigatorType : "navigateBack",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-border-bottom {
  border-color: #f1f1f1 !important;
}
</style>
