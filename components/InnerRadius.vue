<template>
  <view>
    <canvas :canvas-id="canvasId" :style="{ width: triangleWidth + 'px', height: triangleHeight + 'px' }"></canvas>
  </view>
</template>

<script>
export default {
  props: {
    direction: {
      type: String,
      default: 'right'
    },
    fillColor: {
      type: String,
      default: 'white'
    },
    radius: {
      type: Number,
      default: 20
    },
    triangleWidth: {
      type: Number,
      default: 100
    },
    triangleHeight: {
      type: Number,
      default: 100
    },
    canvasId: {
      type: String,
      required: true
    }
  },
  mounted() {
    this.drawCanvas();
  },
  // updated() {
  //   this.drawCanvas();
  // },
  beforeDestroy() {
    const ctx = uni.createCanvasContext(this.canvasId, this);
    ctx.clearRect(0, 0, this.triangleWidth * 2, this.triangleHeight * 2);
    ctx.draw(true);
  },
  methods: {
    drawCanvas() {
      const ctx = uni.createCanvasContext(this.canvasId, this);
      
      ctx.clearRect(0, 0, this.triangleWidth * 2, this.triangleHeight * 2);
      
      ctx.beginPath();
      if (this.direction === 'right') {
        ctx.moveTo(0, 0);
        ctx.lineTo(0, this.triangleHeight);
        ctx.lineTo(this.triangleWidth, this.triangleHeight);
        ctx.arcTo(0, this.triangleHeight, 0, 0, this.radius);
      } else if (this.direction === 'left') {
        ctx.moveTo(this.triangleWidth, 0);
        ctx.lineTo(this.triangleWidth, this.triangleHeight);
        ctx.lineTo(0, this.triangleHeight);
        ctx.arcTo(this.triangleWidth, this.triangleHeight, this.triangleWidth, 0, this.radius);
      } else if (this.direction === 'bottom') {
        ctx.moveTo(0, 0);
        ctx.lineTo(this.triangleWidth, 0);
        ctx.lineTo(this.triangleWidth, this.triangleHeight);
        ctx.arcTo(this.triangleWidth, 0, 0, 0, this.radius);
      }
      ctx.closePath();
      ctx.setFillStyle(this.fillColor);
      ctx.fill();
      ctx.draw(false);
    }
  }
}
</script>