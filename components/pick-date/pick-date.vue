<template>
  <block>
    <u-input
      :value="value"
      readonly
      border="none"
      :inputAlign="inputAlign"
      :placeholder="placeholder"
      @click.native="handleOpen"
    >
      <template slot="suffix">
        <slot name="suffix">
          <u-icon v-if="!readonly" color="#C5C6C6" name="arrow-right" size="19"></u-icon>
        </slot>
      </template>
    </u-input>
    <u-datetime-picker
      v-if="show"
      :maxDate="maxDate"
      :minDate="minDate"
      :immediateChange="true"
      ref="datetimePicker"
      :show="true"
      v-model="time"
      :mode="mode"
      @confirm="dateChange"
      @cancel="onClose"
    ></u-datetime-picker>
  </block>
</template>

<script>
export default {
  props: {
    value: {
      type: String,
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    maxDate: {
      type: Number,
      default: Number(new Date()) + 100 * 365 * 24 * 60 * 60 * 1000,
    },
    minDate: {
      type: Number,
      default: Number(new Date()) - 100 * 365 * 24 * 60 * 60 * 1000,
    },
    mode: {
      type: String,
      default: "date",
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    inputAlign: {
      type: String,
      default: "right",
    },
  },
  data() {
    return {
      show: false,
      time: this.mode === "time" ? null : new Date(),
    };
  },
  methods: {
    onClose() {
      this.show = false;
    },
    handleOpen() {
      if (this.readonly) {
        return;
      }
      this.time =
        this.mode === "time"
          ? this.value
          : this.value
          ? Number(new Date(this.value))
          : Number(new Date());
      this.show = true;
    },
    dateChange({ value }) {
      let formatValue =
        this.mode === "time"
          ? value
          : value
          ? uni.$u.timeFormat(value, "yyyy-mm-dd")
          : "";

      this.show = false;
      this.$emit("input", formatValue);
      this.$emit("change", formatValue);
    },
  },
};
</script>

<style lang="scss" scoped></style>
