<template>
	<view v-if="label" class="prd_label">
		{{label}}
	</view>
</template>

<script>
	export default {
		props: {
			label: {
				type: [String, Number],
			}
		},
	}
</script>

<style lang="scss" scoped>
	.prd_label {
		position: absolute;
		top: 5rpx;
		right: 5rpx;
		z-index: 10;
		border: 1rpx solid #ffdc67;
		background-color: #fff3cc;
		color: #ffc724;
		border-radius: 6rpx 6rpx 6rpx 6rpx;
		font-weight: 800;
		font-size: 20rpx;
		padding: 5rpx 8rpx;
		overflow: hidden;
	}
</style>