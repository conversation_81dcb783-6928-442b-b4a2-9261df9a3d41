<template>
	<u-modal :show="visible" showCancelButton width="560rpx" ref="uModal" title="确认收货" :asyncClose="true"
		content="您确认收货吗？" confirmColor="#3579B4" @confirm="handleConfirm" @cancel="visible = false"></u-modal>
</template>

<script>
	import {
		confirmReceive
	} from '@/api/order.js';
	export default {
		data() {
			return {
				visible: false,
				orderId: ''
			}
		},

		methods: {
			open(orderId) {
				this.orderId = orderId
				this.visible = true;
			},
			handleConfirm() {
				confirmReceive(this.orderId).then(res => {
					this.visible = false
					if (res.code === 200) {
						uni.showToast({
							title: '操作成功',
							duration: 2000
						});
						this.$emit('onSuccess')
					} else {
						uni.showToast({
							title: res.msg || '操作失败',
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(() => {
					uni.showToast({
						title: '操作失败',
						icon: 'none',
						duration: 2000
					});
					this.visible = false
				})
			}
		},
	}
</script>

<style lang="scss" scoped>

</style>