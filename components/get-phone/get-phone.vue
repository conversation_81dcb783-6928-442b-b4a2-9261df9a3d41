<template>
	<view :style="[customStyle]" class="wrap">
		<slot></slot>
		<u-button v-if="!userInfo.phone" :loading="loading" open-type="getPhoneNumber"
			:customStyle="{width:'100%',height:'100%',opacity:0,position:'absolute',left:0,top:0,zIndex:100}"
			@getphonenumber="getPhoneNumber"></u-button>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import {
		dPhone,
	} from '@/common/home.js'
	export default {
		props:{
			customStyle:{
				type:Object,
				default(){
					return {}
				}
			}
		},
		options: {
			virtualHost: true
		},
		data() {
			return {
				loading: false
			}
		},
		computed: {
			...mapState({
				userInfo: state => state.User.userInfo,
				sessionKey: state => state.User.sessionKey,
			})
		},
		methods: {
			...mapActions(['getUserInfo']),
			getPhoneNumber(item) {
				const {
					code,
					iv,
					encryptedData
				} = item.detail;
				// 获取手机号

				const params = {
					sessionKey: this.sessionKey,
					encryptedData,
					iv,
				}
				this.loading = true;
				dPhone(params).then(async res => {
					this.loading = false;
					if (res.data) {
						await this.getUserInfo();
						this.$emit('onSuccess')
					}
				})

			},
		},
	}
</script>

<style>
	.wrap {
		width: 100%;
		height: 100%;
		position: relative;
	}
</style>