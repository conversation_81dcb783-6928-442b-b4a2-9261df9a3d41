<template>
	<view class="tab_content">
		<u-tabs v-if="list&&list.length>0" :current="current" :list="tabList" lineColor="#127BCD" lineHeight="4rpx"
			lineWidth="60rpx" :activeStyle="{color:' #127BCD',fontWeight:'600'}"
			:itemStyle="{padding:'23rpx 40rpx',fontWeight:'400'}"  @change="handleChange">
		</u-tabs>
	</view>
</template>

<script>
	export default {
		props: {
			value: {
				type: [String, Number],
			},
			list: {
				type: Array,
				default () {
					return []
				}
			}
		},
		data() {
			return {}
		},
		computed: {
			current() {
				let index = this.list.findIndex(item => item.value === this.value)
				return index;
			},
			tabList() {
				return this.list.map(item => ({
					name: item.label,
					value: item.value
				}))
			}
		},
		methods: {
			handleChange(row) {
				this.$emit('input', row?.value || '')
				this.$emit('onChange', row?.value || '')
			}
		},
	}
</script>

<style lang="scss" scoped>
.tab_content{
	background-color: #fff;
	padding:0 40rpx;
}
</style>