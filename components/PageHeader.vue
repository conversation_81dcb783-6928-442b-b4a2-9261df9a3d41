<template>
  <view class="fixed-header">
    <view class="header">
      <view class="header-left" v-if="showBack" @click="handleLeft">
        <view class="header-left-back">
          <u-icon name="arrow-left" size="20" color="#303133" />
        </view>
      </view>
      <view class="title">{{ title }}</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PageHeader',
  props: {
    title: {
      type: String,
      required: true
    },
    showBack: {
      type: Boolean,
      default: false
    },
    navigatorType: {
      type: String,
      default: 'navigateBack'  // navigateBack, navigateTo, switchTab, reLaunch, redirectTo
    },
    url: {
      type: String,
      default: ''
    }
  },
  methods: {
    getParams() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1] || {};
      let params = currentPage.options;
      console.log("params", params);
      return params;
    },
    handleLeft() {
      let { sourcePage } = this.getParams();
      this.$emit("back");
      uni.$u.route({
        url: sourcePage || this.url,
        type: this.url ? this.navigatorType : "navigateBack",
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  
  .header {
    display: flex;
    align-items: center;
    padding: 30rpx;
    padding-top: 100rpx;
    
    .header-left {
      position: absolute;
      left: 30rpx;
      
      .header-left-back {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    
    .title {
      flex: 1;
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      text-align: center;
    }
  }
}
</style> 