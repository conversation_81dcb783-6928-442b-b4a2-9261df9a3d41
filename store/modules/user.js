import {
	USERINFO_NAME,
	setStore,
	getStore
} from '@/util/tools.js';
import {
	login,
	getUserById
} from '@/common/home';
const state = {
	userInfo: getStore(USERINFO_NAME) || {},
	sessionKey: getStore('sessionKey')
}
const mutations = {
	SET_USERINFO(state, userInfo = {}) {
		state.userInfo = userInfo;
		setStore(USERINFO_NAME, userInfo);
	},
	SET_SESSIONKEY(state, sessionKey) {
		state.sessionKey = sessionKey;
		setStore('sessionKey', sessionKey);
	}
}

const actions = {
	getUserInfo({
		state,
		commit
	}, id) {
		let userId = id || state.userInfo.id
		if (!userId) {
			return
		}
		return getUserById(userId).then(res => {
			commit('SET_USERINFO', res.data || {});
			return res;
		})
	},
	wxLogin({
		commit,
		dispatch
	}) {
		return new Promise((resolve) => {
			uni.login({
				provider: 'weixin', //使用微信登录
				success: (loginRes) => {
					login({
						code: loginRes.code
					}).then(res => {
						let {
							data
						} = res;
						commit('SET_SESSIONKEY', data.sessionKey);
						dispatch('getUserInfo', data.id).then((userRes) => {
							console.log('userRes',userRes);
							resolve(userRes)
						});

					})

				}
			})
		})
	},
	login({
		dispatch
	}) {
		return new Promise((resolve, reject) => {
			const {
				openId
			} = getStore(USERINFO_NAME)
			if (!openId) {
				dispatch('wxLogin').then(res => {
					resolve(res)
				})
			}
			uni.checkSession({
				fail: () => {
					dispatch('wxLogin').then(res => {
						resolve(res)
					})
				}
			})
		})

	}
}


export default {
	state,
	mutations,
	actions
}