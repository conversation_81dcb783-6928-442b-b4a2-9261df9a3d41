<template>
  <view class="distribute-center">
    <!-- 顶部栏 -->
    <page-header title="分销商信息" :showBack="true" navigatorType="switchTab" url="pages/user/index"></page-header>
    <!-- <page-nav title="分销商信息" navigatorType="switchTab" url="pages/user/index" :border="false"></page-nav> -->

    <!-- 用户信息卡片 -->
    <view class="user-card">
      <image :src="userInfo.avater" class="avatar" />
      <view class="user-info">
        <view class="nickname">
          {{ distributeInfo.realName }}
          <!-- <u-icon name="edit-pen" size="18" @click="editNickname" /> -->
        </view>
        <view class="user-id">分销员ID {{ distributeInfo.id }}</view>
      </view>
    </view>

    <!-- 金额信息卡片 -->
    <view class="amount-card">
      <view class="fxka-bg">
        <image class="icon-image" src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/fxka-bg.png"
          mode="aspectFit"></image>
      </view>
      <!-- <view class="amount-row first">
        <view class="amount-item">
          <view class="label">可提现(元)</view>
          <view class="amount">{{ distributeInfo.estimatedBalance }}</view>
          
        </view>
      </view> -->
      <view class="amount-row-container">
        <view class="amount-row second">
          <view class="amount-item">
            <view class="label">待入账(元)</view>
            <view class="amount">{{ distributeInfo.estimatedBalance }}</view>

          </view>
          <view class="amount-item">
            <view class="label">可提现(元)</view>
            <view class="amount">{{ distributeInfo.withdrawalAmount }}</view>
          </view>
          <view class="amount-item">
            <view class="label">已提现(元)</view>
            <view class="amount">{{ distributeInfo.completeAmount }}</view>
          </view>
        </view>
        <view class="amount-actions">
          <u-button class="record-btn" :plain="true" @click="goWithdrawRecord">提现记录</u-button>
          <u-button type="primary" class="withdraw-btn" @click="goWithdraw">立即提现</u-button>
        </view>
      </view>
    </view>

    <!-- 统计卡片 -->
    <view class="stat-card">
      <view class="stat-item">
        <image src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/fxqb.png" class="stat-icon-yj" />
        <view>
          <view class="stat-value">{{ distributeInfo.accruingAmounts }}</view>
          <view class="stat-label">累计佣金(元)</view>
        </view>
      </view>
      <view class="stat-item">
        <image src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/fxrs.png" class="stat-icon-rs" />
        <view>
          <view class="stat-value">{{ distributeInfo.bindingCount }}</view>
          <view class="stat-label">下线总数(人)</view>
        </view>
      </view>
    </view>

    <!-- 分销订单 -->
    <view class="order-card" @click="goAllOrders">
      <view class="order-info">
        <view class="order-info-item">
          <image src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/fxdd.png" class="order-icon" />
          <view>
            <view class="order-info-item-title">分销订单</view>
            <!-- <view class="order-info-item-count">{{ stat.orderCount || 10 }}笔</view> -->
          </view>
        </view>
      </view>
      <view class="order-all">
        <view class="order-all-title">全部订单</view>
        <u-icon name="arrow-right" size="13" />
      </view>
    </view>
  </view>
</template>

<script>
import {
  getDistributeCenter
} from '@/api/user.js';
import PageHeader from "@/components/PageHeader.vue";
import {
  mapState,
  mapActions
} from 'vuex';
export default {
  components: {
    PageHeader
  },
  data() {
    return {
      distributeInfo: {},
      amount: {
        available: 905.99,
        pending: 399.59,
        processing: 266.8,
        withdrawn: 9989.56
      },
      stat: {
        totalCommission: 19200.50,
        totalDownline: 205,
        orderCount: 10
      }
    }
  },
  onShow() {
    this.initData();
  },
  computed: {
    ...mapState({
      userInfo: (state) => {
        console.log('state.User', state.User);
        return state.User.userInfo
      },
    }),
  },
  methods: {
    initData() {
      console.log('this.userInfo', this.userInfo);
      getDistributeCenter(this.userInfo.distributor.id).then(res => {
        console.log('res', res);
        this.distributeInfo = res.data || {}
      })
    },
    editNickname() {
      // 显示编辑弹窗
    },
    goWithdrawRecord() {
      uni.navigateTo({ url: '/pages/withdrawRecord/index' });
    },
    goWithdraw() {
      uni.navigateTo({ url: '/pages/withdraw/index' });
    },
    goAllOrders() {
      uni.navigateTo({ url: '/pages/distributeRecord/index' });
    }
  }
}
</script>

<style lang="scss" scoped>
/* 参考你的截图进行样式编写，重点是圆角、阴影、渐变背景、flex布局等 */
.distribute-center {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(to bottom,
      #e3c997,
      #ebd7b1,
      #f7edda,
      #fbf5ea,
      #fbf6ed,
      #faf7f5);

  .fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
  }

  .header {
    padding: 30rpx;
    padding-top: 100rpx;
    padding-bottom: 20rpx;
    position: relative;

    .title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      text-align: center;
    }

    .header-left-back {
      position: absolute;
      left: 40rpx;
      top: 100rpx;

    }
  }

  /* 用户信息卡片样式 */
  .user-card {
    display: flex;
    align-items: center;
    padding: 200rpx 40rpx 0 40rpx;

    .avatar {
      width: 132rpx;
      height: 132rpx;
      border-radius: 50%;
    }

    .user-info {
      margin-left: 33rpx;
      flex: 1;

      .nickname {
        display: flex;
        align-items: center;
        font-size: 32rpx;
        font-weight: 500;
        color: #222;

        .u-icon {
          margin-left: 16rpx;
          color: #999999;
        }
      }

      .user-id {
        font-size: 24rpx;
        color: #595757;
        margin-top: 6rpx;
      }
    }
  }

  /* 金额信息卡片样式 */
  .amount-card {
    background: linear-gradient(to bottom,
        #EABE99,
        #EEC7A4,
        #F3D0B1,
        #F8D9BC,
        #FBDFC4,
        #FEE6CC);
    border-radius: 15rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    padding: 30rpx;
    margin: 40rpx;
    position: relative;
    height: 360rpx;
    overflow: hidden;

    .fxka-bg {
      position: absolute;
      top: 0;
      right: -40rpx;
      width: 340rpx;
      height: 360rpx;

      .icon-image {
        width: 100%;
        height: 100%;
      }
    }

    .amount-row-container {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
    }

    .amount-row {
      display: flex;
      margin-bottom: 30rpx;

      &.first {
        justify-content: start;
      }

      &.second {
        justify-content: space-between;
      }

      .amount-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        // flex: 1;

        .amount {
          font-size: 36rpx;
          font-weight: 600;
          color: #835D31;

        }

        .label {
          font-size: 26rpx;
          color: #FFF;
          margin-bottom: 8rpx;
        }
      }
    }

    .amount-actions {
      display: flex;
      justify-content: space-between;

      .record-btn,
      .withdraw-btn {
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 40rpx;
        font-size: 27rpx;
        padding: 0;
      }

      .record-btn {
        margin-right: 20rpx;
        color: #886338;
        background-color: transparent;
        border: 1px solid #886338;
      }

      .withdraw-btn {
        margin-left: 20rpx;
        background: linear-gradient(90deg, #835C31, #835C31);
        color: #ffffff;
        border: none;
      }
    }
  }

  /* 统计卡片样式 */
  .stat-card {
    display: flex;
    justify-content: space-between;
    margin: 40rpx;
    margin-top: 0;

    .stat-item {
      width: 48%;
      background-color: #ffffff;
      display: flex;
      align-items: center;
      /* 去掉默认背景、阴影等，更贴近参考图的简洁白底 + 外间距 */
      padding: 30rpx 0;
      /* 文字和图标整体居中 */
      align-items: center;
      border-radius: 15rpx;

      .stat-icon-yj {
        width: 115rpx;
        height: 70rpx;
        margin: 0 20rpx;
      }

      .stat-icon-rs {
        width: 110rpx;
        height: 95rpx;
        margin: 0 20rpx;
      }

      .stat-value {
        font-size: 36rpx;
        font-weight: 600;
        color: #222;
        margin-bottom: 6rpx;
      }

      .stat-label {
        font-size: 25rpx;
        color: #595757
      }
    }
  }

  /* 分销订单卡片样式 */
  .order-card {
    margin: 40rpx;
    margin-top: 0;
    background-color: #ffffff;
    border-radius: 15rpx;
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .order-info {
      display: flex;
      align-items: center;

      .order-icon {
        width: 48rpx;
        height: 54rpx;
        margin-right: 30rpx;

      }

      &-item {
        display: flex;
        align-items: center;

        &-title {
          font-size: 25rpx;
          font-weight: bold;

          color: #222;
        }

        &-count {
          font-size: 24rpx;
          color: #595757;
          margin-top: 6rpx;
        }
      }
    }

    .order-all {
      display: flex;
      align-items: center;

      &-title {
        font-weight: bold;
        font-size: 18rpx;
        color: #929292;
        margin-right: 10rpx;
      }
    }

  }

}
</style>
