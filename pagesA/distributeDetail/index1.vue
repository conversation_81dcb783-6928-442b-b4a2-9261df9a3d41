<template>
	<view class="container">
		<page-nav title="分销商信息" navigatorType="switchTab" url="pages/user/index" :border="false"></page-nav>
		<view class="content">
			<u--form labelWidth="200rpx">
				<view class="block_wrap">
					<u-form-item label="真实姓名">
						<u-input v-model="distributeInfo.realName" border="none" readonly inputAlign="right" />
					</u-form-item>
					<u-form-item label="员工编号">
						<u-input v-model="distributeInfo.employeeNumber" border="none" readonly inputAlign="right" />
					</u-form-item>
					<u-form-item label="手机号">
						<u-input v-model="distributeInfo.phoneNumber" border="none" readonly inputAlign="right" />
					</u-form-item>
					<u-form-item label="公司">
						<u-input v-model="distributeInfo.companyName" border="none" readonly inputAlign="right" />
					</u-form-item>
					<u-form-item label="地区">
						<u-input v-model="distributeInfo.districtName" border="none" readonly inputAlign="right"
							placeholder="选择公司后会带出地区" />
					</u-form-item>
					<u-form-item label="状态">
						<u-input :value="statusTxt" border="none" readonly inputAlign="right" />
					</u-form-item>
				</view>
			</u--form>
			<view v-if="distributeInfo.auditStatus === '2'" class="footer_wrap">
				<u-button color="#117ACD" :loading="submitLoading" :customStyle="payStyles" @click="handleQRCode">
					查看分销二维码
				</u-button>
			</view>
		</view>
		<share-modal ref="shareModal"></share-modal>
	</view>
</template>

<script>
	import {
		getDistributeDetail
	} from '@/api/user.js'
	import {
		mapState,
		mapActions
	} from 'vuex';
	import ShareModal from './components/shareModal.vue';
	import {
		distributeAuditStatus
	} from '@/util/enums.js'
	export default {
		components: {
			ShareModal
		},
		data() {
			return {
				submitLoading: false,
				distributeInfo: {}
			}
		},
		onShow() {
			this.initData();

		},
		computed: {
			...mapState({
				userInfo: (state) => {
					console.log('state.User', state.User);
					return state.User.userInfo
				},
			}),
			statusTxt() {
				let row = distributeAuditStatus.find(item => item.value === this.distributeInfo.auditStatus)
				return row?.label
			},
			payStyles() {
				return {
					height: '98rpx',
					fontWeight: '600',
					fontSize: '36rpx',
					color: '#FFFFFF',
					borderRadius: '14rpx'
				}
			}
		},
		methods: {
			...mapActions(['getUserInfo']),
			handleQRCode() {
				this.$refs.shareModal?.open();
			},
			initData() {
				console.log('this.userInfo', this.userInfo);
				let {
					distributor,
					id
				} = this.userInfo;
				if (distributor && distributor.id) {
					uni.showLoading({
						title: '加载中'
					});
					getDistributeDetail(id).then(res => {
						uni.hideLoading();
						if (res.code === 200) {
							this.distributeInfo = res.data?.distributor || {}
						}
					}).catch(() => {
						uni.hideLoading();
					})
				}
			},

			handleNext() {

			}

		},
	}
</script>
<style>
	.u-form-item__body {
		padding: 20rpx 30rpx !important;
	}
</style>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background-color: #F8F9F9;

		.tip {
			width: 100%;
			height: 68rpx;
			line-height: 68rpx;
			background: rgba(17, 122, 205, 0.1);
			font-weight: 500;
			font-size: 26rpx;
			color: #117ACD;
			// text-align: center;
		}

		.content {
			padding: 0 20rpx;

			.block_wrap {
				background-color: #fff;
				margin-top: 20rpx;
				border-radius: 12rpx;
				padding-bottom: 20rpx;

				.title_wrap {
					color: #303133;
					font-size: 30rpx;

					.title_desc {
						color: #ADADAD;
					}
				}
			}

			.footer_wrap {
				box-sizing: border-box;
				position: fixed;
				left: 0;
				bottom: 70rpx;
				width: 100%;
				padding: 0 20rpx;

				.footer_tip {
					text-align: center;
					font-weight: 500;
					font-size: 26rpx;
					color: rgba(51, 51, 51, 0.4);
					margin-bottom: 28rpx;
				}
			}
		}
	}
</style>