<template>
	<u-popup mode="center" :closeOnClickOverlay="true" round="20rpx" :closeable="false" :safeAreaInsetBottom="false"
		:show="show" @close="close">

		<view class="content">
			<uqrcode v-if="show" ref="uqrcode" canvas-id="qrcode" :value="shareUrl" :options="{ margin: 10 }">
			</uqrcode>
			<view>
				<u-button type="primary" :customStyle="{margin:'10rpx 0'}" text="下载分享码"
					@click="handleDownload"></u-button>
				<u-button type="primary" open-type="share" text="分享" ></u-button>
			</view>
		</view>

	</u-popup>
</template>

<script>
	import {
		mapState
	} from 'vuex';
	import uqrcode from '@/pagesA/components/Sansnn-uQRCode/components/u-qrcode/u-qrcode.vue'
	export default {
		components: {
			uqrcode
		},
		data() {
			return {
				show: false
			}
		},
		computed: {
			...mapState({
				userInfo: (state) => {
					console.log('state.User', state.User);
					return state.User.userInfo
				},
			}),
			shareUrl() {
				return this.joinParams(process.env.VUE_APP_DISTRIBUTE_SHARE);
			}
		},
		// mounted() {
		// 	console.log('onShow');
		// 	// 该对象已集成到this.$u中，内部属性如下
		// 	uni.$u.mpShare = {
		// 		// title: '测试', // 默认为小程序名称，可自定义
		// 		path: this.joinParams('pages/home/<USER>'), // 默认为当前页面路径，一般无需修改，QQ小程序不支持
		// 		// 分享图标，路径可以是本地文件路径、代码包文件路径或者网络图片路径。
		// 		// 支持PNG及JPG，默认为当前页面的截图
		// 		imageUrl: 'https://millenia.oss-cn-wuhan-lr.aliyuncs.com/2024-12-24/1735005871017_b7fdeb0f.png'
		// 	}
		// },

		methods: {
			// joinParams(url) {
			// 	let params = {
			// 		distributorId: process.env.NODE_ENV === 'production' ? this.userInfo.distributor.id :
			// 			'1866319618574921729'
			// 	}
			// 	let query = uni.$u.queryParams(params)
			// 	return `${url}${query}`
			// },
			handleDownload() {
				// uqrcode为组件的ref名称
				this.$refs.uqrcode.toTempFilePath({
					success: res => {
						this.$refs.uqrcode.save({
							success: () => {
								uni.showToast({
									icon: 'success',
									title: '下载成功'
								});
							}
						});
					}
				});

			},
			close() {
				this.show = false;
			},
			open() {
				this.show = true;

			}
		},
	}
</script>

<style lang="scss" scoped>
	.content {
		padding: 20rpx;
	}
</style>