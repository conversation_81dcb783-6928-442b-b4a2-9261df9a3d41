<template>
  <view class="container">
    <page-nav
      title="运动打卡"
      url="pagesA/healthManage/index"
      bgColor="transparent"
      :border="false"
    ></page-nav>

    <view class="content">
      <u--form labelPosition="top" labelWidth="auto">
        <block v-for="(item, index) in form" :key="index">
          <view class="form-item">
            <view class="label_wrap">
              <view class="label"> <text class="required">*</text>运动方式 </view>
              <view class="del" @click="handleDelete(index)">删除</view>
            </view>
            <u-input
              v-model="item.sportType"
              fontSize="28rpx"
              :customStyle="inputStyles"
              placeholder="请输入"
            />
          </view>

          <u-form-item label="运动时长" required>
			  <d-picker
			    v-model="item.sportTime"
			    border="surround"
			    valueName="value"
			    labelName="label"
			    :inputStyle="inputStyles"
			    :options="timeList"
			  ></d-picker>
            <!-- <u-input
              v-model="item.sportTime"
              fontSize="30rpx"
              :customStyle="inputStyles"
              placeholder="请输入运动时长"
            /> -->
          </u-form-item>
        </block>

        <!-- 添加按钮 -->
        <view class="add-btn" @click="handleAdd">
          <u-icon name="plus-circle" color="#117ACD" size="20"></u-icon>
          <text>添加</text>
        </view>
      </u--form>

      <view class="footer_wrap">
        <u-button
          color="#117ACD"
          :loading="submitLoading"
          :customStyle="payStyles"
          @click="handleSubmit"
        >
          保存记录
        </u-button>
      </view>
    </view>
  </view>
</template>

<script>
import { insertSport } from "@/api/healthManage";
import {timeList} from "@/util/enums.js"
export default {
  data() {
    return {
      submitLoading: false,
      imageUrl: "", // 上传的图片地址
      form: [
        {
          sportType: "",
          sportTime: "",
        },
      ],
	  timeList,
    };
  },
  computed: {
    inputStyles() {
      return {
        backgroundColor: "#fff",
        borderRadius: "22rpx",
        height: "60rpx",
        flex: "auto",
      };
    },
    payStyles() {
      return {
        height: "98rpx",
        fontWeight: "600",
        fontSize: "36rpx",
        color: "#FFFFFF",
        borderRadius: "14rpx",
      };
    },
  },
  methods: {
    handleSubmit() {
      let isFinish = this.form.every((item) => item.sportType && item.sportTime);
      if (!isFinish) {
        uni.$u.toast("请输入完整信息");
        return;
      }
      const params = this.form.map((item) => ({
        sportType: item.sportType,
        sportTime: item.sportTime,
      }));
      this.submitLoading = true;
      insertSport(params).then((res) => {
        this.submitLoading = false;
        if (res.code === 200) {
          uni.$u.toast("打卡成功");
          setTimeout(() => {
            uni.$u.route({
              url: "pagesA/healthManage/index",
            });
          }, 1000);
        } else {
          uni.$u.toast(res.msg);
        }
      });
    },
    handleAdd() {
      this.form.push({
        sportType: "",
        sportTime: "",
      });
    },
    handleDelete(index) {
      if (this.form.length > 1) {
        this.form.splice(index, 1);
      }
    },
  },
};
</script>
<style>
.u-form-item__body {
  padding: 20rpx 0 !important;
}
.u-input{
	padding:40rpx 30rpx !important;
}
</style>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #edf4ff 0%, #f9f9fa 100%);

  .content {
    padding: 32rpx;

    .form-item {
      padding: 24rpx 0;
      border-bottom: 1rpx solid #eeeeee;

      &:last-child {
        border-bottom: none;
      }

      .label_wrap {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;

        .label {
          font-size: 15px;
          color: #303133;
          font-weight: 400;
        }

        .del {
          font-size: 28rpx;
          color: #ff3b30;
        }
      }

      .label {
        font-size: 28rpx;
        color: #333333;
        // margin-bottom: 16rpx;
        display: block;
        position: relative;
      }
    }

    .upload-section {
      margin: 32rpx 0;

      .upload-box {
        width: 100%;
        height: 320rpx;
        background: #ffffff;
        border-radius: 12rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 16rpx;

        .upload-text {
          font-size: 28rpx;
          color: #909399;
        }

        .preview-image {
          width: 100%;
          height: 100%;
          border-radius: 12rpx;
        }
      }
    }

    .add-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      margin: 40rpx 0;

      text {
        color: #117acd;
        font-size: 28rpx;
      }
    }
    .footer_wrap {
      box-sizing: border-box;
      position: fixed;
      left: 0;
      bottom: 70rpx;
      width: 100%;
      padding: 0 20rpx;
    }
  }
}

.required {
  position: absolute;
  left: -9px;
  color: #f56c6c;
  line-height: 20px;
  font-size: 20px;
  top: 3px;
}
</style>
