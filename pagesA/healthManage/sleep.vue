<template>
  <view class="container">
    <page-nav
      title="睡眠打卡"
      url="pagesA/healthManage/index"
      bgColor="transparent"
      :border="false"
    ></page-nav>

    <view class="content">
      <u--form
        :model="form"
        :rules="rules"
        ref="uForm"
        labelPosition="top"
        labelWidth="initial"
      >
        <u-form-item label="睡眠时长" prop="sleepTime">
          <!-- 下拉 -->
          <d-picker
            v-model="form.sleepTime"
            border="surround"
            valueName="value"
            labelName="label"
            :inputStyle="inputStyles"
            :options="sleepTimeList"
          ></d-picker>
        </u-form-item>
        <u-form-item label="睡眠质量" prop="sleepQualit">
          <!-- 下拉 -->
          <d-picker
            v-model="form.sleepQualit"
            border="surround"
            valueName="dictValue"
            labelName="dictLabel"
            :inputStyle="inputStyles"
            :options="sleepQualityList"
          ></d-picker>
        </u-form-item>
      </u--form>

      <view class="footer_wrap">
        <u-button
          color="#117ACD"
          :loading="submitLoading"
          :customStyle="payStyles"
          @click="handleSubmit"
        >
          保存记录
        </u-button>
      </view>
    </view>
  </view>
</template>

<script>
import { insertSleep } from "@/api/healthManage";
import { findDict } from "@/api/common";
import {timeList} from "@/util/enums.js"

export default {
  data() {
    return {
      submitLoading: false,
      form: {
        sleepTime: "",
        sleepQualit: "",
      },
      sleepQualityList: [],
      sleepTimeList: timeList,
      rules: {
        sleepTime: [
          {
            type: "number",
            required: true,
            message: "请选择睡眠时长",
            trigger: ["blur", "change"],
          },
        ],
        sleepQualit: [
          {
            type: "string",
            required: true,
            message: "请选择睡眠质量",
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  onReady() {
    //如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
    this.$refs.uForm.setRules(this.rules);
  },
  onLoad() {
    this.getSleepQuality();
  },
  computed: {
    inputStyles() {
      return {
        backgroundColor: "#fff",
        borderRadius: "22rpx",
        height: "60rpx",
        flex: "auto",
      };
    },
    payStyles() {
      return {
        height: "98rpx",
        fontWeight: "600",
        fontSize: "36rpx",
        color: "#FFFFFF",
        borderRadius: "14rpx",
      };
    },
  },
  methods: {
    getSleepQuality() {
      findDict("sleep_quality").then((res) => {
        this.sleepQualityList = res.data;
      });
    },
    handleSubmit() {
      this.$refs.uForm
        .validate()
        .then(async () => {
          this.submitLoading = true;
          insertSleep(this.form).then((res) => {
            this.submitLoading = false;
            if (res.code === 200) {
              uni.$u.toast("打卡成功");
              setTimeout(() => {
                uni.$u.route({
                  url: "pagesA/healthManage/index",
                });
              }, 1000);
            } else {
              uni.$u.toast(res.msg);
            }
          });
        })
        .catch(() => {
          uni.$u.toast("请完善表单信息");
        });
    },
  },
};
</script>
<style>
.u-form-item__body {
  padding: 20rpx 0 !important;
}
.u-input{
	padding:40rpx 30rpx !important;
}
</style>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #edf4ff 0%, #f9f9fa 100%);

  .content {
    padding: 0 20rpx;

    .footer_wrap {
      box-sizing: border-box;
      position: fixed;
      left: 0;
      bottom: 70rpx;
      width: 100%;
      padding: 0 20rpx;
    }
  }
}
</style>
