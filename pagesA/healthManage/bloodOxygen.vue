<template>
	<view class="container">
	  <page-nav
	    title="健康血氧"
	    url="pagesA/healthManage/index"
	    bgColor="transparent"
	    :border="false"
	  ></page-nav>
	
	  <view class="content">
	    <u--form
	      :model="form"
	      :rules="rules"
	      ref="uForm"
	      labelPosition="top"
	      labelWidth="auto"
	    >
	      <u-form-item label="健康血氧打卡" prop="oximetry">
	        <u-input
	          v-model="form.oximetry"
	          fontSize="30rpx"
	          :customStyle="inputStyles"
	          placeholder="请输入动脉血氧饱和度（%)"
	        />
	      </u-form-item>
	    
	    </u--form>
	
	    <view class="footer_wrap">
	      <u-button
	        color="#117ACD"
	        :loading="submitLoading"
	        :customStyle="payStyles"
	        @click="handleSubmit"
	      >
	        保存记录
	      </u-button>
	    </view>
	  </view>
	</view>
</template>

<script>
import { insertBloodOxygen } from "@/api/healthManage";
export default {
  data() {
    return {
      submitLoading: false,
      form: {
        oximetry: "",
      },
      rules: {
        oximetry: [
          {
            required: true,
            message: "请输入动脉血氧饱和度（%)",
            trigger: ["blur", "change"],
          },
          {
            pattern: /^(100|[1-9]?\d|0)$/,
            message: "请输入合理动脉血氧饱和度（%)",
            trigger: ["blur", "change"],
          },
        ],
        
      },
    };
  },
  onReady() {
    //如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
    this.$refs.uForm.setRules(this.rules);
  },

  computed: {
    inputStyles() {
      return {
        backgroundColor: "#fff",
        borderRadius: "22rpx",
        height: "60rpx",
        flex: "auto",
      };
    },
    payStyles() {
      return {
        height: "98rpx",
        fontWeight: "600",
        fontSize: "36rpx",
        color: "#FFFFFF",
        borderRadius: "14rpx",
      };
    },
  },
  methods: {
    handleSubmit() {
      this.$refs.uForm
        .validate()
        .then(async () => {
          this.submitLoading = true;
          insertBloodOxygen(this.form).then((res) => {
            this.submitLoading = false;
            if (res.code === 200) {
              uni.$u.toast("打卡成功");
              setTimeout(() => {
                uni.$u.route({
                  url: "pagesA/healthManage/index",
                });
              }, 500);
            } else {
              uni.$u.toast(res.msg);
            }
          });
        })
        .catch(() => {
          uni.$u.toast("请完善表单信息");
        });
    },
  },
};
</script>

<style>
.u-form-item__body {
  padding: 20rpx 0 !important;
}
.u-input{
	padding:40rpx 30rpx !important;
}
</style>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #edf4ff 0%, #f9f9fa 100%);

  .content {
    padding: 0 20rpx;

    .footer_wrap {
      box-sizing: border-box;
      position: fixed;
      left: 0;
      bottom: 70rpx;
      width: 100%;
      padding: 0 20rpx;
    }
  }
}
</style>