<template>
  <view class="container">
    <page-nav title="餐食打卡" url="pagesA/healthManage/index" :border="false"></page-nav>

    <view class="content">
      <u--form :model="form" :rules="rules" ref="uForm" label-width="130rpx">
        <!-- 早餐 -->
        <u-form-item label="早餐时间" prop="breakfastTime">
          <pick-date
            v-model="form.breakfastTime"
            mode="time"
            fontSize="30rpx"
            :customStyle="inputStyles"
            placeholder="请选择早餐时间"
          />
        </u-form-item>
        <u-form-item label-width="0" prop="breakfastImage">
          <image-upload
            v-model="form.breakfastImage"
            width="710rpx"
            height="320rpx"
            :maxCount="1"
          >
            <view class="upload-section">
              <view class="upload-box">
                <template v-if="!form.breakfastImage">
                  <u-icon name="plus" size="44" color="#DCDEE0"></u-icon>
                  <text class="upload-text">上传图片</text>
                </template>
              </view>
            </view>
          </image-upload>
        </u-form-item>
        <!-- 午餐 -->
        <u-form-item label="午餐时间" prop="lunchTime">
          <pick-date
            v-model="form.lunchTime"
            mode="time"
            fontSize="30rpx"
            :customStyle="inputStyles"
            placeholder="请选择午餐时间"
          />
        </u-form-item>
        <u-form-item label-width="0" prop="lunchImage">
          <image-upload
            v-model="form.lunchImage"
            width="710rpx"
            height="320rpx"
            :maxCount="1"
          >
            <view class="upload-section">
              <view class="upload-box">
                <template v-if="!form.lunchImage">
                  <u-icon name="plus" size="44" color="#DCDEE0"></u-icon>
                  <text class="upload-text">上传图片</text>
                </template>
              </view>
            </view>
          </image-upload>
        </u-form-item>
        <!-- 晚餐 -->
        <u-form-item label="晚餐时间" prop="dinnerTime">
          <pick-date
            v-model="form.dinnerTime"
            mode="time"
            fontSize="30rpx"
            :customStyle="inputStyles"
            placeholder="请选择晚餐时间"
          />
        </u-form-item>
        <u-form-item label-width="0" prop="dinnerImage">
          <image-upload
            v-model="form.dinnerImage"
            width="710rpx"
            height="320rpx"
            :maxCount="1"
          >
            <view class="upload-section">
              <view class="upload-box">
                <template v-if="!form.dinnerImage">
                  <u-icon name="plus" size="44" color="#DCDEE0"></u-icon>
                  <text class="upload-text">上传图片</text>
                </template>
              </view>
            </view>
          </image-upload>
        </u-form-item>
      </u--form>

      <view class="footer_wrap">
        <u-button
          color="#117ACD"
          :loading="submitLoading"
          :customStyle="payStyles"
          @click="handleSubmit"
        >
          保存记录
        </u-button>
      </view>
    </view>
  </view>
</template>

<script>
import { insertFood } from "@/api/healthManage";
export default {
  data() {
    return {
      submitLoading: false,
      form: {
        breakfastTime: "",
        breakfastImage: "",
        lunchTime: "",
        lunchImage: "",
        dinnerTime: "",
        dinnerImage: "",
      },
      rules: {
        breakfastTime: [
          {
            required: true,
            message: "请选择早餐时间",
            trigger: ["blur", "change"],
          },
        ],
        lunchTime: [
          {
            type: "string",
            required: true,
            message: "请选择午餐时间",
            trigger: ["blur", "change"],
          },
        ],
        dinnerTime: [
          {
            type: "string",
            required: true,
            message: "请选择晚餐时间",
            trigger: ["blur", "change"],
          },
        ],
        breakfastImage: [
          {
            required: true,
            message: "请上传早餐图片",
            trigger: ["blur", "change"],
          },
        ],
        lunchImage: [
          {
            required: true,
            message: "请上传午餐图片",
            trigger: ["blur", "change"],
          },
        ],
        dinnerImage: [
          {
            required: true,
            message: "请上传晚餐图片",
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  onReady() {
    //如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
    this.$refs.uForm.setRules(this.rules);
  },

  computed: {
    inputStyles() {
      return {
        backgroundColor: "#fff",
        borderRadius: "22rpx",
        height: "60rpx",
        flex: "auto",
      };
    },
    payStyles() {
      return {
        height: "98rpx",
        fontWeight: "600",
        fontSize: "36rpx",
        color: "#FFFFFF",
        borderRadius: "14rpx",
      };
    },
  },
  methods: {
    handleSubmit() {
      this.$refs.uForm
        .validate()
        .then(async () => {
          this.submitLoading = true;
          insertFood(this.form).then((res) => {
            this.submitLoading = false;
            if (res.code === 200) {
              uni.$u.toast("打卡成功");
              setTimeout(() => {
                uni.$u.route({
                  url: "pagesA/healthManage/index",
                });
              }, 500);
            } else {
              uni.$u.toast(res.msg);
            }
          });
        })
        .catch(() => {
          uni.$u.toast("请完善表单信息");
        });
    },
  },
};
</script>
<style>
.u-form-item__body {
  padding: 20rpx 0 !important;
}
</style>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #edf4ff 0%, #f9f9fa 100%);
  padding-bottom: 150rpx;

  .content {
    padding: 0 20rpx;
    .upload-section {
      width: 710rpx;
      margin: 20rpx 0;

      .upload-box {
        width: 100%;
        height: 320rpx;
        background: #ffffff;
        border-radius: 12rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 16rpx;

        .upload-text {
          font-size: 28rpx;
          color: #909399;
        }

        .preview-image {
          width: 100%;
          height: 100%;
          border-radius: 12rpx;
        }
      }
    }

    .footer_wrap {
      background-color: #fff;
      box-sizing: border-box;
      position: fixed;
      left: 0;
      bottom: 0;
      width: 100%;
      padding: 20rpx 20rpx 70rpx;
    }
  }
}
</style>
