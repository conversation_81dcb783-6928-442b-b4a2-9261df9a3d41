<template>
  <view class="container">
    <page-nav
      title="体重打卡"
      url="pagesA/healthManage/index"
      bgColor="transparent"
      :border="false"
    ></page-nav>

    <view class="content">
      <u--form
        :model="form"
        :rules="rules"
        ref="uForm"
        labelPosition="top"
        labelWidth="auto"
      >
        <u-form-item label="体重" prop="weight">
          <u-input
            v-model="form.weight"
            fontSize="30rpx"
            :customStyle="inputStyles"
            placeholder="请输入体重"
            @change="handleChange"
            ><template slot="suffix"> kg </template>
          </u-input>
        </u-form-item>
        <u-form-item label="BMI">
          <u-input
            v-model="form.bmi"
            :disabled="true"
            fontSize="30rpx"
            :customStyle="inputStyles"
            placeholder="BMI=体重kg /( 身高m * 身高m )"
          />
        </u-form-item>
      </u--form>

      <view class="footer_wrap">
        <u-button
          color="#117ACD"
          :loading="submitLoading"
          :customStyle="payStyles"
          @click="handleSubmit"
        >
          保存记录
        </u-button>
      </view>
    </view>
  </view>
</template>

<script>
import { insertWeight, findUserHeight } from "@/api/healthManage";
import { question } from "@/common/config";
export default {
  data() {
    return {
      submitLoading: false,
      form: {
        weight: "",
        bmi: "",
      },
      height: "",
      rules: {
        weight: [
          {
            required: true,
            message: "请输入体重",
            trigger: ["blur", "change"],
          },
          {
            pattern: /^(?:[1-9]\d*|0)(?:\.\d{1})?$/,
            message: "请输入正确体重",
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  onReady() {
    //如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
    this.$refs.uForm.setRules(this.rules);
  },
  onShow() {
    this.findUserHeight();
  },

  computed: {
    inputStyles() {
      return {
        backgroundColor: "#fff",
        borderRadius: "22rpx",
        height: "60rpx",
        flex: "auto",
      };
    },
    payStyles() {
      return {
        height: "98rpx",
        fontWeight: "600",
        fontSize: "36rpx",
        color: "#FFFFFF",
        borderRadius: "14rpx",
      };
    },
  },
  methods: {
    findUserHeight() {
      findUserHeight(question.singleId).then((res) => {
        console.log("res", res);
        this.height = res.data || 0;
      });
    },
    handleSubmit() {
      this.$refs.uForm
        .validate()
        .then(async () => {
          this.submitLoading = true;
          insertWeight(this.form).then((res) => {
            this.submitLoading = false;
            if (res.code === 200) {
              uni.$u.toast("打卡成功");
              setTimeout(() => {
                uni.$u.route({
                  url: "pagesA/healthManage/index",
                });
              }, 500);
            } else {
              uni.$u.toast(res.msg);
            }
          });
        })
        .catch(() => {
          uni.$u.toast("请完善表单信息");
        });
    },
    handleChange(value) {
      this.form.bmi = (value / (this.height * this.height)).toFixed(2);
    },
  },
};
</script>
<style>
.u-form-item__body {
  padding: 20rpx 0 !important;
}
.u-input{
	padding:40rpx 30rpx !important;
}
</style>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #edf4ff 0%, #f9f9fa 100%);

  .content {
    padding: 0 20rpx;

    .footer_wrap {
      box-sizing: border-box;
      position: fixed;
      left: 0;
      bottom: 70rpx;
      width: 100%;
      padding: 0 20rpx;
    }
  }
}
</style>
