<template>
	<view class="container">
		<page-nav title="我的健康管理" :titleStyle="{ color: '#fff' }" navigatorType="switchTab" url="pages/user/index"
			bgColor="transparent" leftIconColor="#fff" :border="false"></page-nav>
		<view class="title-container">
			<text class="title">今日打卡</text>
			<view class="right-container" @click="handleRecord">
				<view class="right-text">打卡记录</view>
				<u-icon name="arrow-right" size="24rpx" color="#007aff"></u-icon>
			</view>
		</view>
		<view class="bg_wrap">
			<u--image :showLoading="true"
				src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/2025-02-20/1740036906207_59209137.png" width="376rpx"
				height="376rpx"></u--image>
		</view>
		<view class="card-container">
			<view class="card" v-for="item in quotaList" :key="item.value" @click="handleClick(item)">
				<view class="card-content">
					<view class="card-left">
						<u--image :showLoading="true" :src="item.icon" width="40rpx" height="40rpx"></u--image>
						<text class="item-name">{{ item.label }}</text>
					</view>
					<view class="card-right">
						<u-checkbox-group>
							<u-checkbox :checked="!item.available" shape="circle" active-color="#43CF8E"></u-checkbox>
						</u-checkbox-group>
					</view>
				</view>
			</view>
		</view>
		<view class="chart-containers">
			<view class="titles">
				<view class="left">我的健康数据</view>
				<view class="right" @click="handleAllRecord">
					<text>查看全部数据</text>
					<u-icon name="arrow-right" size="24rpx" color="#333"></u-icon>
				</view>
			</view>
			<weight :echartData="weightRecord" />
			<blood :echartData="bloodRecord" />
		</view>
	</view>
</template>

<script>
	import {
		getAvailableCheckinCategories,
		getHealthCheckHistory
	} from "@/api/healthManage";
	import weight from '@/pagesA/health/components/weight.vue'
	import blood from '@/pagesA/health/components/blood.vue'
	const routeList = {
		1: {
			url: "pagesA/healthManage/blood", //血压脉搏
			icon: "https://ma.aimeyear.com/staticFile/images/healthManage/blood.png",
		},
		2: {
			url: "pagesA/healthManage/food", //餐食打卡
			icon: "https://ma.aimeyear.com/staticFile/images/healthManage/food.png",
		},
		3: {
			url: "pagesA/healthManage/weight", //体重打卡
			icon: "https://ma.aimeyear.com/staticFile/images/healthManage/weight.png",
		},
		4: {
			url: "pagesA/healthManage/sport", //运动打卡
			icon: "https://ma.aimeyear.com/staticFile/images/healthManage/sport.png",
		},
		5: {
			url: "pagesA/healthManage/sleep", //睡眠打卡
			icon: "https://ma.aimeyear.com/staticFile/images/healthManage/sleep.png",
		},
		6: {
			url: "pagesA/healthManage/wine", //饮酒打卡
			icon: "https://ma.aimeyear.com/staticFile/images/healthManage/wine.png",
		},
		7: {
			url: "pagesA/healthManage/defecate", //排便打卡
			icon: "https://ma.aimeyear.com/staticFile/images/healthManage/defecate.png",
		},
		8: {
			url: "pagesA/healthManage/bloodSugar", //血糖打卡
			icon: "https://ma.aimeyear.com/staticFile/images/healthManage/bloodSugar.png",
		},
		9: {
			url: "pagesA/healthManage/heartRate", // 健康心率打卡
			icon: "https://ma.aimeyear.com/staticFile/images/healthManage/heartRate.png",
		},
		10: {
			url: "pagesA/healthManage/bloodOxygen", // 健康血氧打卡
			icon: "https://ma.aimeyear.com/staticFile/images/healthManage/bloodOxygen.png",
		},
	};
	export default {
		components: {
			weight,
			blood
		},
		data() {
			const today = new Date();
			const lastWeek = new Date(today);
			lastWeek.setDate(today.getDate() - 6);

			return {
				quotaList: [],
				baseUrl: process.env.VUE_APP_STATIC_FILE_URL,
				weightRecord: {
					x: [],
					y: []
				},
				bloodRecord: {
					x: [],
					systolic: [],
					diastolic: []
				}
			};
		},
		onShow() {
			this.initData();
			this.getHealthData();
		},
		methods: {
			initData() {
				getAvailableCheckinCategories().then((res) => {
					console.log("res", res);
					let list = res.code === 200 ? res.data : [];
					this.quotaList = list.map((item) => {
						const row = routeList[item.value];
						return {
							...item,
							...row,
						};
					});
				});
			},
			handleClick(item) {
				console.log("item", item);
				if (!item.available) {
					uni.$u.toast("今日已打卡");
					return;
				}
				const row = routeList[item.value];
				if (row && row.url) {
					uni.$u.route({
						url: row.url,
					});
				}
			},
			handleRecord() {
				uni.$u.route({
					url: "pages/clockinRecord/index",
				});
			},
			handleAllRecord() {
				uni.$u.route({
					url: "pagesA/health/data",
				});
			},
			// 获取健康数据
			async getHealthData() {
				const today = new Date();
				const lastWeek = new Date(today);
				lastWeek.setDate(today.getDate() - 6);

				const params = {
					startDate: this.formatDate(lastWeek),
					endDate: this.formatDate(today)
				};

				const res = await getHealthCheckHistory(params);
				const data = res.data;

				// 处理体重数据
				this.weightRecord = {
					x: data.weightRecord.map(item => item.checkInDate),
					y: data.weightRecord.map(item => item.weight)
				};

				// 处理血压数据
				this.bloodRecord = {
					x: data.bloodPressureRecord.map(item => item.checkInDate),
					systolic: data.bloodPressureRecord.map(item => item.systolic),
					diastolic: data.bloodPressureRecord.map(item => item.diastolic)
				};
			},
			// 格式化日期为 YYYY-MM-DD
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
		},
	};
</script>

<style lang="scss">
	.container {
		padding: 20px;
		min-height: 100vh;
		background: #F6F7FB linear-gradient(179deg,
				#59b2f9 0%,
				rgba(89, 178, 249, 0) 600rpx,
				rgba(255, 255, 255, 0) 1000rpx);

		.bg_wrap {
			position: absolute;
			top: 88rpx;
			right: 0;
			z-index: -1;
		}

		.chart-containers {
			background-color: #fff;
			// padding:20rpx 30rpx;
			margin-top: 30rpx;
			overflow: hidden;

			.titles {
				display: flex;
				justify-content: space-between;
				align-items: baseline;
				padding: 20rpx ;

				.left {
					font-weight: 800;
					font-size: 32rpx;
					color: #2D3642;
					position: relative;

					&::after {
						content: '';
						position: absolute;
						width: 100%;
						height: 12rpx;
						background: linear-gradient(90deg, #0691FF 0%, rgba(17, 122, 205, 0) 100%);
						bottom: 2rpx;
						left: 0;
					}
				}

				.right {
					font-weight: 400;
					font-size: 24rpx;
					color: #2D3642;
					display: flex;
					align-items: center;
				}
			}
		}

		.title-container {
			display: flex;
			/* 使用 Flexbox 布局 */
			justify-content: space-between;
			/* 在两侧分配空间 */
			align-items: center;
			/* 垂直居中对齐 */
			padding: 10px;
			/* 增加内边距 */
		}

		.title {
			font-size: 28px;
			/* 标题字体大小 */
			color: #fff;
			/* 标题颜色 */
		}

		.right-container {
			display: flex;
			/* 右侧内容使用 Flexbox */
			align-items: baseline;
			/* 垂直居中对齐 */
			background-color: rgba(255, 255, 255, 0.8);
			/* 背景颜色 */
			border-radius: 40rpx;
			/* 圆角 */
			padding: 9rpx 22rpx;
			/* 内边距 */
		}

		.right-text {
			margin-right: 10rpx;
			/* 增加右侧文本与图标的间距 */
			color: #007aff;
			/* 右侧文本颜色 */
			font-size: 24rpx;
			/* 字体大小 */
		}

		.card-container {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			/* 创建两列 */
			gap: 10px;
			/* 设置列间距 */
			margin-top: 40rpx;

			.card {
				background-color: #ffffff;
				/* 确保卡片背景颜色 */
				border-radius: 24rpx;
				/* 增加圆角 */
				box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
				/* 添加阴影效果 */
				padding: 30rpx 18rpx 30rpx 30rpx;
				/* 增加内边距 */

				.card-content {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.card-left {
						display: flex;
						align-items: center;

						.item-name {
							margin-left: 10rpx;
							font-weight: 500;
							font-size: 28rpx;
							color: #2d3642;
						}
					}

					.card-right {
						position: relative;

						&::after {
							content: "";
							position: absolute;
							top: 0;
							right: 0;
							width: 100%;
							height: 100%;
							background: transparent;
						}

						.status {
							font-weight: 500;
							font-size: 28rpx;
							color: #2d3642;
						}
					}
				}
			}
		}
	}
</style>