!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).echarts={})}(this,function(t){"use strict";var v=function(t,e){return(v=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)};function u(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}v(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var _=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},b=new function(){this.browser=new _,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(b.wxa=!0,b.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?b.worker=!0:!b.hasGlobalWindow||"Deno"in window?(b.node=!0,b.svgSupported=!0):(J=navigator.userAgent,re=(Ht=b).browser,rt=J.match(/Firefox\/([\d.]+)/),W=J.match(/MSIE\s([\d.]+)/)||J.match(/Trident\/.+?rv:(([\d.]+))/),Q=J.match(/Edge?\/([\d.]+)/),J=/micromessenger/i.test(J),rt&&(re.firefox=!0,re.version=rt[1]),W&&(re.ie=!0,re.version=W[1]),Q&&(re.edge=!0,re.version=Q[1],re.newEdge=18<+Q[1].split(".")[0]),J&&(re.weChat=!0),Ht.svgSupported="undefined"!=typeof SVGRect,Ht.touchEventsSupported="ontouchstart"in window&&!re.ie&&!re.edge,Ht.pointerEventsSupported="onpointerdown"in window&&(re.edge||re.ie&&11<=+re.version),Ht.domSupported="undefined"!=typeof document,rt=document.documentElement.style,Ht.transform3dSupported=(re.ie&&"transition"in rt||re.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in rt)&&!("OTransition"in rt),Ht.transformSupported=Ht.transform3dSupported||re.ie&&9<=+re.version);var K="12px sans-serif";var x,w,S=function(t){var e={};if("undefined"!=typeof JSON)for(var n=0;n<t.length;n++){var i=String.fromCharCode(n+32),r=(t.charCodeAt(n)-20)/100;e[i]=r}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),G={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(x||(n=G.createCanvas(),x=n&&n.getContext("2d")),x)return w!==e&&(w=x.font=e||K),x.measureText(t);t=t||"",e=e||K;var n=/((?:\d+)?\.?\d*)px/.exec(e),i=n&&+n[1]||12,r=0;if(0<=e.indexOf("mono"))r=i*t.length;else for(var o=0;o<t.length;o++){var a=S[t[o]];r+=null==a?i:a*i}return{width:r}},loadImage:function(t,e,n){var i=new Image;return i.onload=e,i.onerror=n,i.src=t,i}};function T(t){for(var e in G)t[e]&&(G[e]=t[e])}var C=lt(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(t,e){return t["[object "+e+"]"]=!0,t},{}),A=lt(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(t,e){return t["[object "+e+"Array]"]=!0,t},{}),P=Object.prototype.toString,W=Array.prototype,U=W.forEach,q=W.filter,Z=W.slice,$=W.map,Q=function(){}.constructor,J=Q?Q.prototype:null,tt="__proto__",et=2311;function nt(){return et++}function it(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function y(t){if(null==t||"object"!=typeof t)return t;var e=t,n=P.call(t);if("[object Array]"===n){if(!Dt(t))for(var e=[],i=0,r=t.length;i<r;i++)e[i]=y(t[i])}else if(A[n]){if(!Dt(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=t[i]}}}else if(!C[n]&&!Dt(t)&&!yt(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==tt&&(e[a]=y(t[a]));return e}function d(t,e,n){if(!R(e)||!R(t))return n?y(e):t;for(var i in e){var r,o;e.hasOwnProperty(i)&&i!==tt&&(r=t[i],!R(o=e[i])||!R(r)||F(o)||F(r)||yt(o)||yt(r)||ft(o)||ft(r)||Dt(o)||Dt(r)?!n&&i in t||(t[i]=y(e[i])):d(r,o,n))}return t}function L(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==tt&&(t[n]=e[n]);return t}function z(t,e,n){for(var i=ht(e),r=0,o=i.length;r<o;r++){var a=i[r];(n?null!=e[a]:null==t[a])&&(t[a]=e[a])}return t}var rt=G.createCanvas;function I(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function ot(t,e){var n,i=t.prototype;function r(){}for(n in r.prototype=e.prototype,t.prototype=new r,i)i.hasOwnProperty(n)&&(t.prototype[n]=i[n]);(t.prototype.constructor=t).superClass=e}function at(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else z(t,e,n)}function st(t){return!!t&&"string"!=typeof t&&"number"==typeof t.length}function O(t,e,n){if(t&&e)if(t.forEach&&t.forEach===U)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function B(t,e,n){if(!t)return[];if(!e)return St(t);if(t.map&&t.map===$)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function lt(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function ut(t,e,n){if(!t)return[];if(!e)return St(t);if(t.filter&&t.filter===q)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function ht(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e);return n}var ct=J&&k(J.bind)?J.call.bind(J.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(Z.call(arguments)))}};function pt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(Z.call(arguments)))}}function F(t){return Array.isArray?Array.isArray(t):"[object Array]"===P.call(t)}function k(t){return"function"==typeof t}function V(t){return"string"==typeof t}function dt(t){return"[object String]"===P.call(t)}function H(t){return"number"==typeof t}function R(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function ft(t){return!!C[P.call(t)]}function gt(t){return!!A[P.call(t)]}function yt(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function mt(t){return null!=t.colorStops}function vt(t){return null!=t.image}function _t(t){return"[object RegExp]"===P.call(t)}function xt(t){return t!=t}function wt(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]}function N(t,e){return null!=t?t:e}function bt(t,e,n){return null!=t?t:null!=e?e:n}function St(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return Z.apply(t,e)}function Mt(t){var e;return"number"==typeof t?[t,t,t,t]:2===(e=t.length)?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function Tt(t,e){if(!t)throw new Error(e)}function Ct(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var It="__ec_primitive__";function kt(t){t[It]=!0}function Dt(t){return t[It]}Pt.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},Pt.prototype.has=function(t){return this.data.hasOwnProperty(t)},Pt.prototype.get=function(t){return this.data[t]},Pt.prototype.set=function(t,e){return this.data[t]=e,this},Pt.prototype.keys=function(){return ht(this.data)},Pt.prototype.forEach=function(t){var e,n=this.data;for(e in n)n.hasOwnProperty(e)&&t(n[e],e)};var At=Pt;function Pt(){this.data={}}var Lt="function"==typeof Map;Rt.prototype.hasKey=function(t){return this.data.has(t)},Rt.prototype.get=function(t){return this.data.get(t)},Rt.prototype.set=function(t,e){return this.data.set(t,e),e},Rt.prototype.each=function(n,i){this.data.forEach(function(t,e){n.call(i,t,e)})},Rt.prototype.keys=function(){var t=this.data.keys();return Lt?Array.from(t):t},Rt.prototype.removeKey=function(t){this.data.delete(t)};var Ot=Rt;function Rt(t){var n=F(t),i=(this.data=new(Lt?Map:At),this);function e(t,e){n?i.set(t,e):i.set(e,t)}t instanceof Rt?t.each(e):t&&O(t,e)}function E(t){return new Ot(t)}function Nt(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];for(var r=t.length,i=0;i<e.length;i++)n[i+r]=e[i];return n}function Et(t,e){var n,t=Object.create?Object.create(t):((n=function(){}).prototype=t,new n);return e&&L(t,e),t}function zt(t){t=t.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function Bt(t,e){return t.hasOwnProperty(e)}function Ft(){}var Vt=180/Math.PI,Ht=Object.freeze({__proto__:null,HashMap:Ot,RADIAN_TO_DEGREE:Vt,assert:Tt,bind:ct,clone:y,concatArray:Nt,createCanvas:rt,createHashMap:E,createObject:Et,curry:pt,defaults:z,disableUserSelect:zt,each:O,eqNaN:xt,extend:L,filter:ut,find:function(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]},guid:nt,hasOwn:Bt,indexOf:I,inherits:ot,isArray:F,isArrayLike:st,isBuiltInObject:ft,isDom:yt,isFunction:k,isGradientObject:mt,isImagePatternObject:vt,isNumber:H,isObject:R,isPrimitive:Dt,isRegExp:_t,isString:V,isStringSafe:dt,isTypedArray:gt,keys:ht,logError:it,map:B,merge:d,mergeAll:function(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=d(n,t[i],e);return n},mixin:at,noop:Ft,normalizeCssArray:Mt,reduce:lt,retrieve:wt,retrieve2:N,retrieve3:bt,setAsPrimitive:kt,slice:St,trim:Ct});function Gt(t,e){return[t=null==t?0:t,e=null==e?0:e]}function Wt(t){return[t[0],t[1]]}function Ut(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Xt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function Yt(t){return Math.sqrt(qt(t))}function qt(t){return t[0]*t[0]+t[1]*t[1]}function Zt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function jt(t,e){var n=Yt(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Kt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var $t=Kt;function Qt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Jt=Qt;function te(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function ee(t,e,n){var i=e[0],e=e[1];return t[0]=n[0]*i+n[2]*e+n[4],t[1]=n[1]*i+n[3]*e+n[5],t}function ne(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function ie(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var re=Object.freeze({__proto__:null,add:Ut,applyTransform:ee,clone:Wt,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},create:Gt,dist:$t,distSquare:Jt,distance:Kt,distanceSquare:Qt,div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},len:Yt,lenSquare:qt,length:Yt,lengthSquare:qt,lerp:te,max:ie,min:ne,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:jt,scale:Zt,scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},set:function(t,e,n){return t[0]=e,t[1]=n,t},sub:Xt}),oe=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},ae=(se.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new oe(e,t),"dragstart",t.event))},se.prototype._drag=function(t){var e,n,i,r,o=this._draggingTarget;o&&(e=t.offsetX,n=t.offsetY,i=e-this._x,r=n-this._y,this._x=e,this._y=n,o.drift(i,r,t),this.handler.dispatchToElement(new oe(o,t),"drag",t.event),i=this.handler.findHover(e,n,o).target,r=this._dropTarget,o!==(this._dropTarget=i))&&(r&&i!==r&&this.handler.dispatchToElement(new oe(r,t),"dragleave",t.event),i)&&i!==r&&this.handler.dispatchToElement(new oe(i,t),"dragenter",t.event)},se.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new oe(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new oe(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},se);function se(t){(this.handler=t).on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}ue.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),n&&t){var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;o={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},e=r[t].length-1,i=r[t][e];i&&i.callAtLast?r[t].splice(e,0,o):r[t].push(o)}return this},ue.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},ue.prototype.off=function(t,e){var n=this._$handlers;if(n)if(t)if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];else this._$handlers={};return this},ue.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}r&&r.afterTrigger&&r.afterTrigger(t)}return this},ue.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}r&&r.afterTrigger&&r.afterTrigger(t)}return this};var le=ue;function ue(t){t&&(this._$eventProcessor=t)}var he=Math.log(2);function ce(t,e,n,i,r,o){var a,s=i+"-"+r,l=t.length;if(o.hasOwnProperty(s))return o[s];if(1===e)return a=Math.round(Math.log((1<<l)-1&~r)/he),t[n][a];for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,d=0;p<l;p++){var f=1<<p;f&r||(c+=(d%2?-1:1)*t[n][p]*ce(t,e-1,h,u,r|f,o),d++)}return o[s]=c}function pe(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=ce(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*ce(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var de="___zrEVENTSAVED",fe=[];function ge(t,e,n,i,r){if(e.getBoundingClientRect&&b.domSupported&&!ye(e)){var o=e[de]||(e[de]={}),e=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left,h=h.top;a.push(p,h),l=l&&o&&p===o[c]&&h===o[1+c],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?pe(s,a):pe(a,s))}(function(t,e){var n=e.markers;if(!n){n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,l=o%2,u=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",r[u]+":0",i[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}}return n}(e,o),o,r);if(e)return e(t,n,i),!0}return!1}function ye(t){return"CANVAS"===t.nodeName.toUpperCase()}var me=/([&<>"'])/g,ve={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function _e(t){return null==t?"":(t+"").replace(me,function(t,e){return ve[e]})}var xe=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,we=[],be=b.browser.firefox&&+b.browser.version.split(".")[0]<39;function Se(t,e,n,i){return n=n||{},i?Me(t,e,n):be&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):Me(t,e,n),n}function Me(t,e,n){if(b.domSupported&&t.getBoundingClientRect){var i,r=e.clientX,e=e.clientY;if(ye(t))return i=t.getBoundingClientRect(),n.zrX=r-i.left,void(n.zrY=e-i.top);if(ge(we,t,r,e))return n.zrX=we[0],void(n.zrY=we[1])}n.zrX=n.zrY=0}function Te(t){return t||window.event}function Ce(t,e,n){var i;return null==(e=Te(e)).zrX&&((i=e.type)&&0<=i.indexOf("touch")?(i=("touchend"!==i?e.targetTouches:e.changedTouches)[0])&&Se(t,i,e,n):(Se(t,e,e,n),i=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,t=t.deltaY;return null!=n&&null!=t?3*(0!==t?Math.abs(t):Math.abs(n))*(0<t||!(t<0)&&0<n?-1:1):e}(e),e.zrDelta=i?i/120:-(e.detail||0)/3),t=e.button,null==e.which&&void 0!==t&&xe.test(e.type))&&(e.which=1&t?1:2&t?3:4&t?2:0),e}var Ie=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0},ke=(De.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},De.prototype.clear=function(){return this._track.length=0,this},De.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=Se(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},De.prototype._recognize=function(t){for(var e in Pe)if(Pe.hasOwnProperty(e)){e=Pe[e](this._track,t);if(e)return e}},De);function De(){this._track=[]}function Ae(t){var e=t[1][0]-t[0][0],t=t[1][1]-t[0][1];return Math.sqrt(e*e+t*t)}var Pe={pinch:function(t,e){var n,i=t.length;if(i)return n=(t[i-1]||{}).points,(i=(t[i-2]||{}).points||n)&&1<i.length&&n&&1<n.length?(i=Ae(n)/Ae(i),isFinite(i)||(i=1),e.pinchScale=i,i=[(n[0][0]+n[1][0])/2,(n[0][1]+n[1][1])/2],e.pinchX=i[0],e.pinchY=i[1],{type:"pinch",target:t[0].target,event:e}):void 0}};function Le(){return[1,0,0,1,0,0]}function Oe(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Re(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Ne(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],n=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=n,t}function Ee(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function ze(t,e,n,i){void 0===i&&(i=[0,0]);var r=e[0],o=e[2],a=e[4],s=e[1],l=e[3],e=e[5],u=Math.sin(n),n=Math.cos(n);return t[0]=r*n+s*u,t[1]=-r*u+s*n,t[2]=o*n+l*u,t[3]=-o*u+n*l,t[4]=n*(a-i[0])+u*(e-i[1])+i[0],t[5]=n*(e-i[1])-u*(a-i[0])+i[1],t}function Be(t,e,n){var i=n[0],n=n[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function Fe(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],e=e[5],s=n*a-o*i;return s?(t[0]=a*(s=1/s),t[1]=-o*s,t[2]=-i*s,t[3]=n*s,t[4]=(i*e-a*r)*s,t[5]=(o*r-n*e)*s,t):null}var Ve=Object.freeze({__proto__:null,clone:function(t){var e=Le();return Re(e,t),e},copy:Re,create:Le,identity:Oe,invert:Fe,mul:Ne,rotate:ze,scale:Be,translate:Ee}),M=(e.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},e.prototype.clone=function(){return new e(this.x,this.y)},e.prototype.set=function(t,e){return this.x=t,this.y=e,this},e.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},e.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},e.prototype.scale=function(t){this.x*=t,this.y*=t},e.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},e.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},e.prototype.dot=function(t){return this.x*t.x+this.y*t.y},e.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},e.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},e.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},e.prototype.distance=function(t){var e=this.x-t.x,t=this.y-t.y;return Math.sqrt(e*e+t*t)},e.prototype.distanceSquare=function(t){var e=this.x-t.x,t=this.y-t.y;return e*e+t*t},e.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},e.prototype.transform=function(t){var e,n;if(t)return e=this.x,n=this.y,this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this},e.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},e.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},e.set=function(t,e,n){t.x=e,t.y=n},e.copy=function(t,e){t.x=e.x,t.y=e.y},e.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},e.lenSquare=function(t){return t.x*t.x+t.y*t.y},e.dot=function(t,e){return t.x*e.x+t.y*e.y},e.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},e.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},e.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},e.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},e.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},e);function e(t,e){this.x=t||0,this.y=e||0}var He=Math.min,Ge=Math.max,We=new M,Ue=new M,Xe=new M,Ye=new M,qe=new M,Ze=new M,X=(je.prototype.union=function(t){var e=He(t.x,this.x),n=He(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Ge(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Ge(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},je.prototype.applyTransform=function(t){je.applyTransform(this,this,t)},je.prototype.calculateTransform=function(t){var e=t.width/this.width,n=t.height/this.height,i=Le();return Ee(i,i,[-this.x,-this.y]),Be(i,i,[e,n]),Ee(i,i,[t.x,t.y]),i},je.prototype.intersect=function(t,e){if(!t)return!1;t instanceof je||(t=je.create(t));var n,i,r,o,a,s,l,u,h=this,c=h.x,p=h.x+h.width,d=h.y,h=h.y+h.height,f=t.x,g=t.x+t.width,y=t.y,t=t.y+t.height,m=!(p<f||g<c||h<y||t<d);return e&&(n=1/0,i=0,r=Math.abs(p-f),o=Math.abs(g-c),a=Math.abs(h-y),s=Math.abs(t-d),l=Math.min(r,o),u=Math.min(a,s),p<f||g<c?i<l&&(i=l,r<o?M.set(Ze,-r,0):M.set(Ze,o,0)):l<n&&(n=l,r<o?M.set(qe,r,0):M.set(qe,-o,0)),h<y||t<d?i<u&&(i=u,a<s?M.set(Ze,0,-a):M.set(Ze,0,s)):l<n&&(n=l,a<s?M.set(qe,0,a):M.set(qe,0,-s))),e&&M.copy(e,m?qe:Ze),m},je.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},je.prototype.clone=function(){return new je(this.x,this.y,this.width,this.height)},je.prototype.copy=function(t){je.copy(this,t)},je.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},je.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},je.prototype.isZero=function(){return 0===this.width||0===this.height},je.create=function(t){return new je(t.x,t.y,t.width,t.height)},je.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},je.applyTransform=function(t,e,n){var i,r,o,a;n?n[1]<1e-5&&-1e-5<n[1]&&n[2]<1e-5&&-1e-5<n[2]?(i=n[0],r=n[3],o=n[4],a=n[5],t.x=e.x*i+o,t.y=e.y*r+a,t.width=e.width*i,t.height=e.height*r,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height)):(We.x=Xe.x=e.x,We.y=Ye.y=e.y,Ue.x=Ye.x=e.x+e.width,Ue.y=Xe.y=e.y+e.height,We.transform(n),Ye.transform(n),Ue.transform(n),Xe.transform(n),t.x=He(We.x,Ue.x,Xe.x,Ye.x),t.y=He(We.y,Ue.y,Xe.y,Ye.y),o=Ge(We.x,Ue.x,Xe.x,Ye.x),a=Ge(We.y,Ue.y,Xe.y,Ye.y),t.width=o-t.x,t.height=a-t.y):t!==e&&je.copy(t,e)},je);function je(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}var Ke="silent";function $e(){Ie(this.event)}u(tn,Qe=le),tn.prototype.dispose=function(){},tn.prototype.setCursor=function(){};var Qe,Je=tn;function tn(){var t=null!==Qe&&Qe.apply(this,arguments)||this;return t.handler=null,t}var en,nn=function(t,e){this.x=t,this.y=e},rn=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],on=new X(0,0,0,0),an=(u(sn,en=le),sn.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(O(rn,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},sn.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=un(this,e,n),r=this._hovered,o=r.target,i=(o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target),this._hovered=i?new nn(e,n):this.findHover(e,n)),e=i.target,n=this.proxy;n.setCursor&&n.setCursor(e?e.cursor:"default"),o&&e!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(i,"mousemove",t),e&&e!==o&&this.dispatchToElement(i,"mouseover",t)},sn.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},sn.prototype.resize=function(){this._hovered=new nn(0,0)},sn.prototype.dispatch=function(t,e){t=this[t];t&&t.call(this,e)},sn.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},sn.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},sn.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o={type:e,event:n,target:(t=t).target,topTarget:t.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:$e};i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget||i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)}))}},sn.prototype.findHover=function(t,e,n){var i=this.storage.getDisplayList(),r=new nn(t,e);if(ln(i,r,t,e,n),this._pointerSize&&!r.target){for(var o=[],a=this._pointerSize,s=a/2,l=new X(t-s,e-s,a,a),u=i.length-1;0<=u;u--){var h=i[u];h===n||h.ignore||h.ignoreCoarsePointer||h.parent&&h.parent.ignoreCoarsePointer||(on.copy(h.getBoundingRect()),h.transform&&on.applyTransform(h.transform),on.intersect(l)&&o.push(h))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,d=0;d<s;d+=4)for(var f=0;f<p;f+=c)if(ln(o,r,t+d*Math.cos(f),e+d*Math.sin(f),n),r.target)return r}return r},sn.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new ke);var n=this._gestureMgr,i=("start"===e&&n.clear(),n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom));"end"===e&&n.clear(),i&&(e=i.type,t.gestureEvent=e,(n=new nn).target=i.target,this.dispatchToElement(n,e,i.event))},sn);function sn(t,e,n,i,r){var o=en.call(this)||this;return o._hovered=new nn(0,0),o.storage=t,o.painter=e,o.painterRoot=i,o._pointerSize=r,n=n||new Je,o.proxy=null,o.setHandlerProxy(n),o._draggingMgr=new ae(o),o}function ln(t,e,n,i,r){for(var o=t.length-1;0<=o;o--){var a=t[o],s=void 0;if(a!==r&&!a.ignore&&(s=function(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(!(o=i.ignoreClip?!0:o)){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1}i.silent&&(r=!0);a=i.__hostTarget,i=a||i.parent}return!r||Ke}return!1}(a,n,i))&&(e.topTarget||(e.topTarget=a),s!==Ke)){e.target=a;break}}}function un(t,e,n){t=t.painter;return e<0||e>t.getWidth()||n<0||n>t.getHeight()}O(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){an.prototype[a]=function(t){var e,n,i=t.zrX,r=t.zrY,o=un(this,i,r);if("mouseup"===a&&o||(n=(e=this.findHover(i,r)).target),"mousedown"===a)this._downEl=n,this._downPoint=[t.zrX,t.zrY],this._upEl=n;else if("mouseup"===a)this._upEl=n;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<$t(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}});var hn=32,cn=7;function pn(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;var o=t,a=e,s=r;for(s--;a<s;){var l=o[a];o[a++]=o[s],o[s--]=l}}else for(;r<n&&0<=i(t[r],t[r-1]);)r++;return r-e}function dn(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=1+o;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<u;)t[s+u]=t[s+u-1],u--}t[s]=a}}function fn(t,e,n,i,r,o){var a=0,s=0,l=1;if(0<o(t,e[n+r])){for(s=i-r;l<s&&0<o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)(l=1+((a=l)<<1))<=0&&(l=s);i=a,a=r-(l=s<l?s:l),l=r-i}for(a++;a<l;){var u=a+(l-a>>>1);0<o(t,e[n+u])?a=u+1:l=u}return l}function gn(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)(l=1+((a=l)<<1))<=0&&(l=s);var u=a,a=r-(l=s<l?s:l),l=r-u}else{for(s=i-r;l<s&&0<=o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function yn(A,P){var L,O,R=cn,N=0,E=[];function e(t){var e=L[t],n=O[t],i=L[t+1],r=O[t+1],t=(O[t]=n+r,t===N-3&&(L[t+1]=L[t+2],O[t+1]=O[t+2]),N--,gn(A[i],A,e,n,0,P));if(e+=t,0!=(n-=t)&&0!==(r=fn(A[e+n-1],A,i,r,r-1,P)))if(n<=r){var o=e,a=n,t=i,s=r,l=0;for(l=0;l<a;l++)E[l]=A[o+l];var u=0,h=t,c=o;if(A[c++]=A[h++],0==--s)for(l=0;l<a;l++)A[c+l]=E[u+l];else if(1===a){for(l=0;l<s;l++)A[c+l]=A[h+l];A[c+s]=E[u]}else{for(var p,d,f,g=R;;){d=p=0,f=!1;do{if(P(A[h],E[u])<0){if(A[c++]=A[h++],d++,(p=0)==--s){f=!0;break}}else if(A[c++]=E[u++],p++,d=0,1==--a){f=!0;break}}while((p|d)<g);if(f)break;do{if(0!==(p=gn(A[h],E,u,a,0,P))){for(l=0;l<p;l++)A[c+l]=E[u+l];if(c+=p,u+=p,(a-=p)<=1){f=!0;break}}if(A[c++]=A[h++],0==--s){f=!0;break}if(0!==(d=fn(E[u],A,h,s,0,P))){for(l=0;l<d;l++)A[c+l]=A[h+l];if(c+=d,h+=d,0===(s-=d)){f=!0;break}}if(A[c++]=E[u++],1==--a){f=!0;break}}while(g--,cn<=p||cn<=d);if(f)break;g<0&&(g=0),g+=2}if((R=g)<1&&(R=1),1===a){for(l=0;l<s;l++)A[c+l]=A[h+l];A[c+s]=E[u]}else{if(0===a)throw new Error;for(l=0;l<a;l++)A[c+l]=E[u+l]}}}else{var y=e,m=n,v=i,_=r,x=0;for(x=0;x<_;x++)E[x]=A[v+x];var w=y+m-1,b=_-1,S=v+_-1,M=0,T=0;if(A[S--]=A[w--],0==--m)for(M=S-(_-1),x=0;x<_;x++)A[M+x]=E[x];else if(1===_){for(T=(S-=m)+1,M=(w-=m)+1,x=m-1;0<=x;x--)A[T+x]=A[M+x];A[S]=E[b]}else{for(var C=R;;){var I=0,k=0,D=!1;do{if(P(E[b],A[w])<0){if(A[S--]=A[w--],I++,(k=0)==--m){D=!0;break}}else if(A[S--]=E[b--],k++,I=0,1==--_){D=!0;break}}while((I|k)<C);if(D)break;do{if(0!==(I=m-gn(E[b],A,y,m,m-1,P))){for(m-=I,T=(S-=I)+1,M=(w-=I)+1,x=I-1;0<=x;x--)A[T+x]=A[M+x];if(0===m){D=!0;break}}if(A[S--]=E[b--],1==--_){D=!0;break}if(0!==(k=_-fn(A[w],E,0,_,_-1,P))){for(_-=k,T=(S-=k)+1,M=(b-=k)+1,x=0;x<k;x++)A[T+x]=E[M+x];if(_<=1){D=!0;break}}if(A[S--]=A[w--],0==--m){D=!0;break}}while(C--,cn<=I||cn<=k);if(D)break;C<0&&(C=0),C+=2}if((R=C)<1&&(R=1),1===_){for(T=(S-=m)+1,M=(w-=m)+1,x=m-1;0<=x;x--)A[T+x]=A[M+x];A[S]=E[b]}else{if(0===_)throw new Error;for(M=S-(_-1),x=0;x<_;x++)A[M+x]=E[x]}}}}return L=[],O=[],{mergeRuns:function(){for(;1<N;){var t=N-2;if(1<=t&&O[t-1]<=O[t]+O[t+1]||2<=t&&O[t-2]<=O[t]+O[t-1])O[t-1]<O[t+1]&&t--;else if(O[t]>O[t+1])break;e(t)}},forceMergeRuns:function(){for(;1<N;){var t=N-2;0<t&&O[t-1]<O[t+1]&&t--,e(t)}},pushRun:function(t,e){L[N]=t,O[N]=e,N+=1}}}function mn(t,e,n,i){var r=(i=i||t.length)-(n=n||0);if(!(r<2)){var o=0;if(r<hn)dn(t,n,i,n+(o=pn(t,n,i,e)),e);else{var a,s=yn(t,e),l=function(t){for(var e=0;hn<=t;)e|=1&t,t>>=1;return t+e}(r);do{}while((o=pn(t,n,i,e))<l&&(dn(t,n,n+(a=l<(a=r)?l:r),n+o,e),o=a),s.pushRun(n,o),s.mergeRuns(),n+=o,0!==(r-=o));s.forceMergeRuns()}}}var vn=1,_n=4,xn=!1;function wn(){xn||(xn=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function bn(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}Mn.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},Mn.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},Mn.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,mn(n,bn)},Mn.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),r=(o=r).getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty|=vn),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else{i=t;e&&e.length?i.__clipPaths=e:i.__clipPaths&&0<i.__clipPaths.length&&(i.__clipPaths=[]),isNaN(i.z)&&(wn(),i.z=0),isNaN(i.z2)&&(wn(),i.z2=0),isNaN(i.zlevel)&&(wn(),i.zlevel=0),this._displayList[this._displayListLen++]=i}i=t.getDecalElement&&t.getDecalElement(),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextGuideLine()),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextContent());i&&this._updateAndAddDisplayable(i,e,n)}},Mn.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},Mn.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=I(this._roots,t);0<=i&&this._roots.splice(i,1)}},Mn.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},Mn.prototype.getRoots=function(){return this._roots},Mn.prototype.dispose=function(){this._displayList=null,this._roots=null};var Sn=Mn;function Mn(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=bn}var Tn=b.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},Cn={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:n*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-Cn.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*Cn.bounceIn(2*t):.5*Cn.bounceOut(2*t-1)+.5}},In=Math.pow,kn=Math.sqrt,Dn=1e-8,An=kn(3),Pn=1/3,Ln=Gt(),On=Gt(),Rn=Gt();function Nn(t){return-Dn<t&&t<Dn}function En(t){return Dn<t||t<-Dn}function zn(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function Bn(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function Fn(t,e,n,i,r,o){var a,s,i=i+3*(e-n)-t,n=3*(n-2*e+t),e=3*(e-t),t=t-r,r=n*n-3*i*e,l=n*e-9*i*t,t=e*e-3*n*t,u=0;return Nn(r)&&Nn(l)?Nn(n)?o[0]=0:0<=(a=-e/n)&&a<=1&&(o[u++]=a):Nn(e=l*l-4*r*t)?(s=-(t=l/r)/2,0<=(a=-n/i+t)&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s)):0<e?(e=r*n+1.5*i*(-l-(t=kn(e))),0<=(a=(-n-((t=(t=r*n+1.5*i*(-l+t))<0?-In(-t,Pn):In(t,Pn))+(e=e<0?-In(-e,Pn):In(e,Pn))))/(3*i))&&a<=1&&(o[u++]=a)):(t=(2*r*n-3*i*l)/(2*kn(r*r*r)),e=Math.acos(t)/3,a=(-n-2*(l=kn(r))*(t=Math.cos(e)))/(3*i),s=(-n+l*(t+An*Math.sin(e)))/(3*i),r=(-n+l*(t-An*Math.sin(e)))/(3*i),0<=a&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s),0<=r&&r<=1&&(o[u++]=r)),u}function Vn(t,e,n,i,r){var o,a=6*n-12*e+6*t,i=9*e+3*i-3*t-9*n,n=3*e-3*t,e=0;return Nn(i)?En(a)&&0<=(o=-n/a)&&o<=1&&(r[e++]=o):Nn(t=a*a-4*i*n)?r[0]=-a/(2*i):0<t&&(t=(-a-(n=kn(t)))/(2*i),0<=(o=(-a+n)/(2*i))&&o<=1&&(r[e++]=o),0<=t)&&t<=1&&(r[e++]=t),e}function Hn(t,e,n,i,r,o){var a=(e-t)*r+t,e=(n-e)*r+e,n=(i-n)*r+n,s=(e-a)*r+a,e=(n-e)*r+e,r=(e-s)*r+s;o[0]=t,o[1]=a,o[2]=s,o[3]=r,o[4]=r,o[5]=e,o[6]=n,o[7]=i}function Gn(t,e,n,i,r,o,a,s,l,u,h){var c,p,d,f,g=.005,y=1/0;Ln[0]=l,Ln[1]=u;for(var m=0;m<1;m+=.05)On[0]=zn(t,n,r,a,m),On[1]=zn(e,i,o,s,m),(d=Jt(Ln,On))<y&&(c=m,y=d);for(var y=1/0,v=0;v<32&&!(g<1e-4);v++)p=c+g,On[0]=zn(t,n,r,a,f=c-g),On[1]=zn(e,i,o,s,f),d=Jt(On,Ln),0<=f&&d<y?(c=f,y=d):(Rn[0]=zn(t,n,r,a,p),Rn[1]=zn(e,i,o,s,p),f=Jt(Rn,Ln),p<=1&&f<y?(c=p,y=f):g*=.5);return h&&(h[0]=zn(t,n,r,a,c),h[1]=zn(e,i,o,s,c)),kn(y)}function Wn(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function Un(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function Xn(t,e,n){n=t+n-2*e;return 0==n?.5:(t-e)/n}function Yn(t,e,n,i,r){var o=(e-t)*i+t,e=(n-e)*i+e,i=(e-o)*i+o;r[0]=t,r[1]=o,r[2]=i,r[3]=i,r[4]=e,r[5]=n}function qn(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;Ln[0]=a,Ln[1]=s;for(var p=0;p<1;p+=.05)On[0]=Wn(t,n,r,p),On[1]=Wn(e,i,o,p),(y=Jt(Ln,On))<c&&(u=p,c=y);for(var c=1/0,d=0;d<32&&!(h<1e-4);d++){var f=u-h,g=u+h,y=(On[0]=Wn(t,n,r,f),On[1]=Wn(e,i,o,f),Jt(On,Ln));0<=f&&y<c?(u=f,c=y):(Rn[0]=Wn(t,n,r,g),Rn[1]=Wn(e,i,o,g),f=Jt(Rn,Ln),g<=1&&f<c?(u=g,c=f):h*=.5)}return l&&(l[0]=Wn(t,n,r,u),l[1]=Wn(e,i,o,u)),kn(c)}var Zn=/cubic-bezier\(([0-9,\.e ]+)\)/;function jn(t){t=t&&Zn.exec(t);if(t){var e,t=t[1].split(","),n=+Ct(t[0]),i=+Ct(t[1]),r=+Ct(t[2]),o=+Ct(t[3]);if(!isNaN(n+i+r+o))return e=[],function(t){return t<=0?0:1<=t?1:Fn(0,n,r,1,t,e)&&zn(0,i,o,1,e[0])}}}$n.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n,o=(r<0&&(r=0),r=Math.min(r,1),this.easingFunc),o=o?o(r):r;if(this.onframe(o),1===r){if(!this.loop)return!0;this._startTime=t-i%n,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},$n.prototype.pause=function(){this._paused=!0},$n.prototype.resume=function(){this._paused=!1},$n.prototype.setEasing=function(t){this.easing=t,this.easingFunc=k(t)?t:Cn[t]||jn(t)};var Kn=$n;function $n(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Ft,this.ondestroy=t.ondestroy||Ft,this.onrestart=t.onrestart||Ft,t.easing&&this.setEasing(t.easing)}var Qn=function(t){this.value=t},Jn=(ti.prototype.insert=function(t){t=new Qn(t);return this.insertEntry(t),t},ti.prototype.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},ti.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},ti.prototype.len=function(){return this._len},ti.prototype.clear=function(){this.head=this.tail=null,this._len=0},ti);function ti(){this._len=0}ni.prototype.put=function(t,e){var n,i,r=this._list,o=this._map,a=null;return null==o[t]&&(i=r.len(),n=this._lastRemovedEntry,i>=this._maxSize&&0<i&&(i=r.head,r.remove(i),delete o[i.key],a=i.value,this._lastRemovedEntry=i),n?n.value=e:n=new Qn(e),n.key=t,r.insertEntry(n),o[t]=n),a},ni.prototype.get=function(t){var t=this._map[t],e=this._list;if(null!=t)return t!==e.tail&&(e.remove(t),e.insertEntry(t)),t.value},ni.prototype.clear=function(){this._list.clear(),this._map={}},ni.prototype.len=function(){return this._list.len()};var ei=ni;function ni(t){this._list=new Jn,this._maxSize=10,this._map={},this._maxSize=t}var ii={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function ri(t){return(t=Math.round(t))<0?0:255<t?255:t}function oi(t){return t<0?0:1<t?1:t}function ai(t){return t.length&&"%"===t.charAt(t.length-1)?ri(parseFloat(t)/100*255):ri(parseInt(t,10))}function si(t){return t.length&&"%"===t.charAt(t.length-1)?oi(parseFloat(t)/100):oi(parseFloat(t))}function li(t,e,n){return n<0?n+=1:1<n&&--n,6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function ui(t,e,n){return t+(e-t)*n}function hi(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function ci(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var pi=new ei(20),di=null;function fi(t,e){di&&ci(di,e),di=pi.put(t,di||e.slice())}function gi(t,e){if(t){e=e||[];var n=pi.get(t);if(n)return ci(e,n);n=(t+="").replace(/ /g,"").toLowerCase();if(n in ii)return ci(e,ii[n]),fi(t,e),e;var i=n.length;if("#"===n.charAt(0))return 4===i||5===i?0<=(r=parseInt(n.slice(1,4),16))&&r<=4095?(hi(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===i?parseInt(n.slice(4),16)/15:1),fi(t,e),e):void hi(e,0,0,0,1):7===i||9===i?0<=(r=parseInt(n.slice(1,7),16))&&r<=16777215?(hi(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===i?parseInt(n.slice(7),16)/255:1),fi(t,e),e):void hi(e,0,0,0,1):void 0;var r=n.indexOf("("),o=n.indexOf(")");if(-1!==r&&o+1===i){var i=n.substr(0,r),a=n.substr(r+1,o-(r+1)).split(","),s=1;switch(i){case"rgba":if(4!==a.length)return 3===a.length?hi(e,+a[0],+a[1],+a[2],1):hi(e,0,0,0,1);s=si(a.pop());case"rgb":return 3<=a.length?(hi(e,ai(a[0]),ai(a[1]),ai(a[2]),3===a.length?s:si(a[3])),fi(t,e),e):void hi(e,0,0,0,1);case"hsla":return 4!==a.length?void hi(e,0,0,0,1):(a[3]=si(a[3]),yi(a,e),fi(t,e),e);case"hsl":return 3!==a.length?void hi(e,0,0,0,1):(yi(a,e),fi(t,e),e);default:return}}hi(e,0,0,0,1)}}function yi(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=si(t[1]),r=si(t[2]),i=r<=.5?r*(i+1):r+i-r*i,r=2*r-i;return hi(e=e||[],ri(255*li(r,i,n+1/3)),ri(255*li(r,i,n)),ri(255*li(r,i,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function mi(t,e){var n=gi(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,255<n[i]?n[i]=255:n[i]<0&&(n[i]=0);return wi(n,4===n.length?"rgba":"rgb")}}function vi(t,e,n){var i,r,o;if(e&&e.length&&0<=t&&t<=1)return n=n||[],t=t*(e.length-1),i=Math.floor(t),o=Math.ceil(t),r=e[i],e=e[o],n[0]=ri(ui(r[0],e[0],o=t-i)),n[1]=ri(ui(r[1],e[1],o)),n[2]=ri(ui(r[2],e[2],o)),n[3]=oi(ui(r[3],e[3],o)),n}var _i=vi;function xi(t,e,n){var i,r,o,a;if(e&&e.length&&0<=t&&t<=1)return t=t*(e.length-1),i=Math.floor(t),r=Math.ceil(t),a=gi(e[i]),e=gi(e[r]),a=wi([ri(ui(a[0],e[0],o=t-i)),ri(ui(a[1],e[1],o)),ri(ui(a[2],e[2],o)),oi(ui(a[3],e[3],o))],"rgba"),n?{color:a,leftIndex:i,rightIndex:r,value:t}:a}var n=xi;function wi(t,e){var n;if(t&&t.length)return n=t[0]+","+t[1]+","+t[2],"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}function bi(t,e){t=gi(t);return t?(.299*t[0]+.587*t[1]+.114*t[2])*t[3]/255+(1-t[3])*e:0}var Si=new ei(100);function Mi(t){var e;return V(t)?((e=Si.get(t))||(e=mi(t,-.1),Si.put(t,e)),e):mt(t)?((e=L({},t)).colorStops=B(t.colorStops,function(t){return{offset:t.offset,color:mi(t.color,-.1)}}),e):t}_i=Object.freeze({__proto__:null,fastLerp:vi,fastMapToColor:_i,lerp:xi,lift:mi,liftColor:Mi,lum:bi,mapToColor:n,modifyAlpha:function(t,e){if((t=gi(t))&&null!=e)return t[3]=oi(e),wi(t,"rgba")},modifyHSL:function(t,e,n,i){var r=gi(t);if(t)return r=function(t){var e,n,i,r,o,a,s,l,u,h;if(t)return h=t[0]/255,e=t[1]/255,n=t[2]/255,s=Math.min(h,e,n),r=((i=Math.max(h,e,n))+s)/2,0==(u=i-s)?a=o=0:(a=r<.5?u/(i+s):u/(2-i-s),s=((i-h)/6+u/2)/u,l=((i-e)/6+u/2)/u,u=((i-n)/6+u/2)/u,h===i?o=u-l:e===i?o=1/3+s-u:n===i&&(o=2/3+l-s),o<0&&(o+=1),1<o&&--o),h=[360*o,a,r],null!=t[3]&&h.push(t[3]),h}(r),null!=e&&(r[0]=(t=e,(t=Math.round(t))<0?0:360<t?360:t)),null!=n&&(r[1]=si(n)),null!=i&&(r[2]=si(i)),wi(yi(r),"rgba")},parse:gi,random:function(){return wi([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},stringify:wi,toHex:function(t){if(t=gi(t))return((1<<24)+(t[0]<<16)+(t[1]<<8)+ +t[2]).toString(16).slice(1)}});b.hasGlobalWindow&&k(window.btoa);var Ti=Array.prototype.slice;function Ci(t,e,n){return(e-t)*n+t}function Ii(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=Ci(e[o],n[o],i);return t}function ki(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function Di(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function Ai(t){if(st(t)){var e=t.length;if(st(t[0])){for(var n=[],i=0;i<e;i++)n.push(Ti.call(t[i]));return n}return Ti.call(t)}return t}function Pi(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function Li(t){return 4===t||5===t}function Oi(t){return 1===t||2===t}var Ri=[0,0,0,0],Ni=(Ei.prototype.isFinished=function(){return this._finished},Ei.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},Ei.prototype.needsAnimate=function(){return 1<=this.keyframes.length},Ei.prototype.getAdditiveTrack=function(){return this._additiveTrack},Ei.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i,r=this.keyframes,o=r.length,a=!1,s=6,l=e,u=(st(e)?(1==(s=i=st((i=e)&&i[0])?2:1)&&!H(e[0])||2==i&&!H(e[0][0]))&&(a=!0):H(e)&&!xt(e)?s=0:V(e)?isNaN(+e)?(i=gi(e))&&(l=i,s=3):s=0:mt(e)&&((u=L({},l)).colorStops=B(e.colorStops,function(t){return{offset:t.offset,color:gi(t.color)}}),"linear"===e.type?s=4:"radial"===e.type&&(s=5),l=u),0===o?this.valType=s:s===this.valType&&6!==s||(a=!0),this.discrete=this.discrete||a,{time:t,value:l,rawValue:e,percent:0});return n&&(u.easing=n,u.easingFunc=k(n)?n:Cn[n]||jn(n)),r.push(u),u},Ei.prototype.prepare=function(t,e){for(var n=this.keyframes,i=(this._needsSort&&n.sort(function(t,e){return t.time-e.time}),this.valType),r=n.length,o=n[r-1],a=this.discrete,s=Oi(i),l=Li(i),u=0;u<r;u++){var h=n[u],c=h.value,p=o.value;if(h.percent=h.time/t,!a)if(s&&u!==r-1){x=_=v=m=y=g=f=d=h=void 0;var d=p,f=i,g=h=c,y=d;if(g.push&&y.push){var h=g.length,m=y.length;if(h!==m)if(m<h)g.length=m;else for(var v=h;v<m;v++)g.push(1===f?y[v]:Ti.call(y[v]));for(var _=g[0]&&g[0].length,v=0;v<g.length;v++)if(1===f)isNaN(g[v])&&(g[v]=y[v]);else for(var x=0;x<_;x++)isNaN(g[v][x])&&(g[v][x]=y[v][x])}}else if(l){T=M=S=b=w=h=d=void 0;for(var d=c.colorStops,h=p.colorStops,w=d.length,b=h.length,S=b<w?h:d,h=Math.min(w,b),M=S[h-1]||{color:[0,0,0,0],offset:0},T=h;T<Math.max(w,b);T++)S.push({offset:M.offset,color:M.color.slice()})}}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;for(var C=n[0].value,u=0;u<r;u++)0===i?n[u].additiveValue=n[u].value-C:3===i?n[u].additiveValue=ki([],n[u].value,C,-1):Oi(i)&&(n[u].additiveValue=(1===i?ki:Di)([],n[u].value,C,-1))}},Ei.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,o,a=null!=this._additiveTrack,s=a?"additiveValue":"value",l=this.valType,u=this.keyframes,h=u.length,c=this.propName,p=3===l,d=this._lastFr,f=Math.min;if(1===h)n=i=u[0];else{if(e<0)g=0;else if(e<this._lastFrP){for(var g=f(d+1,h-1);0<=g&&!(u[g].percent<=e);g--);g=f(g,h-2)}else{for(g=d;g<h&&!(u[g].percent>e);g++);g=f(g-1,h-2)}i=u[g+1],n=u[g]}n&&i&&(this._lastFr=g,this._lastFrP=e,d=i.percent-n.percent,r=0==d?1:f((e-n.percent)/d,1),i.easingFunc&&(r=i.easingFunc(r)),f=a?this._additiveValue:p?Ri:t[c],(Oi(l)||p)&&(f=f||(this._additiveValue=[])),this.discrete?t[c]=(r<1?n:i).rawValue:Oi(l)?(1===l?Ii:function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=Ci(e[a][s],n[a][s],i)}})(f,n[s],i[s],r):Li(l)?(d=n[s],o=i[s],t[c]={type:(l=4===l)?"linear":"radial",x:Ci(d.x,o.x,r),y:Ci(d.y,o.y,r),colorStops:B(d.colorStops,function(t,e){e=o.colorStops[e];return{offset:Ci(t.offset,e.offset,r),color:Pi(Ii([],t.color,e.color,r))}}),global:o.global},l?(t[c].x2=Ci(d.x2,o.x2,r),t[c].y2=Ci(d.y2,o.y2,r)):t[c].r=Ci(d.r,o.r,r)):p?(Ii(f,n[s],i[s],r),a||(t[c]=Pi(f))):(l=Ci(n[s],i[s],r),a?this._additiveValue=l:t[c]=l),a)&&this._addToTarget(t)}},Ei.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;0===e?t[n]=t[n]+i:3===e?(gi(t[n],Ri),ki(Ri,Ri,i,1),t[n]=Pi(Ri)):1===e?ki(t[n],t[n],i,1):2===e&&Di(t[n],t[n],i,1)},Ei);function Ei(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}Bi.prototype.getMaxTime=function(){return this._maxTime},Bi.prototype.getDelay=function(){return this._delay},Bi.prototype.getLoop=function(){return this._loop},Bi.prototype.getTarget=function(){return this._target},Bi.prototype.changeTarget=function(t){this._target=t},Bi.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,ht(e),n)},Bi.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o];if(!(l=r[a])){var s,l=r[a]=new Ni(a),u=void 0,h=this._getAdditiveTrack(a);if(h?(u=(s=(s=h.keyframes)[s.length-1])&&s.value,3===h.valType&&(u=u&&Pi(u))):u=this._target[a],null==u)continue;0<t&&l.addKeyframe(0,Ai(u),i),this._trackKeys.push(a)}l.addKeyframe(t,Ai(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},Bi.prototype.pause=function(){this._clip.pause(),this._paused=!0},Bi.prototype.resume=function(){this._clip.resume(),this._paused=!1},Bi.prototype.isPaused=function(){return!!this._paused},Bi.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},Bi.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},Bi.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},Bi.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},Bi.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},Bi.prototype.start=function(t){if(!(0<this._started)){this._started=1;for(var e,o=this,a=[],n=this._maxTime||0,i=0;i<this._trackKeys.length;i++){var r=this._trackKeys[i],s=this._tracks[r],r=this._getAdditiveTrack(r),l=s.keyframes,u=l.length;s.prepare(n,r),s.needsAnimate()&&(!this._allowDiscrete&&s.discrete?((r=l[u-1])&&(o._target[s.propName]=r.rawValue),s.setFinished()):a.push(s))}return a.length||this._force?(e=new Kn({life:n,loop:this._loop,delay:this._delay||0,onframe:function(t){o._started=2;var e=o._additiveAnimators;if(e){for(var n=!1,i=0;i<e.length;i++)if(e[i]._clip){n=!0;break}n||(o._additiveAnimators=null)}for(i=0;i<a.length;i++)a[i].step(o._target,t);var r=o._onframeCbs;if(r)for(i=0;i<r.length;i++)r[i](o._target,t)},ondestroy:function(){o._doneCallback()}}),this._clip=e,this.animation&&this.animation.addClip(e),t&&e.setEasing(t)):this._doneCallback(),this}},Bi.prototype.stop=function(t){var e;this._clip&&(e=this._clip,t&&e.onframe(1),this._abortedCallback())},Bi.prototype.delay=function(t){return this._delay=t,this},Bi.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},Bi.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},Bi.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},Bi.prototype.getClip=function(){return this._clip},Bi.prototype.getTrack=function(t){return this._tracks[t]},Bi.prototype.getTracks=function(){var e=this;return B(this._trackKeys,function(t){return e._tracks[t]})},Bi.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}for(var a=!0,r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},Bi.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];o&&!o.isFinished()&&(o=(o=o.keyframes)[n?0:o.length-1])&&(t[r]=Ai(o.rawValue))}}},Bi.prototype.__changeFinalValue=function(t,e){e=e||ht(t);for(var n=0;n<e.length;n++){var i,r=e[n],o=this._tracks[r];o&&1<(i=o.keyframes).length&&(i=i.pop(),o.addKeyframe(i.time,t[r]),o.prepare(this._maxTime,o.getAdditiveTrack()))}};var zi=Bi;function Bi(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,(this._loop=e)&&i?it("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}function Fi(){return(new Date).getTime()}u(Gi,Vi=le),Gi.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?((this._tail.next=t).prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},Gi.prototype.addAnimator=function(t){t.animation=this;t=t.getClip();t&&this.addClip(t)},Gi.prototype.removeClip=function(t){var e,n;t.animation&&(e=t.prev,n=t.next,e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null)},Gi.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},Gi.prototype.update=function(t){for(var e=Fi()-this._pausedTime,n=e-this._time,i=this._head;i;)var r=i.next,i=(i.step(e,n)&&(i.ondestroy(),this.removeClip(i)),r);this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},Gi.prototype._startLoop=function(){var e=this;this._running=!0,Tn(function t(){e._running&&(Tn(t),e._paused||e.update())})},Gi.prototype.start=function(){this._running||(this._time=Fi(),this._pausedTime=0,this._startLoop())},Gi.prototype.stop=function(){this._running=!1},Gi.prototype.pause=function(){this._paused||(this._pauseStart=Fi(),this._paused=!0)},Gi.prototype.resume=function(){this._paused&&(this._pausedTime+=Fi()-this._pauseStart,this._paused=!1)},Gi.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},Gi.prototype.isFinished=function(){return null==this._head},Gi.prototype.animate=function(t,e){e=e||{},this.start();t=new zi(t,e.loop);return this.addAnimator(t),t};var Vi,Hi=Gi;function Gi(t){var e=Vi.call(this)||this;return e._running=!1,e._time=0,e._pausedTime=0,e._pauseStart=0,e._paused=!1,e.stage=(t=t||{}).stage||{},e}var Wi,Ui=b.domSupported,Xi=(Wi={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:n=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:B(n,function(t){var e=t.replace("mouse","pointer");return Wi.hasOwnProperty(e)?e:t})}),Yi=["mousemove","mouseup"],qi=["pointermove","pointerup"],Zi=!1;function ji(t){t=t.pointerType;return"pen"===t||"touch"===t}function Ki(t){t&&(t.zrByTouch=!0)}function $i(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var Qi=function(t,e){this.stopPropagation=Ft,this.stopImmediatePropagation=Ft,this.preventDefault=Ft,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},Ji={mousedown:function(t){t=Ce(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=Ce(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=Ce(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){$i(this,(t=Ce(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){Zi=!0,t=Ce(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){Zi||(t=Ce(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){Ki(t=Ce(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Ji.mousemove.call(this,t),Ji.mousedown.call(this,t)},touchmove:function(t){Ki(t=Ce(this.dom,t)),this.handler.processGesture(t,"change"),Ji.mousemove.call(this,t)},touchend:function(t){Ki(t=Ce(this.dom,t)),this.handler.processGesture(t,"end"),Ji.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&Ji.click.call(this,t)},pointerdown:function(t){Ji.mousedown.call(this,t)},pointermove:function(t){ji(t)||Ji.mousemove.call(this,t)},pointerup:function(t){Ji.mouseup.call(this,t)},pointerout:function(t){ji(t)||Ji.mouseout.call(this,t)}},tr=(O(["click","dblclick","contextmenu"],function(e){Ji[e]=function(t){t=Ce(this.dom,t),this.trigger(e,t)}}),{pointermove:function(t){ji(t)||tr.mousemove.call(this,t)},pointerup:function(t){tr.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}});function er(i,r){var o=r.domHandlers;b.pointerEventsSupported?O(Xi.pointer,function(e){ir(r,e,function(t){o[e].call(i,t)})}):(b.touchEventsSupported&&O(Xi.touch,function(n){ir(r,n,function(t){var e;o[n].call(i,t),(e=r).touching=!0,null!=e.touchTimer&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)})}),O(Xi.mouse,function(e){ir(r,e,function(t){t=Te(t),r.touching||o[e].call(i,t)})}))}function nr(i,r){function t(n){ir(r,n,function(t){var e;t=Te(t),$i(i,t.target)||(e=t,t=Ce(i.dom,new Qi(i,e),!0),r.domHandlers[n].call(i,t))},{capture:!0})}b.pointerEventsSupported?O(qi,t):b.touchEventsSupported||O(Yi,t)}function ir(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,t.domTarget.addEventListener(e,n,i)}function rr(t){var e,n,i,r,o,a=t.mounted;for(e in a)a.hasOwnProperty(e)&&(n=t.domTarget,r=a[i=e],o=t.listenerOpts[e],n.removeEventListener(i,r,o));t.mounted={}}var or,ar=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},sr=(u(lr,or=le),lr.prototype.dispose=function(){rr(this._localHandlerScope),Ui&&rr(this._globalHandlerScope)},lr.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},lr.prototype.__togglePointerCapture=function(t){var e;this.__mayPointerCapture=null,Ui&&+this.__pointerCapturing^+t&&(this.__pointerCapturing=t,e=this._globalHandlerScope,t?nr(this,e):rr(e))},lr);function lr(t,e){var n=or.call(this)||this;return n.__pointerCapturing=!1,n.dom=t,n.painterRoot=e,n._localHandlerScope=new ar(t,Ji),Ui&&(n._globalHandlerScope=new ar(document,tr)),er(n,n._localHandlerScope),n}var n=1,ur=n=b.hasGlobalWindow?Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1):n,hr="#333",cr="#ccc",pr=Oe;function dr(t){return 5e-5<t||t<-5e-5}var fr=[],gr=[],yr=Le(),mr=Math.abs,vr=(_r.prototype.getLocalTransform=function(t){return _r.getLocalTransform(this,t)},_r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},_r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},_r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},_r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},_r.prototype.needLocalTransform=function(){return dr(this.rotation)||dr(this.x)||dr(this.y)||dr(this.scaleX-1)||dr(this.scaleY-1)||dr(this.skewX)||dr(this.skewY)},_r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||Le(),e?this.getLocalTransform(n):pr(n),t&&(e?Ne(n,t,n):Re(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&(pr(n),this.invTransform=null)},_r.prototype._resolveGlobalScaleRatio=function(t){var e,n,i=this.globalScaleRatio;null!=i&&1!==i&&(this.getGlobalScale(fr),n=((fr[1]-(n=fr[1]<0?-1:1))*i+n)/fr[1]||0,t[0]*=i=((fr[0]-(e=fr[0]<0?-1:1))*i+e)/fr[0]||0,t[1]*=i,t[2]*=n,t[3]*=n),this.invTransform=this.invTransform||Le(),Fe(this.invTransform,t)},_r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},_r.prototype.setLocalTransform=function(t){var e,n,i,r;t&&(r=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],e=Math.atan2(t[1],t[0]),n=Math.PI/2+e-Math.atan2(t[3],t[2]),i=Math.sqrt(i)*Math.cos(n),r=Math.sqrt(r),this.skewX=n,this.skewY=0,this.rotation=-e,this.x=+t[4],this.y=+t[5],this.scaleX=r,this.scaleY=i,this.originX=0,this.originY=0)},_r.prototype.decomposeTransform=function(){var t,e,n;this.transform&&(e=this.parent,t=this.transform,e&&e.transform&&(e.invTransform=e.invTransform||Le(),Ne(gr,e.invTransform,t),t=gr),e=this.originX,n=this.originY,(e||n)&&(yr[4]=e,yr[5]=n,Ne(gr,t,yr),gr[4]-=e,gr[5]-=n,t=gr),this.setLocalTransform(t))},_r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},_r.prototype.transformCoordToLocal=function(t,e){t=[t,e],e=this.invTransform;return e&&ee(t,t,e),t},_r.prototype.transformCoordToGlobal=function(t,e){t=[t,e],e=this.transform;return e&&ee(t,t,e),t},_r.prototype.getLineScale=function(){var t=this.transform;return t&&1e-10<mr(t[0]-1)&&1e-10<mr(t[3]-1)?Math.sqrt(mr(t[0]*t[3]-t[2]*t[1])):1},_r.prototype.copyTransform=function(t){for(var e=this,n=t,i=0;i<xr.length;i++){var r=xr[i];e[r]=n[r]}},_r.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,l=t.rotation||0,u=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,t=t.skewY?Math.tan(-t.skewY):0;return n||i||a||s?(e[4]=-(a=n+a)*r-c*(s=i+s)*o,e[5]=-s*o-t*a*r):e[4]=e[5]=0,e[0]=r,e[3]=o,e[1]=t*r,e[2]=c*o,l&&ze(e,e,l),e[4]+=n+u,e[5]+=i+h,e},_r.initDefaultProps=((n=_r.prototype).scaleX=n.scaleY=n.globalScaleRatio=1,void(n.x=n.y=n.originX=n.originY=n.skewX=n.skewY=n.rotation=n.anchorX=n.anchorY=0)),_r);function _r(){}var xr=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];var wr={};function br(t,e){var n=wr[e=e||K],i=(n=n||(wr[e]=new ei(500))).get(t);return null==i&&(i=G.measureText(t,e).width,n.put(t,i)),i}function Sr(t,e,n,i){t=br(t,e),e=Ir(e),n=Tr(0,t,n),i=Cr(0,e,i);return new X(n,i,t,e)}function Mr(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return Sr(r[0],e,n,i);for(var o=new X(0,0,0,0),a=0;a<r.length;a++){var s=Sr(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function Tr(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function Cr(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function Ir(t){return br("国",t)}function kr(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function Dr(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=kr(i[0],n.width),u+=kr(i[1],n.height),c=h=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var Ar,Pr="__zr_normal__",Lr=xr.concat(["ignore"]),Or=lt(xr,function(t,e){return t[e]=!0,t},{ignore:!1}),Rr={},Nr=new X(0,0,0,0),n=(i.prototype._init=function(t){this.attr(t)},i.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},i.prototype.beforeUpdate=function(){},i.prototype.afterUpdate=function(){},i.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},i.prototype.updateInnerText=function(t){var e,n,i,r,o,a,s,l,u,h,c=this._textContent;!c||c.ignore&&!t||(this.textConfig||(this.textConfig={}),l=(t=this.textConfig).local,i=n=void 0,r=!1,(e=c.innerTransformable).parent=l?this:null,h=!1,e.copyTransform(c),null!=t.position&&(u=Nr,t.layoutRect?u.copy(t.layoutRect):u.copy(this.getBoundingRect()),l||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Rr,t,u):Dr(Rr,t,u),e.x=Rr.x,e.y=Rr.y,n=Rr.align,i=Rr.verticalAlign,o=t.origin)&&null!=t.rotation&&(s=a=void 0,s="center"===o?(a=.5*u.width,.5*u.height):(a=kr(o[0],u.width),kr(o[1],u.height)),h=!0,e.originX=-e.x+a+(l?0:u.x),e.originY=-e.y+s+(l?0:u.y)),null!=t.rotation&&(e.rotation=t.rotation),(o=t.offset)&&(e.x+=o[0],e.y+=o[1],h||(e.originX=-o[0],e.originY=-o[1])),a=null==t.inside?"string"==typeof t.position&&0<=t.position.indexOf("inside"):t.inside,s=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),h=u=l=void 0,a&&this.canBeInsideText()?(l=t.insideFill,u=t.insideStroke,null!=l&&"auto"!==l||(l=this.getInsideTextFill()),null!=u&&"auto"!==u||(u=this.getInsideTextStroke(l),h=!0)):(l=t.outsideFill,u=t.outsideStroke,null!=l&&"auto"!==l||(l=this.getOutsideFill()),null!=u&&"auto"!==u||(u=this.getOutsideStroke(l),h=!0)),(l=l||"#000")===s.fill&&u===s.stroke&&h===s.autoStroke&&n===s.align&&i===s.verticalAlign||(r=!0,s.fill=l,s.stroke=u,s.autoStroke=h,s.align=n,s.verticalAlign=i,c.setDefaultTextStyle(s)),c.__dirty|=vn,r&&c.dirtyStyle(!0))},i.prototype.canBeInsideText=function(){return!0},i.prototype.getInsideTextFill=function(){return"#fff"},i.prototype.getInsideTextStroke=function(t){return"#000"},i.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?cr:hr},i.prototype.getOutsideStroke=function(t){for(var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&gi(e),i=(n=n||[255,255,255,1])[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,wi(n,"rgba")},i.prototype.traverse=function(t,e){},i.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},L(this.extra,e)):this[t]=e},i.prototype.hide=function(){this.ignore=!0,this.markRedraw()},i.prototype.show=function(){this.ignore=!1,this.markRedraw()},i.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(R(t))for(var n=ht(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},i.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;i.getLoop()||r&&r!==Pr||(r=(r=i.targetName)?e[r]:e,i.saveTo(r))}},i.prototype._innerSaveToNormal=function(t){var e=(e=this._normalState)||(this._normalState={});t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Lr)},i.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},i.prototype.hasState=function(){return 0<this.currentStates.length},i.prototype.getState=function(t){return this.states[t]},i.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},i.prototype.clearStates=function(t){this.useState(Pr,!1,t)},i.prototype.useState=function(t,e,n,i){var r=t===Pr,o=this.hasState();if(o||!r){var a,o=this.currentStates,s=this.stateTransition;if(!(0<=I(o,t))||!e&&1!==o.length){if((a=(a=this.stateProxy&&!r?this.stateProxy(t):a)||this.states&&this.states[t])||r)return r||this.saveCurrentToNormalState(a),(o=!!(a&&a.hoverLayer||i))&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,a,this._normalState,e,!n&&!this.__inHover&&s&&0<s.duration,s),i=this._textContent,s=this._textGuide,i&&i.useState(t,e,n,o),s&&s.useState(t,e,n,o),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!o&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~vn),a;it("State "+t+" not exists.")}}},i.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(!a){for(s=0;s<o;s++){var l=t[s],u=void 0;(u=(u=this.stateProxy?this.stateProxy(l,t):u)||this.states[l])&&i.push(u)}var h=i[o-1],h=!!(h&&h.hoverLayer||n),n=(h&&this._toggleHoverLayerFlag(!0),this._mergeStates(i)),c=this.stateTransition,n=(this.saveCurrentToNormalState(n),this._applyStateObj(t.join(","),n,this._normalState,!1,!e&&!this.__inHover&&c&&0<c.duration,c),this._textContent),c=this._textGuide;n&&n.useStates(t,e,h),c&&c.useStates(t,e,h),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~vn)}}else this.clearStates()},i.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},i.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},i.prototype.removeState=function(t){var e,t=I(this.currentStates,t);0<=t&&((e=this.currentStates.slice()).splice(t,1),this.useStates(e))},i.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),t=I(i,t),r=0<=I(i,e);0<=t?r?i.splice(t,1):i[t]=e:n&&!r&&i.push(e),this.useStates(i)},i.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},i.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];L(n,r),r.textConfig&&L(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},i.prototype._applyStateObj=function(t,e,n,i,r,o){for(var a=!(e&&i),s=(e&&e.textConfig?(this.textConfig=L({},(i?this:n).textConfig),L(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig),{}),l=!1,u=0;u<Lr.length;u++){var h=Lr[u],c=r&&Or[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],d=p.targetName;p.getLoop()||p.__changeFinalValue(d?(e||n)[d]:e||n)}l&&this._transitionState(t,s,o)},i.prototype._attachComponent=function(t){var e;t.__zr&&!t.__hostTarget||t!==this&&((e=this.__zr)&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this)},i.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},i.prototype.getClipPath=function(){return this._clipPath},i.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},i.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},i.prototype.getTextContent=function(){return this._textContent},i.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new vr,this._attachComponent(t),this._textContent=t,this.markRedraw())},i.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),L(this.textConfig,t),this.markRedraw()},i.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},i.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},i.prototype.getTextGuideLine=function(){return this._textGuide},i.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},i.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},i.prototype.markRedraw=function(){this.__dirty|=vn;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},i.prototype.dirty=function(){this.markRedraw()},i.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},i.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},i.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},i.prototype.animate=function(t,e,n){var i=t?this[t]:this,i=new zi(i,e,n);return t&&(i.targetName=t),this.addAnimator(i,t),i},i.prototype.addAnimator=function(n,t){var e=this.__zr,i=this;n.during(function(){i.updateDuringAnimation(t)}).done(function(){var t=i.animators,e=I(t,n);0<=e&&t.splice(e,1)}),this.animators.push(n),e&&e.animation.addAnimator(n),e&&e.wakeUp()},i.prototype.updateDuringAnimation=function(t){this.markRedraw()},i.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},i.prototype.animateTo=function(t,e,n){zr(this,t,e,n)},i.prototype.animateFrom=function(t,e,n){zr(this,t,e,n,!0)},i.prototype._transitionState=function(t,e,n,i){for(var r=zr(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},i.prototype.getBoundingRect=function(){return null},i.prototype.getPaintRect=function(){return null},i.initDefaultProps=((Ar=i.prototype).type="element",Ar.name="",Ar.ignore=Ar.silent=Ar.isGroup=Ar.draggable=Ar.dragging=Ar.ignoreClip=Ar.__inHover=!1,Ar.__dirty=vn,void(Object.defineProperty&&(Er("position","_legacyPos","x","y"),Er("scale","_legacyScale","scaleX","scaleY"),Er("origin","_legacyOrigin","originX","originY")))),i);function i(t){this.id=et++,this.animators=[],this.currentStates=[],this.states={},this._init(t)}function Er(t,e,n,i){function r(e,t){Object.defineProperty(t,0,{get:function(){return e[n]},set:function(t){e[n]=t}}),Object.defineProperty(t,1,{get:function(){return e[i]},set:function(t){e[i]=t}})}Object.defineProperty(Ar,t,{get:function(){var t;return this[e]||(t=this[e]=[],r(this,t)),this[e]},set:function(t){this[n]=t[0],this[i]=t[1],this[e]=t,r(this,t)}})}function zr(t,e,n,i,r){function o(){u=!0,--l<=0&&(u?h&&h():c&&c())}function a(){--l<=0&&(u?h&&h():c&&c())}var s=[],l=(!function t(e,n,i,r,o,a,s,l){var u=ht(r);var h=o.duration;var c=o.delay;var p=o.additive;var d=o.setToFinal;var f=!R(a);var g=e.animators;var y=[];for(var m=0;m<u.length;m++){var v=u[m],_=r[v];null!=_&&null!=i[v]&&(f||a[v])?!R(_)||st(_)||mt(_)?y.push(v):n?l||(i[v]=_,e.updateDuringAnimation(n)):t(e,v,i[v],_,o,a&&a[v],s,l):l||(i[v]=_,e.updateDuringAnimation(n),y.push(v))}var x=y.length;if(!p&&x)for(var w,b=0;b<g.length;b++)(S=g[b]).targetName===n&&S.stopTracks(y)&&(w=I(g,S),g.splice(w,1));o.force||(y=ut(y,function(t){return!Vr(r[t],i[t])}),x=y.length);if(0<x||o.force&&!s.length){var S,M=void 0,T=void 0,C=void 0;if(l){T={},d&&(M={});for(b=0;b<x;b++){v=y[b];T[v]=i[v],d?M[v]=r[v]:i[v]=r[v]}}else if(d){C={};for(b=0;b<x;b++){v=y[b];C[v]=Ai(i[v]),Fr(i,r,v)}}(S=new zi(i,!1,!1,p?ut(g,function(t){return t.targetName===n}):null)).targetName=n,o.scope&&(S.scope=o.scope),d&&M&&S.whenWithKeys(0,M,y),C&&S.whenWithKeys(0,C,y),S.whenWithKeys(null==h?500:h,l?T:r,y).delay(c||0),e.addAnimator(S,n),s.push(S)}}(t,"",t,e,n=n||{},i,s,r),s.length),u=!1,h=n.done,c=n.aborted;l||h&&h(),0<s.length&&n.during&&s[0].during(function(t,e){n.during(e)});for(var p=0;p<s.length;p++){var d=s[p];d.done(o),d.aborted(a),n.force&&d.duration(n.duration),d.start(n.easing)}return s}function Br(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function Fr(t,e,n){if(st(e[n]))if(st(t[n])||(t[n]=[]),gt(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),Br(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(st(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?Br(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else Br(o,r,a);o.length=r.length}else t[n]=e[n]}function Vr(t,e){return t===e||st(t)&&st(e)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}(t,e)}at(n,le),at(n,vr);u(Wr,Hr=n),Wr.prototype.childrenRef=function(){return this._children},Wr.prototype.children=function(){return this._children.slice()},Wr.prototype.childAt=function(t){return this._children[t]},Wr.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},Wr.prototype.childCount=function(){return this._children.length},Wr.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},Wr.prototype.addBefore=function(t,e){var n;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&0<=(e=(n=this._children).indexOf(e))&&(n.splice(e,0,t),this._doAdd(t)),this},Wr.prototype.replace=function(t,e){t=I(this._children,t);return 0<=t&&this.replaceAt(e,t),this},Wr.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];return t&&t!==this&&t.parent!==this&&t!==i&&(n[e]=t,i.parent=null,(n=this.__zr)&&i.removeSelfFromZr(n),this._doAdd(t)),this},Wr.prototype._doAdd=function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},Wr.prototype.remove=function(t){var e=this.__zr,n=this._children,i=I(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},Wr.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},Wr.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},Wr.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},Wr.prototype.addSelfToZr=function(t){Hr.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].addSelfToZr(t)},Wr.prototype.removeSelfFromZr=function(t){Hr.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].removeSelfFromZr(t)},Wr.prototype.getBoundingRect=function(t){for(var e=new X(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a,s=n[o];s.ignore||s.invisible||(a=s.getBoundingRect(),(s=s.getLocalTransform(i))?(X.applyTransform(e,a,s),(r=r||e.clone()).union(e)):(r=r||a.clone()).union(a))}return r||e};var Hr,Gr=Wr;function Wr(t){var e=Hr.call(this)||this;return e.isGroup=!0,e._children=[],e.attr(t),e}Gr.prototype.type="group";var Ur={},Xr={};Zr.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},Zr.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},Zr.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},Zr.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(t){if("string"==typeof t)return bi(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=bi(e[r].color,1);return(n/=i)<.4}}return!1}(t))},Zr.prototype.getBackgroundColor=function(){return this._backgroundColor},Zr.prototype.setDarkMode=function(t){this._darkMode=t},Zr.prototype.isDarkMode=function(){return this._darkMode},Zr.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},Zr.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},Zr.prototype.flush=function(){this._disposed||this._flush(!1)},Zr.prototype._flush=function(t){var e,n=Fi(),t=(this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately()),Fi());e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:t-n})):0<this._sleepAfterStill&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill)&&this.animation.stop()},Zr.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},Zr.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},Zr.prototype.refreshHover=function(){this._needsRefreshHover=!0},Zr.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},Zr.prototype.resize=function(t){this._disposed||(this.painter.resize((t=t||{}).width,t.height),this.handler.resize())},Zr.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},Zr.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},Zr.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},Zr.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},Zr.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},Zr.prototype.on=function(t,e,n){return this._disposed||this.handler.on(t,e,n),this},Zr.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},Zr.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},Zr.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Gr&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},Zr.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete Xr[t])};var Yr,qr=Zr;function Zr(t,e,n){var i,r=this,o=(this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t,new Sn),a=n.renderer||"canvas",a=(Ur[a]||(a=ht(Ur)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect,new Ur[a](e,o,n,t)),e=n.ssr||a.ssrOnly,t=(this.storage=o,this.painter=a,b.node||b.worker||e?null:new sr(a.getViewportRoot(),a.root)),s=n.useCoarsePointer;(null==s||"auto"===s?b.touchEventsSupported:!!s)&&(i=N(n.pointerSize,44)),this.handler=new an(o,a,t,a.root,i),this.animation=new Hi({stage:{update:e?null:function(){return r._flush(!0)}}}),e||this.animation.start()}function jr(t,e){t=new qr(et++,t,e);return Xr[t.id]=t}function Kr(t,e){Ur[t]=e}function $r(t){Yr=t}var Qr=Object.freeze({__proto__:null,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in Xr)Xr.hasOwnProperty(t)&&Xr[t].dispose();Xr={}},getElementSSRData:function(t){if("function"==typeof Yr)return Yr(t)},getInstance:function(t){return Xr[t]},init:jr,registerPainter:Kr,registerSSRDataGetter:$r,version:"5.6.1"}),Jr=20;function to(t,e,n,i){var r=e[0],e=e[1],o=n[0],n=n[1],a=e-r,s=n-o;if(0==a)return 0==s?o:(o+n)/2;if(i)if(0<a){if(t<=r)return o;if(e<=t)return n}else{if(r<=t)return o;if(t<=e)return n}else{if(t===r)return o;if(t===e)return n}return(t-r)/a*s+o}function eo(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return V(t)?t.replace(/^\s+|\s+$/g,"").match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function no(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),Jr),t=(+t).toFixed(e),n?t:+t}function io(t){if(t=+t,isNaN(t))return 0;if(1e-14<t)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return ro(t)}function ro(t){var t=t.toString().toLowerCase(),e=t.indexOf("e"),n=0<e?+t.slice(e+1):0,e=0<e?e:t.length,t=t.indexOf(".");return Math.max(0,(t<0?0:e-1-t)-n)}function oo(t,e){var n=Math.log,i=Math.LN10,t=Math.floor(n(t[1]-t[0])/i),n=Math.round(n(Math.abs(e[1]-e[0]))/i),e=Math.min(Math.max(-t+n,0),20);return isFinite(e)?e:20}function ao(t){var e=2*Math.PI;return(t%e+e)%e}function so(t){return-1e-4<t&&t<1e-4}var lo=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function uo(t){var e,n;return t instanceof Date?t:V(t)?(e=lo.exec(t))?e[8]?(n=+e[4]||0,"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))):new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0):new Date(NaN):null==t?new Date(NaN):new Date(Math.round(t))}function ho(t){return Math.pow(10,co(t))}function co(t){var e;return 0===t?0:(e=Math.floor(Math.log(t)/Math.LN10),10<=t/Math.pow(10,e)&&e++,e)}function po(t,e){var n=co(t),i=Math.pow(10,n),r=t/i,e=e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10;return t=e*i,-20<=n?+t.toFixed(n<0?-n:0):t}function fo(t){var e=parseFloat(t);return e==t&&(0!==e||!V(t)||t.indexOf("x")<=0)?e:NaN}function go(t){return!isNaN(fo(t))}function yo(){return Math.round(9*Math.random())}function mo(t,e){return null==t?e:null==e?t:t*e/function t(e,n){return 0===n?e:t(n,e%n)}(t,e)}function f(t){throw new Error(t)}function vo(t,e,n){return(e-t)*n+t}var _o="series\0";function xo(t){return t instanceof Array?t:null==t?[]:[t]}function wo(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var bo=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function So(t){return!R(t)||F(t)||t instanceof Date?t:t.value}function Mo(t,n,e){var o,a,s,l,r,u,i,h,c,p,d="normalMerge"===e,f="replaceMerge"===e,g="replaceAll"===e,y=(t=t||[],n=(n||[]).slice(),E()),e=(O(n,function(t,e){R(t)||(n[e]=null)}),function(t,e,n){var i=[];if("replaceAll"!==n)for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||Do(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,y,e));return(d||f)&&(o=e,a=t,s=y,O(l=n,function(t,e){var n,i,r;t&&null!=t.id&&(n=Co(t.id),null!=(i=s.get(n)))&&(Tt(!(r=o[i]).newOption,'Duplicated option on id "'+n+'".'),r.newOption=t,r.existing=a[i],l[e]=null)})),d&&(r=e,O(u=n,function(t,e){if(t&&null!=t.name)for(var n=0;n<r.length;n++){var i=r[n].existing;if(!r[n].newOption&&i&&(null==i.id||null==t.id)&&!Do(t)&&!Do(i)&&To("name",i,t))return r[n].newOption=t,void(u[e]=null)}})),d||f?(h=e,c=f,O(n,function(t){if(t){for(var e,n=0;(e=h[n])&&(e.newOption||Do(e.existing)||e.existing&&null!=t.id&&!To("id",t,e.existing));)n++;e?(e.newOption=t,e.brandNew=c):h.push({newOption:t,brandNew:c,existing:null,keyInfo:null}),n++}})):g&&(i=e,O(n,function(t){i.push({newOption:t,brandNew:!0,existing:null,keyInfo:null})})),t=e,p=E(),O(t,function(t){var e=t.existing;e&&p.set(e.id,t)}),O(t,function(t){var e=t.newOption;Tt(!e||null==e.id||!p.get(e.id)||p.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&p.set(e.id,t),t.keyInfo||(t.keyInfo={})}),O(t,function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(R(i)){if(r.name=null!=i.name?Co(i.name):n?n.name:_o+e,n)r.id=Co(n.id);else if(null!=i.id)r.id=Co(i.id);else for(var o=0;r.id="\0"+r.name+"\0"+o++,p.get(r.id););p.set(r.id,t)}}),e}function To(t,e,n){e=Io(e[t],null),n=Io(n[t],null);return null!=e&&null!=n&&e===n}function Co(t){return Io(t,"")}function Io(t,e){return null==t?e:V(t)?t:H(t)||dt(t)?t+"":e}function ko(t){t=t.name;return!(!t||!t.indexOf(_o))}function Do(t){return t&&null!=t.id&&0===Co(t.id).indexOf("\0_ec_\0")}function Ao(t,r,o){O(t,function(t){var e,n,i=t.newOption;R(i)&&(t.keyInfo.mainType=r,t.keyInfo.subType=(e=r,i=i,t=t.existing,n=o,i.type||(t?t.subType:n.determineSubType(e,i))))})}function Po(e,t){return null!=t.dataIndexInside?t.dataIndexInside:null!=t.dataIndex?F(t.dataIndex)?B(t.dataIndex,function(t){return e.indexOfRawIndex(t)}):e.indexOfRawIndex(t.dataIndex):null!=t.name?F(t.name)?B(t.name,function(t){return e.indexOfName(t)}):e.indexOfName(t.name):void 0}function Lo(){var e="__ec_inner_"+Oo++;return function(t){return t[e]||(t[e]={})}}var Oo=yo();function Ro(n,t,i){var t=No(t,i),e=t.mainTypeSpecified,r=t.queryOptionMap,o=t.others,a=i?i.defaultMainType:null;return!e&&a&&r.set(a,{}),r.each(function(t,e){t=zo(n,e,t,{useDefault:a===e,enableAll:!i||null==i.enableAll||i.enableAll,enableNone:!i||null==i.enableNone||i.enableNone});o[e+"Models"]=t.models,o[e+"Model"]=t.models[0]}),o}function No(t,i){var e=V(t)?((e={})[t+"Index"]=0,e):t,r=E(),o={},a=!1;return O(e,function(t,e){var n;"dataIndex"===e||"dataIndexInside"===e?o[e]=t:(n=(e=e.match(/^(\w+)(Index|Id|Name)$/)||[])[1],e=(e[2]||"").toLowerCase(),!n||!e||i&&i.includeMainTypes&&I(i.includeMainTypes,n)<0||(a=a||!!n,(r.get(n)||r.set(n,{}))[e]=t))}),{mainTypeSpecified:a,queryOptionMap:r,others:o}}var Eo={useDefault:!0,enableAll:!1,enableNone:!1};function zo(t,e,n,i){i=i||Eo;var r=n.index,o=n.id,n=n.name,a={models:null,specified:null!=r||null!=o||null!=n};return a.specified?"none"===r||!1===r?(Tt(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),a.models=[]):("all"===r&&(Tt(i.enableAll,'`"all"` is not a valid value on index option.'),r=o=n=null),a.models=t.queryComponents({mainType:e,index:r,id:o,name:n})):(r=void 0,a.models=i.useDefault&&(r=t.getComponent(e))?[r]:[]),a}function Bo(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function Fo(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if(H(i))return no(p=vo(n||0,i,r),o?Math.max(io(n||0),io(i)):e);if(V(i))return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c,p,d=t.getDimensionInfo(h);d&&"ordinal"===d.type?a[h]=(r<1&&s?s:l)[h]:(p=vo(d=s&&s[h]?s[h]:0,c=l[h],r),a[h]=no(p,o?Math.max(io(d),io(c)):e))}return a}var Vo=".",Ho="___EC__COMPONENT__CONTAINER___",Go="___EC__EXTENDED_CLASS___";function Wo(t){var e={main:"",sub:""};return t&&(t=t.split(Vo),e.main=t[0]||"",e.sub=t[1]||""),e}function Uo(t){(t.$constructor=t).extend=function(t){var e,n,i,r=this;function o(){return n.apply(this,arguments)||this}return k(i=r)&&/^class\s/.test(Function.prototype.toString.call(i))?(u(o,n=r),e=o):ot(e=function(){(t.$constructor||r).apply(this,arguments)},this),L(e.prototype,t),e[Go]=!0,e.extend=this.extend,e.superCall=qo,e.superApply=Zo,e.superClass=r,e}}function Xo(t,e){t.extend=e.extend}var Yo=Math.round(10*Math.random());function qo(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function Zo(t,e,n){return this.superClass.prototype[e].apply(t,n)}function jo(t){var r={};t.registerClass=function(t){var e,n=t.type||t.prototype.type;return n&&(Tt(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(e=n),'componentType "'+e+'" illegal'),(e=Wo(t.prototype.type=n)).sub?e.sub!==Ho&&(function(t){var e=r[t.main];e&&e[Ho]||(e=r[t.main]={___EC__COMPONENT__CONTAINER___:!0});return e}(e)[e.sub]=t):r[e.main]=t),t},t.getClass=function(t,e,n){var i=r[t];if(i&&i[Ho]&&(i=e?i[e]:null),n&&!i)throw new Error(e?"Component "+t+"."+(e||"")+" is used but not imported.":t+".type should be specified.");return i},t.getClassesByMainType=function(t){var t=Wo(t),n=[],t=r[t.main];return t&&t[Ho]?O(t,function(t,e){e!==Ho&&n.push(t)}):n.push(t),n},t.hasClass=function(t){t=Wo(t);return!!r[t.main]},t.getAllClassMainTypes=function(){var n=[];return O(r,function(t,e){n.push(e)}),n},t.hasSubTypes=function(t){t=Wo(t),t=r[t.main];return t&&t[Ho]}}function Ko(a,s){for(var t=0;t<a.length;t++)a[t][1]||(a[t][1]=a[t][0]);return s=s||!1,function(t,e,n){for(var i={},r=0;r<a.length;r++){var o=a[r][1];e&&0<=I(e,o)||n&&I(n,o)<0||null!=(o=t.getShallow(o,s))&&(i[a[r][0]]=o)}return i}}var $o=Ko([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),Qo=(Jo.prototype.getAreaStyle=function(t,e){return $o(this,t,e)},Jo);function Jo(){}var ta=new ei(50);function ea(t,e,n,i,r){return t?"string"==typeof t?(e&&e.__zrImageSrc===t||!n||(n={hostEl:n,cb:i,cbPayload:r},(i=ta.get(t))?ia(e=i.image)||i.pending.push(n):((e=G.loadImage(t,na,na)).__zrImageSrc=t,ta.put(t,e.__cachedImgObj={image:e,pending:[n]}))),e):t:e}function na(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function ia(t){return t&&t.width&&t.height}var ra=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function oa(t,e,n,i,r,o){if(n){for(var a=(e+"").split("\n"),s=(o=aa(n,i,r,o),!1),l={},u=0,h=a.length;u<h;u++)sa(l,a[u],o),a[u]=l.textLine,s=s||l.isTruncated;t.text=a.join("\n"),t.isTruncated=s}else t.text="",t.isTruncated=!1}function aa(t,e,n,i){for(var r=L({},i=i||{}),o=(r.font=e,n=N(n,"..."),r.maxIterations=N(i.maxIterations,2),r.minChar=N(i.minChar,0)),a=(r.cnCharWidth=br("国",e),r.ascCharWidth=br("a",e)),s=(r.placeholder=N(i.placeholder,""),t=Math.max(0,t-1)),l=0;l<o&&a<=s;l++)s-=a;i=br(n,e);return s<i&&(n="",i=0),s=t-i,r.ellipsis=n,r.ellipsisWidth=i,r.contentWidth=s,r.containerWidth=t,r}function sa(t,e,n){var i=n.containerWidth,r=n.font,o=n.contentWidth;if(i)if((l=br(e,r))<=i)t.textLine=e,t.isTruncated=!1;else{for(var a=0;;a++){if(l<=o||a>=n.maxIterations){e+=n.ellipsis;break}var s=0===a?function(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}(e,o,n.ascCharWidth,n.cnCharWidth):0<l?Math.floor(e.length*o/l):0,l=br(e=e.substr(0,s),r)}""===e&&(e=n.placeholder),t.textLine=e,t.isTruncated=!0}else t.textLine="",t.isTruncated=!1}var la=function(){},ua=function(t){this.tokens=[],t&&(this.tokens=t)},ha=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1};function ca(t,e){var n=new ha;if(null!=t&&(t+=""),t){for(var i,r=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==r?null:{width:r,accumWidth:0,breakAll:"breakAll"===a},l=ra.lastIndex=0;null!=(i=ra.exec(t));){var u=i.index;l<u&&pa(n,t.substring(l,u),e,s),pa(n,i[2],e,s,i[1]),l=ra.lastIndex}l<t.length&&pa(n,t.substring(l,t.length),e,s);var h,c=[],p=0,d=0,f=e.padding,g="truncate"===a,y="truncate"===e.lineOverflow,m={};t:for(var v=0;v<n.lines.length;v++){for(var _=n.lines[v],x=0,w=0,b=0;b<_.tokens.length;b++){var S=(A=_.tokens[b]).styleName&&e.rich[A.styleName]||{},M=A.textPadding=S.padding,T=M?M[1]+M[3]:0,C=A.font=S.font||e.font,I=(A.contentHeight=Ir(C),N(S.height,A.contentHeight));if(A.innerHeight=I,M&&(I+=M[0]+M[2]),A.height=I,A.lineHeight=bt(S.lineHeight,e.lineHeight,I),A.align=S&&S.align||e.align,A.verticalAlign=S&&S.verticalAlign||"middle",y&&null!=o&&p+A.lineHeight>o){M=n.lines.length;0<b?(_.tokens=_.tokens.slice(0,b),L(_,w,x),n.lines=n.lines.slice(0,v+1)):n.lines=n.lines.slice(0,v),n.isTruncated=n.isTruncated||n.lines.length<M;break t}var k,M=S.width,D=null==M||"auto"===M;"string"==typeof M&&"%"===M.charAt(M.length-1)?(A.percentWidth=M,c.push(A),A.contentWidth=br(A.text,C)):(D&&(M=(M=S.backgroundColor)&&M.image)&&(k=void 0,ia(M="string"==typeof(h=M)?(k=ta.get(h))&&k.image:h))&&(A.width=Math.max(A.width,M.width*I/M.height)),null!=(k=g&&null!=r?r-w:null)&&k<A.width?!D||k<T?(A.text="",A.width=A.contentWidth=0):(oa(m,A.text,k-T,C,e.ellipsis,{minChar:e.truncateMinChar}),A.text=m.text,n.isTruncated=n.isTruncated||m.isTruncated,A.width=A.contentWidth=br(A.text,C)):A.contentWidth=br(A.text,C)),A.width+=T,w+=A.width,S&&(x=Math.max(x,A.lineHeight))}L(_,w,x)}n.outerWidth=n.width=N(r,d),n.outerHeight=n.height=N(o,p),n.contentHeight=p,n.contentWidth=d,f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]);for(v=0;v<c.length;v++){var A,P=(A=c[v]).percentWidth;A.width=parseInt(P,10)/100*n.width}}return n;function L(t,e,n){t.width=e,t.lineHeight=n,p+=n,d=Math.max(d,e)}}function pa(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;i?(n=(t=l.padding)?t[1]+t[3]:0,null!=l.width&&"auto"!==l.width?(t=kr(l.width,i.width)+n,0<u.length&&t+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=t):(t=fa(e,h,i.width,i.breakAll,i.accumWidth),i.accumWidth=t.accumWidth+n,a=t.linesWidths,o=t.lines)):o=e.split("\n");for(var p=0;p<o.length;p++){var d,f,g=o[p],y=new la;y.styleName=r,y.text=g,y.isLineHolder=!g&&!s,"number"==typeof l.width?y.width=l.width:y.width=a?a[p]:br(g,h),p||c?u.push(new ua([y])):1===(f=(d=(u[u.length-1]||(u[0]=new ua)).tokens).length)&&d[0].isLineHolder?d[0]=y:!g&&f&&!s||d.push(y)}}var da=lt(",&?/;] ".split(""),function(t,e){return t[e]=!0,t},{});function fa(t,e,n,i,r){for(var o,a=[],s=[],l="",u="",h=0,c=0,p=0;p<t.length;p++){var d,f,g=t.charAt(p);"\n"===g?(u&&(l+=u,c+=h),a.push(l),s.push(c),u=l="",c=h=0):(d=br(g,e),f=!(i||(f=void 0,!(32<=(f=(f=o=g).charCodeAt(0))&&f<=591||880<=f&&f<=4351||4608<=f&&f<=5119||7680<=f&&f<=8303))||!!da[o]),(a.length?n<c+d:n<r+c+d)?c?(l||u)&&(c=f?(l||(l=u,u="",c=h=0),a.push(l),s.push(c-h),u+=g,l="",h+=d):(u&&(l+=u,u="",h=0),a.push(l),s.push(c),l=g,d)):f?(a.push(u),s.push(h),u=g,h=d):(a.push(g),s.push(d)):(c+=d,f?(u+=g,h+=d):(u&&(l+=u,u="",h=0),l+=g)))}return a.length||l||(l=t,u="",h=0),u&&(l+=u),l&&(a.push(l),s.push(c)),1===a.length&&(c+=r),{accumWidth:c,lines:a,linesWidths:s}}var ga,ya="__zr_style_"+Math.round(10*Math.random()),ma={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},va={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}},_a=(ma[ya]=!0,["z","z2","invisible"]),xa=["invisible"],n=(u(r,ga=n),r.prototype._init=function(t){for(var e=ht(t),n=0;n<e.length;n++){var i=e[n];"style"===i?this.useStyle(t[i]):ga.prototype.attrKV.call(this,i,t[i])}this.style||this.useStyle({})},r.prototype.beforeBrush=function(){},r.prototype.afterBrush=function(){},r.prototype.innerBeforeBrush=function(){},r.prototype.innerAfterBrush=function(){},r.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){wa.copy(t.getBoundingRect()),t.transform&&wa.applyTransform(t.transform);return ba.width=e,ba.height=n,!wa.intersect(ba)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},r.prototype.contain=function(t,e){return this.rectContain(t,e)},r.prototype.traverse=function(t,e){t.call(e,this)},r.prototype.rectContain=function(t,e){t=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(t[0],t[1])},r.prototype.getPaintRect=function(){var t,e,n,i,r,o=this._paintRect;return this._paintRect&&!this.__dirty||(r=this.transform,t=this.getBoundingRect(),e=(i=this.style).shadowBlur||0,n=i.shadowOffsetX||0,i=i.shadowOffsetY||0,o=this._paintRect||(this._paintRect=new X(0,0,0,0)),r?X.applyTransform(o,t,r):o.copy(t),(e||n||i)&&(o.width+=2*e+Math.abs(n),o.height+=2*e+Math.abs(i),o.x=Math.min(o.x,o.x+n-e),o.y=Math.min(o.y,o.y+i-e)),r=this.dirtyRectTolerance,o.isZero())||(o.x=Math.floor(o.x-r),o.y=Math.floor(o.y-r),o.width=Math.ceil(o.width+1+2*r),o.height=Math.ceil(o.height+1+2*r)),o},r.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new X(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},r.prototype.getPrevPaintRect=function(){return this._prevPaintRect},r.prototype.animateStyle=function(t){return this.animate("style",t)},r.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},r.prototype.attrKV=function(t,e){"style"!==t?ga.prototype.attrKV.call(this,t,e):this.style?this.setStyle(e):this.useStyle(e)},r.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:L(this.style,t),this.dirtyStyle(),this},r.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},r.prototype.dirty=function(){this.dirtyStyle()},r.prototype.styleChanged=function(){return!!(2&this.__dirty)},r.prototype.styleUpdated=function(){this.__dirty&=-3},r.prototype.createStyle=function(t){return Et(ma,t)},r.prototype.useStyle=function(t){t[ya]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},r.prototype.isStyleObject=function(t){return t[ya]},r.prototype._innerSaveToNormal=function(t){ga.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.style&&!e.style&&(e.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,e,_a)},r.prototype._applyStateObj=function(t,e,n,i,r,o){ga.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.style?r?i?a=e.style:(a=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(a,e.style)):(a=this._mergeStyle(this.createStyle(),(i?this:n).style),this._mergeStyle(a,e.style)):s&&(a=n.style),a)if(r){var l=this.style;if(this.style=this.createStyle(s?{}:l),s)for(var u=ht(l),h=0;h<u.length;h++)(p=u[h])in a&&(a[p]=a[p],this.style[p]=l[p]);for(var c=ht(a),h=0;h<c.length;h++){var p=c[h];this.style[p]=this.style[p]}this._transitionState(t,{style:a},o,this.getAnimationStyleProps())}else this.useStyle(a);for(var d=this.__inHover?xa:_a,h=0;h<d.length;h++){p=d[h];e&&null!=e[p]?this[p]=e[p]:s&&null!=n[p]&&(this[p]=n[p])}},r.prototype._mergeStates=function(t){for(var e,n=ga.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.style&&this._mergeStyle(e=e||{},r.style)}return e&&(n.style=e),n},r.prototype._mergeStyle=function(t,e){return L(t,e),t},r.prototype.getAnimationStyleProps=function(){return va},r.initDefaultProps=((n=r.prototype).type="displayable",n.invisible=!1,n.z=0,n.z2=0,n.zlevel=0,n.culling=!1,n.cursor="pointer",n.rectHover=!1,n.incremental=!1,n._rect=null,n.dirtyRectTolerance=0,void(n.__dirty=2|vn)),r);function r(t){return ga.call(this,t)||this}var wa=new X(0,0,0,0),ba=new X(0,0,0,0);var Sa=Math.min,Ma=Math.max,Ta=Math.sin,Ca=Math.cos,Ia=2*Math.PI,ka=Gt(),Da=Gt(),Aa=Gt();function Pa(t,e,n,i,r,o){r[0]=Sa(t,n),r[1]=Sa(e,i),o[0]=Ma(t,n),o[1]=Ma(e,i)}var La=[],Oa=[];var Y={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Ra=[],Na=[],Ea=[],za=[],Ba=[],Fa=[],Va=Math.min,Ha=Math.max,Ga=Math.cos,Wa=Math.sin,Ua=Math.abs,Xa=Math.PI,Ya=2*Xa,qa="undefined"!=typeof Float32Array,Za=[];function ja(t){return Math.round(t/Xa*1e8)/1e8%2*Xa}o.prototype.increaseVersion=function(){this._version++},o.prototype.getVersion=function(){return this._version},o.prototype.setScale=function(t,e,n){0<(n=n||0)&&(this._ux=Ua(n/ur/t)||0,this._uy=Ua(n/ur/e)||0)},o.prototype.setDPR=function(t){this.dpr=t},o.prototype.setContext=function(t){this._ctx=t},o.prototype.getContext=function(){return this._ctx},o.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},o.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},o.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Y.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},o.prototype.lineTo=function(t,e){var n=Ua(t-this._xi),i=Ua(e-this._yi),r=n>this._ux||i>this._uy;return this.addData(Y.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r?(this._xi=t,this._yi=e,this._pendingPtDist=0):(r=n*n+i*i)>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=r),this},o.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(Y.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},o.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(Y.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},o.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),Za[0]=i,Za[1]=r,s=o,(l=ja((a=Za)[0]))<0&&(l+=Ya),h=l-a[0],u=a[1],u+=h,!s&&Ya<=u-l?u=l+Ya:s&&Ya<=l-u?u=l-Ya:!s&&u<l?u=l+(Ya-ja(l-u)):s&&l<u&&(u=l-(Ya-ja(u-l))),a[0]=l,a[1]=u;var a,s,l,u,h=(r=Za[1])-(i=Za[0]);return this.addData(Y.A,t,e,n,n,i,h,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=Ga(r)*n+t,this._yi=Wa(r)*n+e,this},o.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},o.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Y.R,t,e,n,i),this},o.prototype.closePath=function(){this._drawPendingPt(),this.addData(Y.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},o.prototype.fill=function(t){t&&t.fill(),this.toStatic()},o.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},o.prototype.len=function(){return this._len},o.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!qa||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},o.prototype.appendPath=function(t){for(var e=(t=t instanceof Array?t:[t]).length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();qa&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},o.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},o.prototype._drawPendingPt=function(){0<this._pendingPtDist&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},o.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},o.prototype.toStatic=function(){var t;this._saveData&&(this._drawPendingPt(),(t=this.data)instanceof Array)&&(t.length=this._len,qa)&&11<this._len&&(this.data=new Float32Array(t))},o.prototype.getBoundingRect=function(){Ea[0]=Ea[1]=Ba[0]=Ba[1]=Number.MAX_VALUE,za[0]=za[1]=Fa[0]=Fa[1]=-Number.MAX_VALUE;for(var t,e=this.data,n=0,i=0,r=0,o=0,a=0;a<this._len;){var E=e[a++],z=1===a;switch(z&&(r=n=e[a],o=i=e[a+1]),E){case Y.M:n=r=e[a++],i=o=e[a++],Ba[0]=r,Ba[1]=o,Fa[0]=r,Fa[1]=o;break;case Y.L:Pa(n,i,e[a],e[a+1],Ba,Fa),n=e[a++],i=e[a++];break;case Y.C:G=H=m=y=V=g=f=d=p=c=F=B=h=u=l=s=void 0;var s=n,l=i,u=e[a++],h=e[a++],B=e[a++],F=e[a++],c=e[a],p=e[a+1],d=Ba,f=Fa,g=Vn,V=zn,y=g(s,u,B,c,La);d[0]=1/0,d[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var m=0;m<y;m++){var H=V(s,u,B,c,La[m]);d[0]=Sa(H,d[0]),f[0]=Ma(H,f[0])}for(y=g(l,h,F,p,Oa),m=0;m<y;m++){var G=V(l,h,F,p,Oa[m]);d[1]=Sa(G,d[1]),f[1]=Ma(G,f[1])}d[0]=Sa(s,d[0]),f[0]=Ma(s,f[0]),d[0]=Sa(c,d[0]),f[0]=Ma(c,f[0]),d[1]=Sa(l,d[1]),f[1]=Ma(l,f[1]),d[1]=Sa(p,d[1]),f[1]=Ma(p,f[1]),n=e[a++],i=e[a++];break;case Y.Q:g=n,L=i,M=e[a++],x=e[a++],S=e[a],v=e[a+1],b=Ba,T=Fa,t=w=t=_=void 0,_=Wn,t=Ma(Sa((w=Xn)(g,M,S),1),0),w=Ma(Sa(w(L,x,v),1),0),M=_(g,M,S,t),t=_(L,x,v,w),b[0]=Sa(g,S,M),b[1]=Sa(L,v,t),T[0]=Ma(g,S,M),T[1]=Ma(L,v,t),n=e[a++],i=e[a++];break;case Y.A:var v,_=e[a++],x=e[a++],w=e[a++],b=e[a++],S=e[a++],M=e[a++]+S,T=(a+=1,!e[a++]),C=(z&&(r=Ga(S)*w+_,o=Wa(S)*b+x),N=v=U=W=R=O=L=P=A=D=k=I=C=void 0,_),I=x,k=w,D=b,A=S,P=M,L=T,O=Ba,R=Fa,W=ne,U=ie;if((v=Math.abs(A-P))%Ia<1e-4&&1e-4<v)O[0]=C-k,O[1]=I-D,R[0]=C+k,R[1]=I+D;else{ka[0]=Ca(A)*k+C,ka[1]=Ta(A)*D+I,Da[0]=Ca(P)*k+C,Da[1]=Ta(P)*D+I,W(O,ka,Da),U(R,ka,Da),(A%=Ia)<0&&(A+=Ia),(P%=Ia)<0&&(P+=Ia),P<A&&!L?P+=Ia:A<P&&L&&(A+=Ia),L&&(v=P,P=A,A=v);for(var N=0;N<P;N+=Math.PI/2)A<N&&(Aa[0]=Ca(N)*k+C,Aa[1]=Ta(N)*D+I,W(O,Aa,O),U(R,Aa,R))}n=Ga(M)*w+_,i=Wa(M)*b+x;break;case Y.R:Pa(r=n=e[a++],o=i=e[a++],r+e[a++],o+e[a++],Ba,Fa);break;case Y.Z:n=r,i=o}ne(Ea,Ea,Ba),ie(za,za,Fa)}return 0===a&&(Ea[0]=Ea[1]=za[0]=za[1]=0),new X(Ea[0],Ea[1],za[0]-Ea[0],za[1]-Ea[1])},o.prototype._calculateLength=function(){for(var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0,l=(this._pathSegLen||(this._pathSegLen=[]),this._pathSegLen),u=0,h=0,c=0;c<e;){var p=t[c++],d=1===c,f=(d&&(a=r=t[c],s=o=t[c+1]),-1);switch(p){case Y.M:r=a=t[c++],o=s=t[c++];break;case Y.L:var g=t[c++],y=(_=t[c++])-o;(Ua(I=g-r)>n||Ua(y)>i||c===e-1)&&(f=Math.sqrt(I*I+y*y),r=g,o=_);break;case Y.C:var m=t[c++],v=t[c++],g=t[c++],_=t[c++],x=t[c++],w=t[c++],f=function(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1/l,d=1;d<=l;d++){var f=d*p,g=zn(t,n,r,a,f),f=zn(e,i,o,s,f),y=g-u,m=f-h;c+=Math.sqrt(y*y+m*m),u=g,h=f}return c}(r,o,m,v,g,_,x,w,10),r=x,o=w;break;case Y.Q:f=function(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1/a,c=1;c<=a;c++){var p=c*h,d=Wn(t,n,r,p),p=Wn(e,i,o,p),f=d-s,g=p-l;u+=Math.sqrt(f*f+g*g),s=d,l=p}return u}(r,o,m=t[c++],v=t[c++],g=t[c++],_=t[c++],10),r=g,o=_;break;case Y.A:var x=t[c++],w=t[c++],b=t[c++],S=t[c++],M=t[c++],T=t[c++],C=T+M;c+=1,d&&(a=Ga(M)*b+x,s=Wa(M)*S+w),f=Ha(b,S)*Va(Ya,Math.abs(T)),r=Ga(C)*b+x,o=Wa(C)*S+w;break;case Y.R:a=r=t[c++],s=o=t[c++];f=2*t[c++]+2*t[c++];break;case Y.Z:var I=a-r,y=s-o;f=Math.sqrt(I*I+y*y),r=a,o=s}0<=f&&(u+=l[h++]=f)}return this._pathLen=u},o.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h=this.data,E=this._ux,z=this._uy,B=this._len,c=e<1,p=0,d=0,f=0;if(!c||(this._pathSegLen||this._calculateLength(),a=this._pathSegLen,s=e*this._pathLen))t:for(var g=0;g<B;){var y=h[g++],F=1===g;switch(F&&(n=r=h[g],i=o=h[g+1]),y!==Y.L&&0<f&&(t.lineTo(l,u),f=0),y){case Y.M:n=r=h[g++],i=o=h[g++],t.moveTo(r,o);break;case Y.L:var m=h[g++],v=h[g++],_=Ua(m-r),x=Ua(v-o);if(E<_||z<x){if(c){if(s<p+(N=a[d++])){var w=(s-p)/N;t.lineTo(r*(1-w)+m*w,o*(1-w)+v*w);break t}p+=N}t.lineTo(m,v),r=m,o=v,f=0}else{_=_*_+x*x;f<_&&(l=m,u=v,f=_)}break;case Y.C:var b=h[g++],S=h[g++],M=h[g++],T=h[g++],x=h[g++],_=h[g++];if(c){if(s<p+(N=a[d++])){Hn(r,b,M,x,w=(s-p)/N,Ra),Hn(o,S,T,_,w,Na),t.bezierCurveTo(Ra[1],Na[1],Ra[2],Na[2],Ra[3],Na[3]);break t}p+=N}t.bezierCurveTo(b,S,M,T,x,_),r=x,o=_;break;case Y.Q:b=h[g++],S=h[g++],M=h[g++],T=h[g++];if(c){if(s<p+(N=a[d++])){Yn(r,b,M,w=(s-p)/N,Ra),Yn(o,S,T,w,Na),t.quadraticCurveTo(Ra[1],Na[1],Ra[2],Na[2]);break t}p+=N}t.quadraticCurveTo(b,S,M,T),r=M,o=T;break;case Y.A:var C=h[g++],I=h[g++],k=h[g++],D=h[g++],A=h[g++],P=h[g++],L=h[g++],V=!h[g++],H=D<k?k:D,O=.001<Ua(k-D),R=A+P,G=!1;if(c&&(s<p+(N=a[d++])&&(R=A+P*(s-p)/N,G=!0),p+=N),O&&t.ellipse?t.ellipse(C,I,k,D,L,A,R,V):t.arc(C,I,H,A,R,V),G)break t;F&&(n=Ga(A)*k+C,i=Wa(A)*D+I),r=Ga(R)*k+C,o=Wa(R)*D+I;break;case Y.R:n=r=h[g],i=o=h[g+1],m=h[g++],v=h[g++];var N,P=h[g++],O=h[g++];if(c){if(s<p+(N=a[d++])){L=s-p;t.moveTo(m,v),t.lineTo(m+Va(L,P),v),0<(L-=P)&&t.lineTo(m+P,v+Va(L,O)),0<(L-=O)&&t.lineTo(m+Ha(P-L,0),v+O),0<(L-=P)&&t.lineTo(m,v+Ha(O-L,0));break t}p+=N}t.rect(m,v,P,O);break;case Y.Z:if(c){if(s<p+(N=a[d++])){w=(s-p)/N;t.lineTo(r*(1-w)+n*w,o*(1-w)+i*w);break t}p+=N}t.closePath(),r=n,o=i}}},o.prototype.clone=function(){var t=new o,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},o.CMD=Y,o.initDefaultProps=((hu=o.prototype)._saveData=!0,hu._ux=0,hu._uy=0,hu._pendingPtDist=0,void(hu._version=0));var Ka=o;function o(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}function $a(t,e,n,i,r,o,a){var s;if(0!==r)return s=0,!(e+(r=r)<a&&i+r<a||a<e-r&&a<i-r||t+r<o&&n+r<o||o<t-r&&o<n-r)&&(t===n?Math.abs(o-t)<=r/2:(o=(s=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n))*o/(s*s+1)<=r/2*r/2)}var Qa=2*Math.PI;function Ja(t){return(t%=Qa)<0&&(t+=Qa),t}var ts=2*Math.PI;function es(t,e,n,i,r,o){return e<o&&i<o||o<e&&o<i||i===e?0:(n=(o=(o-e)/(i-e))*(n-t)+t)===r?1/0:r<n?1!=o&&0!=o?i<e?1:-1:i<e?.5:-.5:0}var ns=Ka.CMD,is=2*Math.PI,rs=1e-4;var os=[-1,-1,-1],as=[-1,-1];function ss(t,e,n,i,r,o,a,s,l,u){if(e<u&&i<u&&o<u&&s<u||u<e&&u<i&&u<o&&u<s)return 0;var h=Fn(e,i,o,s,u,os);if(0===h)return 0;for(var c,p=0,d=-1,f=void 0,g=void 0,y=0;y<h;y++){var m=os[y],v=0===m||1===m?.5:1;zn(t,n,r,a,m)<l||(d<0&&(d=Vn(e,i,o,s,as),as[1]<as[0]&&1<d&&(c=void 0,c=as[0],as[0]=as[1],as[1]=c),f=zn(e,i,o,s,as[0]),1<d)&&(g=zn(e,i,o,s,as[1])),2===d?m<as[0]?p+=f<e?v:-v:m<as[1]?p+=g<f?v:-v:p+=s<g?v:-v:m<as[0]?p+=f<e?v:-v:p+=s<f?v:-v)}return p}function ls(t,e,n,i,r,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;c=os,h=(l=e)-2*(u=i)+(h=o),u=2*(u-l),l-=s=s,s=0,Nn(h)?En(u)&&0<=(p=-l/u)&&p<=1&&(c[s++]=p):Nn(l=u*u-4*h*l)?0<=(p=-u/(2*h))&&p<=1&&(c[s++]=p):0<l&&(d=(-u-(l=kn(l)))/(2*h),0<=(p=(-u+l)/(2*h))&&p<=1&&(c[s++]=p),0<=d)&&d<=1&&(c[s++]=d);var l,u,h,c,p,d,f=s;if(0===f)return 0;var g=Xn(e,i,o);if(0<=g&&g<=1){for(var y=0,m=Wn(e,i,o,g),v=0;v<f;v++){var _=0===os[v]||1===os[v]?.5:1;Wn(t,n,r,os[v])<a||(os[v]<g?y+=m<e?_:-_:y+=o<m?_:-_)}return y}return _=0===os[0]||1===os[0]?.5:1,Wn(t,n,r,os[0])<a?0:o<e?_:-_}function us(t,e,n,i,r){for(var o,a=t.data,s=t.len(),l=0,u=0,h=0,c=0,p=0,d=0;d<s;){var f=a[d++],g=1===d;switch(f===ns.M&&1<d&&(n||(l+=es(u,h,c,p,i,r))),g&&(c=u=a[d],p=h=a[d+1]),f){case ns.M:u=c=a[d++],h=p=a[d++];break;case ns.L:if(n){if($a(u,h,a[d],a[d+1],e,i,r))return!0}else l+=es(u,h,a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case ns.C:if(n){if(function(t,e,n,i,r,o,a,s,l,u,h){if(0!==l)return!(e+(l=l)<h&&i+l<h&&o+l<h&&s+l<h||h<e-l&&h<i-l&&h<o-l&&h<s-l||t+l<u&&n+l<u&&r+l<u&&a+l<u||u<t-l&&u<n-l&&u<r-l&&u<a-l)&&Gn(t,e,n,i,r,o,a,s,u,h,null)<=l/2}(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=ss(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case ns.Q:if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return!(e+(a=a)<l&&i+a<l&&o+a<l||l<e-a&&l<i-a&&l<o-a||t+a<s&&n+a<s&&r+a<s||s<t-a&&s<n-a&&s<r-a)&&qn(t,e,n,i,r,o,s,l,null)<=a/2}(u,h,a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=ls(u,h,a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case ns.A:var y=a[d++],m=a[d++],v=a[d++],_=a[d++],x=a[d++],w=a[d++],b=(d+=1,!!(1-a[d++])),S=Math.cos(x)*v+y,M=Math.sin(x)*_+m,T=(g?(c=S,p=M):l+=es(u,h,S,M,i,r),(i-y)*_/v+y);if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return a=a,s-=t,l-=e,!(n<(t=Math.sqrt(s*s+l*l))-a||t+a<n)&&(Math.abs(i-r)%ts<1e-4||((r=o?(e=i,i=Ja(r),Ja(e)):(i=Ja(i),Ja(r)))<i&&(r+=ts),(t=Math.atan2(l,s))<0&&(t+=ts),i<=t&&t<=r)||i<=t+ts&&t+ts<=r)}(y,m,_,x,x+w,b,e,T,r))return!0}else l+=function(t,e,n,i,r,o,a,s){if(n<(s-=e)||s<-n)return 0;var e=Math.sqrt(n*n-s*s);if(os[0]=-e,os[1]=e,(n=Math.abs(i-r))<1e-4)return 0;if(is-1e-4<=n)return r=is,h=o?1:-1,a>=os[i=0]+t&&a<=os[1]+t?h:0;r<i&&(e=i,i=r,r=e),i<0&&(i+=is,r+=is);for(var l=0,u=0;u<2;u++){var h,c=os[u];a<c+t&&(h=o?1:-1,i<=(c=(c=Math.atan2(s,c))<0?is+c:c)&&c<=r||i<=c+is&&c+is<=r)&&(l+=h=c>Math.PI/2&&c<1.5*Math.PI?-h:h)}return l}(y,m,_,x,x+w,b,T,r);u=Math.cos(x+w)*v+y,h=Math.sin(x+w)*_+m;break;case ns.R:c=u=a[d++],p=h=a[d++];if(S=c+a[d++],M=p+a[d++],n){if($a(c,p,S,p,e,i,r)||$a(S,p,S,M,e,i,r)||$a(S,M,c,M,e,i,r)||$a(c,M,c,p,e,i,r))return!0}else l=(l+=es(S,p,S,M,i,r))+es(c,M,c,p,i,r);break;case ns.Z:if(n){if($a(u,h,c,p,e,i,r))return!0}else l+=es(u,h,c,p,i,r);u=c,h=p}}return n||(t=h,o=p,Math.abs(t-o)<rs)||(l+=es(u,h,c,p,i,r)||0),0!==l}var hs,cs=z({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},ma),ps={style:z({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},va.style)},ds=xr.concat(["invisible","culling","z","z2","zlevel","parent"]),j=(u(a,hs=n),a.prototype.update=function(){var e=this,t=(hs.prototype.update.call(this),this.style);if(t.decal){var n,i=this._decalEl=this._decalEl||new a,r=(i.buildPath===a.prototype.buildPath&&(i.buildPath=function(t){e.buildPath(t,e.shape)}),i.silent=!0,i.style);for(n in t)r[n]!==t[n]&&(r[n]=t[n]);r.fill=t.fill?t.decal:null,r.decal=null,r.shadowColor=null,t.strokeFirst&&(r.stroke=null);for(var o=0;o<ds.length;++o)i[ds[o]]=this[ds[o]];i.__dirty|=vn}else this._decalEl&&(this._decalEl=null)},a.prototype.getDecalElement=function(){return this._decalEl},a.prototype._init=function(t){var e=ht(t),n=(this.shape=this.getDefaultShape(),this.getDefaultStyle());n&&this.useStyle(n);for(var i=0;i<e.length;i++){var r=e[i],o=t[r];"style"===r?this.style?L(this.style,o):this.useStyle(o):"shape"===r?L(this.shape,o):hs.prototype.attrKV.call(this,r,o)}this.style||this.useStyle({})},a.prototype.getDefaultStyle=function(){return null},a.prototype.getDefaultShape=function(){return{}},a.prototype.canBeInsideText=function(){return this.hasFill()},a.prototype.getInsideTextFill=function(){var t,e=this.style.fill;if("none"!==e){if(V(e))return.5<(t=bi(e,0))?hr:.2<t?"#eee":cr;if(e)return cr}return hr},a.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(V(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())==bi(t,0)<.4)return e}},a.prototype.buildPath=function(t,e,n){},a.prototype.pathUpdated=function(){this.__dirty&=~_n},a.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},a.prototype.createPathProxy=function(){this.path=new Ka(!1)},a.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))},a.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},a.prototype.getBoundingRect=function(){var t,e,n=this._rect,i=this.style,r=!n;return r&&(t=!1,this.path||(t=!0,this.createPathProxy()),e=this.path,(t||this.__dirty&_n)&&(e.beginPath(),this.buildPath(e,this.shape,!1),this.pathUpdated()),n=e.getBoundingRect()),this._rect=n,this.hasStroke()&&this.path&&0<this.path.len()?(t=this._rectStroke||(this._rectStroke=n.clone()),(this.__dirty||r)&&(t.copy(n),e=i.strokeNoScale?this.getLineScale():1,r=i.lineWidth,this.hasFill()||(i=this.strokeContainThreshold,r=Math.max(r,null==i?4:i)),1e-10<e)&&(t.width+=r/e,t.height+=r/e,t.x-=r/e/2,t.y-=r/e/2),t):n},a.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){n=this.path;if(this.hasStroke()){i=r.lineWidth,r=r.strokeNoScale?this.getLineScale():1;if(1e-10<r&&(this.hasFill()||(i=Math.max(i,this.strokeContainThreshold)),us(n,i/r,!0,t,e)))return!0}if(this.hasFill())return us(n,0,!1,t,e)}return!1},a.prototype.dirtyShape=function(){this.__dirty|=_n,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},a.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},a.prototype.animateShape=function(t){return this.animate("shape",t)},a.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},a.prototype.attrKV=function(t,e){"shape"===t?this.setShape(e):hs.prototype.attrKV.call(this,t,e)},a.prototype.setShape=function(t,e){var n=(n=this.shape)||(this.shape={});return"string"==typeof t?n[t]=e:L(n,t),this.dirtyShape(),this},a.prototype.shapeChanged=function(){return!!(this.__dirty&_n)},a.prototype.createStyle=function(t){return Et(cs,t)},a.prototype._innerSaveToNormal=function(t){hs.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.shape&&!e.shape&&(e.shape=L({},this.shape))},a.prototype._applyStateObj=function(t,e,n,i,r,o){hs.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.shape?r?i?a=e.shape:(a=L({},n.shape),L(a,e.shape)):(a=L({},(i?this:n).shape),L(a,e.shape)):s&&(a=n.shape),a)if(r){this.shape=L({},this.shape);for(var l={},u=ht(a),h=0;h<u.length;h++){var c=u[h];"object"==typeof a[c]?this.shape[c]=a[c]:l[c]=a[c]}this._transitionState(t,{shape:l},o)}else this.shape=a,this.dirtyShape()},a.prototype._mergeStates=function(t){for(var e,n=hs.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.shape&&this._mergeStyle(e=e||{},r.shape)}return e&&(n.shape=e),n},a.prototype.getAnimationStyleProps=function(){return ps},a.prototype.isZeroArea=function(){return!1},a.extend=function(n){u(r,i=a),r.prototype.getDefaultStyle=function(){return y(n.style)},r.prototype.getDefaultShape=function(){return y(n.shape)};var i,t,e=r;function r(t){var e=i.call(this,t)||this;return n.init&&n.init.call(e,t),e}for(t in n)"function"==typeof n[t]&&(e.prototype[t]=n[t]);return e},a.initDefaultProps=((hu=a.prototype).type="path",hu.strokeContainThreshold=5,hu.segmentIgnoreThreshold=0,hu.subPixelOptimize=!1,hu.autoBatch=!1,void(hu.__dirty=2|vn|_n)),a);function a(t){return hs.call(this,t)||this}var fs,gs=z({strokeFirst:!0,font:K,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},cs),ys=(u(ms,fs=n),ms.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&0<t.lineWidth},ms.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},ms.prototype.createStyle=function(t){return Et(gs,t)},ms.prototype.setBoundingRect=function(t){this._rect=t},ms.prototype.getBoundingRect=function(){var t,e=this.style;return this._rect||(null!=(t=e.text)?t+="":t="",(t=Mr(t,e.font,e.textAlign,e.textBaseline)).x+=e.x||0,t.y+=e.y||0,this.hasStroke()&&(e=e.lineWidth,t.x-=e/2,t.y-=e/2,t.width+=e,t.height+=e),this._rect=t),this._rect},ms.initDefaultProps=void(ms.prototype.dirtyRectTolerance=10),ms);function ms(){return null!==fs&&fs.apply(this,arguments)||this}ys.prototype.type="tspan";var vs=z({x:0,y:0},ma),_s={style:z({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},va.style)};u(bs,xs=n),bs.prototype.createStyle=function(t){return Et(vs,t)},bs.prototype._getSize=function(t){var e,n=this.style,i=n[t];return null!=i?i:(i=(i=n.image)&&"string"!=typeof i&&i.width&&i.height?n.image:this.__image)?null==(e=n[n="width"===t?"height":"width"])?i[t]:i[t]/i[n]*e:0},bs.prototype.getWidth=function(){return this._getSize("width")},bs.prototype.getHeight=function(){return this._getSize("height")},bs.prototype.getAnimationStyleProps=function(){return _s},bs.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new X(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect};var xs,ws=bs;function bs(){return null!==xs&&xs.apply(this,arguments)||this}ws.prototype.type="image";var Ss=Math.round;function Ms(t,e,n){var i,r,o;if(e)return i=e.x1,r=e.x2,o=e.y1,e=e.y2,t.x1=i,t.x2=r,t.y1=o,t.y2=e,(n=n&&n.lineWidth)&&(Ss(2*i)===Ss(2*r)&&(t.x1=t.x2=Cs(i,n,!0)),Ss(2*o)===Ss(2*e))&&(t.y1=t.y2=Cs(o,n,!0)),t}function Ts(t,e,n){var i,r,o;if(e)return i=e.x,r=e.y,o=e.width,e=e.height,t.x=i,t.y=r,t.width=o,t.height=e,(n=n&&n.lineWidth)&&(t.x=Cs(i,n,!0),t.y=Cs(r,n,!0),t.width=Math.max(Cs(i+o,n,!1)-t.x,0===o?0:1),t.height=Math.max(Cs(r+e,n,!1)-t.y,0===e?0:1)),t}function Cs(t,e,n){var i;return e?((i=Ss(2*t))+Ss(e))%2==0?i/2:(i+(n?1:-1))/2:t}var Is,ks=function(){this.x=0,this.y=0,this.width=0,this.height=0},Ds={},As=(u(Ps,Is=j),Ps.prototype.getDefaultShape=function(){return new ks},Ps.prototype.buildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g;this.subPixelOptimize?(n=(a=Ts(Ds,e,this.style)).x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a):(n=e.x,i=e.y,r=e.width,o=e.height),e.r?(a=t,p=(e=e).x,d=e.y,f=e.width,g=e.height,e=e.r,f<0&&(p+=f,f=-f),g<0&&(d+=g,g=-g),"number"==typeof e?s=l=u=h=e:e instanceof Array?1===e.length?s=l=u=h=e[0]:2===e.length?(s=u=e[0],l=h=e[1]):3===e.length?(s=e[0],l=h=e[1],u=e[2]):(s=e[0],l=e[1],u=e[2],h=e[3]):s=l=u=h=0,f<s+l&&(s*=f/(c=s+l),l*=f/c),f<u+h&&(u*=f/(c=u+h),h*=f/c),g<l+u&&(l*=g/(c=l+u),u*=g/c),g<s+h&&(s*=g/(c=s+h),h*=g/c),a.moveTo(p+s,d),a.lineTo(p+f-l,d),0!==l&&a.arc(p+f-l,d+l,l,-Math.PI/2,0),a.lineTo(p+f,d+g-u),0!==u&&a.arc(p+f-u,d+g-u,u,0,Math.PI/2),a.lineTo(p+h,d+g),0!==h&&a.arc(p+h,d+g-h,h,Math.PI/2,Math.PI),a.lineTo(p,d+s),0!==s&&a.arc(p+s,d+s,s,Math.PI,1.5*Math.PI)):t.rect(n,i,r,o)},Ps.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},Ps);function Ps(t){return Is.call(this,t)||this}As.prototype.type="rect";var Ls,Os={fill:"#000"},Rs={style:z({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},va.style)},Ns=(u(Es,Ls=n),Es.prototype.childrenRef=function(){return this._children},Es.prototype.update=function(){Ls.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var e=this._children[t];e.zlevel=this.zlevel,e.z=this.z,e.z2=this.z2,e.culling=this.culling,e.cursor=this.cursor,e.invisible=this.invisible}},Es.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):Ls.prototype.updateTransform.call(this)},Es.prototype.getLocalTransform=function(t){var e=this.innerTransformable;return e?e.getLocalTransform(t):Ls.prototype.getLocalTransform.call(this,t)},Es.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),Ls.prototype.getComputedTransform.call(this)},Es.prototype._updateSubTexts=function(){var t;this._childCursor=0,Hs(t=this.style),O(t.rich,Hs),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},Es.prototype.addSelfToZr=function(t){Ls.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=t},Es.prototype.removeSelfFromZr=function(t){Ls.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=null},Es.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new X(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),o=o.getLocalTransform(n);o?(t.copy(a),t.applyTransform(o),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},Es.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||Os},Es.prototype.setTextContent=function(t){},Es.prototype._mergeStyle=function(t,e){var n,i;return e&&(n=e.rich,i=t.rich||n&&{},L(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i)),t},Es.prototype._mergeRich=function(t,e){for(var n=ht(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},L(t[r],e[r])}},Es.prototype.getAnimationStyleProps=function(){return Rs},Es.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),(this._children[this._childCursor++]=e).__zr=this.__zr,e.parent=this,e},Es.prototype._updatePlainTexts=function(){for(var t,e=this.style,n=e.font||K,i=e.padding,r=function(t,e){null!=t&&(t+="");var n,i=e.overflow,r=e.padding,o=e.font,a="truncate"===i,s=Ir(o),l=N(e.lineHeight,s),u=!!e.backgroundColor,h="truncate"===e.lineOverflow,c=!1,p=e.width,i=(n=null==p||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?fa(t,e.font,p,"breakAll"===i,0).lines:[]).length*l,d=N(e.height,i);if(d<i&&h&&(h=Math.floor(d/l),c=c||n.length>h,n=n.slice(0,h)),t&&a&&null!=p)for(var f=aa(p,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),g={},y=0;y<n.length;y++)sa(g,n[y],f),n[y]=g.textLine,c=c||g.isTruncated;for(var h=d,m=0,y=0;y<n.length;y++)m=Math.max(br(n[y],o),m);return null==p&&(p=m),t=m,r&&(h+=r[0]+r[2],t+=r[1]+r[3],p+=r[1]+r[3]),{lines:n,height:d,outerWidth:t=u?p:t,outerHeight:h,lineHeight:l,calculatedLineHeight:s,contentWidth:m,contentHeight:i,width:p,isTruncated:c}}(Xs(e),e),o=Ys(e),a=!!e.backgroundColor,s=r.outerHeight,l=r.outerWidth,u=r.contentWidth,h=r.lines,c=r.lineHeight,p=this._defaultStyle,d=(this.isTruncated=!!r.isTruncated,e.x||0),f=e.y||0,g=e.align||p.align||"left",y=e.verticalAlign||p.verticalAlign||"top",m=d,v=Cr(f,r.contentHeight,y),_=((o||i)&&(t=Tr(d,l,g),f=Cr(f,s,y),o)&&this._renderBackground(e,e,t,f,l,s),v+=c/2,i&&(m=Us(d,g,i),"top"===y?v+=i[0]:"bottom"===y&&(v-=i[2])),0),o=!1,x=(Ws(("fill"in e?e:(o=!0,p)).fill)),w=(Gs("stroke"in e?e.stroke:a||p.autoStroke&&!o?null:(_=2,p.stroke))),b=0<e.textShadowBlur,S=null!=e.width&&("truncate"===e.overflow||"break"===e.overflow||"breakAll"===e.overflow),M=r.calculatedLineHeight,T=0;T<h.length;T++){var C=this._getOrCreateChild(ys),I=C.createStyle();C.useStyle(I),I.text=h[T],I.x=m,I.y=v,g&&(I.textAlign=g),I.textBaseline="middle",I.opacity=e.opacity,I.strokeFirst=!0,b&&(I.shadowBlur=e.textShadowBlur||0,I.shadowColor=e.textShadowColor||"transparent",I.shadowOffsetX=e.textShadowOffsetX||0,I.shadowOffsetY=e.textShadowOffsetY||0),I.stroke=w,I.fill=x,w&&(I.lineWidth=e.lineWidth||_,I.lineDash=e.lineDash,I.lineDashOffset=e.lineDashOffset||0),I.font=n,Vs(I,e),v+=c,S&&C.setBoundingRect(new X(Tr(I.x,u,I.textAlign),Cr(I.y,M,I.textBaseline),u,M))}},Es.prototype._updateRichTexts=function(){for(var t=this.style,e=ca(Xs(t),t),n=e.width,i=e.outerWidth,r=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,l=this._defaultStyle,u=t.align||l.align,l=t.verticalAlign||l.verticalAlign,a=(this.isTruncated=!!e.isTruncated,Tr(a,i,u)),u=Cr(s,r,l),h=a,c=u,p=(o&&(h+=o[3],c+=o[0]),h+n),d=(Ys(t)&&this._renderBackground(t,t,a,u,i,r),!!t.backgroundColor),f=0;f<e.lines.length;f++){for(var g=e.lines[f],y=g.tokens,m=y.length,v=g.lineHeight,_=g.width,x=0,w=h,b=p,S=m-1,M=void 0;x<m&&(!(M=y[x]).align||"left"===M.align);)this._placeToken(M,t,v,c,w,"left",d),_-=M.width,w+=M.width,x++;for(;0<=S&&"right"===(M=y[S]).align;)this._placeToken(M,t,v,c,b,"right",d),_-=M.width,b-=M.width,S--;for(w+=(n-(w-h)-(p-b)-_)/2;x<=S;)M=y[x],this._placeToken(M,t,v,c,w+M.width/2,"center",d),w+=M.width,x++;c+=v}},Es.prototype._placeToken=function(t,e,n,i,r,o,a){var s=e.rich[t.styleName]||{},l=(s.text=t.text,t.verticalAlign),u=i+n/2;"top"===l?u=i+t.height/2:"bottom"===l&&(u=i+n-t.height/2);!t.isLineHolder&&Ys(s)&&this._renderBackground(s,e,"right"===o?r-t.width:"center"===o?r-t.width/2:r,u-t.height/2,t.width,t.height);var l=!!s.backgroundColor,i=t.textPadding,n=(i&&(r=Us(r,o,i),u-=t.height/2-i[0]-t.innerHeight/2),this._getOrCreateChild(ys)),i=n.createStyle(),h=(n.useStyle(i),this._defaultStyle),c=!1,p=0,d=Ws(("fill"in s?s:"fill"in e?e:(c=!0,h)).fill),l=Gs("stroke"in s?s.stroke:"stroke"in e?e.stroke:l||a||h.autoStroke&&!c?null:(p=2,h.stroke)),a=0<s.textShadowBlur||0<e.textShadowBlur,c=(i.text=t.text,i.x=r,i.y=u,a&&(i.shadowBlur=s.textShadowBlur||e.textShadowBlur||0,i.shadowColor=s.textShadowColor||e.textShadowColor||"transparent",i.shadowOffsetX=s.textShadowOffsetX||e.textShadowOffsetX||0,i.shadowOffsetY=s.textShadowOffsetY||e.textShadowOffsetY||0),i.textAlign=o,i.textBaseline="middle",i.font=t.font||K,i.opacity=bt(s.opacity,e.opacity,1),Vs(i,s),l&&(i.lineWidth=bt(s.lineWidth,e.lineWidth,p),i.lineDash=N(s.lineDash,e.lineDash),i.lineDashOffset=e.lineDashOffset||0,i.stroke=l),d&&(i.fill=d),t.contentWidth),h=t.contentHeight;n.setBoundingRect(new X(Tr(i.x,c,i.textAlign),Cr(i.y,h,i.textBaseline),c,h))},Es.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u,h=t.backgroundColor,c=t.borderWidth,p=t.borderColor,d=h&&h.image,f=h&&!d,g=t.borderRadius,y=this,g=((f||t.lineHeight||c&&p)&&((a=this._getOrCreateChild(As)).useStyle(a.createStyle()),a.style.fill=null,(l=a.shape).x=n,l.y=i,l.width=r,l.height=o,l.r=g,a.dirtyShape()),f?((u=a.style).fill=h||null,u.fillOpacity=N(t.fillOpacity,1)):d&&((s=this._getOrCreateChild(ws)).onload=function(){y.dirtyStyle()},(l=s.style).image=h.image,l.x=n,l.y=i,l.width=r,l.height=o),c&&p&&((u=a.style).lineWidth=c,u.stroke=p,u.strokeOpacity=N(t.strokeOpacity,1),u.lineDash=t.borderDash,u.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill())&&a.hasStroke()&&(u.strokeFirst=!0,u.lineWidth*=2),(a||s).style);g.shadowBlur=t.shadowBlur||0,g.shadowColor=t.shadowColor||"transparent",g.shadowOffsetX=t.shadowOffsetX||0,g.shadowOffsetY=t.shadowOffsetY||0,g.opacity=bt(t.opacity,e.opacity,1)},Es.makeFont=function(t){var e,n="";return(n=null!=(e=t).fontSize||e.fontFamily||e.fontWeight?[t.fontStyle,t.fontWeight,"string"!=typeof(e=t.fontSize)||-1===e.indexOf("px")&&-1===e.indexOf("rem")&&-1===e.indexOf("em")?isNaN(+e)?"12px":e+"px":e,t.fontFamily||"sans-serif"].join(" "):n)&&Ct(n)||t.textFont||t.font},Es);function Es(t){var e=Ls.call(this)||this;return e.type="text",e._children=[],e._defaultStyle=Os,e.attr(t),e}var zs={left:!0,right:1,center:1},Bs={top:1,bottom:1,middle:1},Fs=["fontStyle","fontWeight","fontSize","fontFamily"];function Vs(t,e){for(var n=0;n<Fs.length;n++){var i=Fs[n],r=e[i];null!=r&&(t[i]=r)}}function Hs(t){var e;t&&(t.font=Ns.makeFont(t),e=t.align,t.align=null==(e="middle"===e?"center":e)||zs[e]?e:"left",e=t.verticalAlign,t.verticalAlign=null==(e="center"===e?"middle":e)||Bs[e]?e:"top",t.padding)&&(t.padding=Mt(t.padding))}function Gs(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Ws(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Us(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Xs(t){t=t.text;return null!=t&&(t+=""),t}function Ys(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var D=Lo(),qs=1,Zs={},js=Lo(),Ks=Lo(),$s=0,Qs=1,Js=2,tl=["emphasis","blur","select"],el=["normal","emphasis","blur","select"],nl="highlight",il="downplay",rl="select",ol="unselect",al="toggleSelect";function sl(t){return null!=t&&"none"!==t}function ll(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function ul(t){ll(t,"emphasis",Js)}function hl(t){t.hoverState===Js&&ll(t,"normal",$s)}function cl(t){ll(t,"blur",Qs)}function pl(t){t.hoverState===Qs&&ll(t,"normal",$s)}function dl(t){t.selected=!0}function fl(t){t.selected=!1}function gl(t,e,n){e(t,n)}function yl(t,e,n){gl(t,e,n),t.isGroup&&t.traverse(function(t){gl(t,e,n)})}function ml(t,e){switch(e){case"emphasis":t.hoverState=Js;break;case"normal":t.hoverState=$s;break;case"blur":t.hoverState=Qs;break;case"select":t.selected=!0}}function vl(t,e,n){var i=0<=I(t.currentStates,e),r=t.style.opacity,t=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveTo(o,e)}return o}(t,["opacity"],e,{opacity:1}),e=(n=n||{}).style||{};return null==e.opacity&&(n=L({},n),e=L({opacity:i?r:.1*t.opacity},e),n.style=e),n}function _l(t,e){var n,i,r,o,a,s=this.states[t];if(this.style){if("emphasis"===t)return n=this,i=s,e=(e=e)&&0<=I(e,"select"),a=!1,n instanceof j&&(r=js(n),o=e&&r.selectFill||r.normalFill,e=e&&r.selectStroke||r.normalStroke,sl(o)||sl(e))&&("inherit"===(r=(i=i||{}).style||{}).fill?(a=!0,i=L({},i),(r=L({},r)).fill=o):!sl(r.fill)&&sl(o)?(a=!0,i=L({},i),(r=L({},r)).fill=Mi(o)):!sl(r.stroke)&&sl(e)&&(a||(i=L({},i),r=L({},r)),r.stroke=Mi(e)),i.style=r),i&&null==i.z2&&(a||(i=L({},i)),o=n.z2EmphasisLift,i.z2=n.z2+(null!=o?o:10)),i;if("blur"===t)return vl(this,t,s);if("select"===t)return e=this,(r=s)&&null==r.z2&&(r=L({},r),a=e.z2SelectLift,r.z2=e.z2+(null!=a?a:9)),r}return s}function xl(t){t.stateProxy=_l;var e=t.getTextContent(),t=t.getTextGuideLine();e&&(e.stateProxy=_l),t&&(t.stateProxy=_l)}function wl(t,e){kl(t,e)||t.__highByOuter||yl(t,ul)}function bl(t,e){kl(t,e)||t.__highByOuter||yl(t,hl)}function Sl(t,e){t.__highByOuter|=1<<(e||0),yl(t,ul)}function Ml(t,e){(t.__highByOuter&=~(1<<(e||0)))||yl(t,hl)}function Tl(t){yl(t,pl)}function Cl(t){yl(t,dl)}function Il(t){yl(t,fl)}function kl(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Dl(r){var e=r.getModel(),o=[],a=[];e.eachComponent(function(t,e){var n=Ks(e),t="series"===t,i=t?r.getViewOfSeriesModel(e):r.getViewOfComponentModel(e);t||a.push(i),n.isBlured&&(i.group.traverse(function(t){pl(t)}),t)&&o.push(e),n.isBlured=!1}),O(a,function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(o,!1,e)})}function Al(t,o,a,s){var l,u,h,n=s.getModel();function c(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&Tl(i)}}a=a||"coordinateSystem",null!=t&&o&&"none"!==o&&(l=n.getSeriesByIndex(t),(u=l.coordinateSystem)&&u.master&&(u=u.master),h=[],n.eachSeries(function(t){var e=l===t,n=t.coordinateSystem,n=(n=n&&n.master?n.master:n)&&u?n===u:e;if(!("series"===a&&!e||"coordinateSystem"===a&&!n||"series"===o&&e)){if(s.getViewOfSeriesModel(t).group.traverse(function(t){t.__highByOuter&&e&&"self"===o||cl(t)}),st(o))c(t.getData(),o);else if(R(o))for(var i=ht(o),r=0;r<i.length;r++)c(t.getData(i[r]),o[i[r]]);h.push(t),Ks(t).isBlured=!0}}),n.eachComponent(function(t,e){"series"!==t&&(t=s.getViewOfComponentModel(e))&&t.toggleBlurSeries&&t.toggleBlurSeries(h,!0,n)}))}function Pl(t,e,n){var i;null!=t&&null!=e&&(t=n.getModel().getComponent(t,e))&&(Ks(t).isBlured=!0,i=n.getViewOfComponentModel(t))&&i.focusBlurEnabled&&i.group.traverse(function(t){cl(t)})}function Ll(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;t=i.getModel().getComponent(t,e);if(!t)return r;e=i.getViewOfComponentModel(t);if(!e||!e.findHighDownDispatchers)return r;for(var o,a=e.findHighDownDispatchers(n),s=0;s<a.length;s++)if("self"===D(a[s]).focus){o=!0;break}return{focusSelf:o,dispatchers:a}}function Ol(i){O(i.getAllData(),function(t){var e=t.data,n=t.type;e.eachItemGraphicEl(function(t,e){(i.isSelected(e,n)?Cl:Il)(t)})})}function Rl(t,e,n){Fl(t,!0),yl(t,xl);t=D(t),null!=e?(t.focus=e,t.blurScope=n):t.focus&&(t.focus=null)}function Nl(t,e,n,i){i?Fl(t,!1):Rl(t,e,n)}var El=["emphasis","blur","select"],zl={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function Bl(t,e,n,i){n=n||"itemStyle";for(var r=0;r<El.length;r++){var o=El[r],a=e.getModel([o,n]);t.ensureState(o).style=i?i(a):a[zl[n]]()}}function Fl(t,e){var e=!1===e,n=t;t.highDownSilentOnTouch&&(n.__highDownSilentOnTouch=t.highDownSilentOnTouch),e&&!n.__highDownDispatcher||(n.__highByOuter=n.__highByOuter||0,n.__highDownDispatcher=!e)}function Vl(t){return!(!t||!t.__highDownDispatcher)}function Hl(t){t=t.type;return t===rl||t===ol||t===al}function Gl(t){t=t.type;return t===nl||t===il}var Wl=Ka.CMD,Ul=[[],[],[]],Xl=Math.sqrt,Yl=Math.atan2;var ql=Math.sqrt,Zl=Math.sin,jl=Math.cos,Kl=Math.PI;function $l(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function Ql(t,e){return(t[0]*e[0]+t[1]*e[1])/($l(t)*$l(e))}function Jl(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Ql(t,e))}function tu(t,e,n,i,r,o,a,s,l,u,h){var l=l*(Kl/180),c=jl(l)*(t-n)/2+Zl(l)*(e-i)/2,p=-1*Zl(l)*(t-n)/2+jl(l)*(e-i)/2,d=c*c/(a*a)+p*p/(s*s),d=(1<d&&(a*=ql(d),s*=ql(d)),(r===o?-1:1)*ql((a*a*(s*s)-a*a*(p*p)-s*s*(c*c))/(a*a*(p*p)+s*s*(c*c)))||0),r=d*a*p/s,d=d*-s*c/a,t=(t+n)/2+jl(l)*r-Zl(l)*d,n=(e+i)/2+Zl(l)*r+jl(l)*d,e=Jl([1,0],[(c-r)/a,(p-d)/s]),i=[(c-r)/a,(p-d)/s],c=[(-1*c-r)/a,(-1*p-d)/s],r=Jl(i,c);Ql(i,c)<=-1&&(r=Kl),(r=1<=Ql(i,c)?0:r)<0&&(p=Math.round(r/Kl*1e6)/1e6,r=2*Kl+p%2*Kl),h.addData(u,t,n,a,s,e,r,l,o)}var eu=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,nu=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;u(ou,iu=j),ou.prototype.applyTransform=function(t){};var iu,ru=ou;function ou(){return null!==iu&&iu.apply(this,arguments)||this}function au(t){return null!=t.setData}function su(t,e){var S=function(t){var e=new Ka;if(t){var n,i=0,r=0,o=i,a=r,s=Ka.CMD,l=t.match(eu);if(l){for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,d=h.match(nu)||[],f=d.length,g=0;g<f;g++)d[g]=parseFloat(d[g]);for(var y=0;y<f;){var m=void 0,v=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=i,T=r,C=void 0,I=void 0;switch(c){case"l":i+=d[y++],r+=d[y++],p=s.L,e.addData(p,i,r);break;case"L":i=d[y++],r=d[y++],p=s.L,e.addData(p,i,r);break;case"m":i+=d[y++],r+=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=d[y++],r=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=d[y++],p=s.L,e.addData(p,i,r);break;case"H":i=d[y++],p=s.L,e.addData(p,i,r);break;case"v":r+=d[y++],p=s.L,e.addData(p,i,r);break;case"V":r=d[y++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,d[y++],d[y++],d[y++],d[y++],d[y++],d[y++]),i=d[y-2],r=d[y-1];break;case"c":p=s.C,e.addData(p,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r),i+=d[y-2],r+=d[y-1];break;case"S":m=i,v=r,C=e.len(),I=e.data,n===s.C&&(m+=i-I[C-4],v+=r-I[C-3]),p=s.C,M=d[y++],T=d[y++],i=d[y++],r=d[y++],e.addData(p,m,v,M,T,i,r);break;case"s":m=i,v=r,C=e.len(),I=e.data,n===s.C&&(m+=i-I[C-4],v+=r-I[C-3]),p=s.C,M=i+d[y++],T=r+d[y++],i+=d[y++],r+=d[y++],e.addData(p,m,v,M,T,i,r);break;case"Q":M=d[y++],T=d[y++],i=d[y++],r=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=d[y++]+i,T=d[y++]+r,i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":m=i,v=r,C=e.len(),I=e.data,n===s.Q&&(m+=i-I[C-4],v+=r-I[C-3]),i=d[y++],r=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"t":m=i,v=r,C=e.len(),I=e.data,n===s.Q&&(m+=i-I[C-4],v+=r-I[C-3]),i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"A":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],tu(M=i,T=r,i=d[y++],r=d[y++],b,S,_,x,w,p=s.A,e);break;case"a":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],tu(M=i,T=r,i+=d[y++],r+=d[y++],b,S,_,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}e.toStatic()}}return e}(t),t=L({},e);return t.buildPath=function(t){var e;au(t)?(t.setData(S.data),(e=t.getContext())&&t.rebuildPath(e,1)):S.rebuildPath(e=t,1)},t.applyTransform=function(t){var e=S,n=t;if(n){for(var i,r,o,a,s=e.data,l=e.len(),u=Wl.M,h=Wl.C,c=Wl.L,p=Wl.R,d=Wl.A,f=Wl.Q,g=0,y=0;g<l;){switch(i=s[g++],y=g,r=0,i){case u:case c:r=1;break;case h:r=3;break;case f:r=2;break;case d:var m=n[4],v=n[5],_=Xl(n[0]*n[0]+n[1]*n[1]),x=Xl(n[2]*n[2]+n[3]*n[3]),w=Yl(-n[1]/x,n[0]/_);s[g]*=_,s[g++]+=m,s[g]*=x,s[g++]+=v,s[g++]*=_,s[g++]*=x,s[g++]+=w,s[g++]+=w,y=g+=2;break;case p:a[0]=s[g++],a[1]=s[g++],ee(a,a,n),s[y++]=a[0],s[y++]=a[1],a[0]+=s[g++],a[1]+=s[g++],ee(a,a,n),s[y++]=a[0],s[y++]=a[1]}for(o=0;o<r;o++){var b=Ul[o];b[0]=s[g++],b[1]=s[g++],ee(b,b,n),s[y++]=b[0],s[y++]=b[1]}}e.increaseVersion()}this.dirtyShape()},t}var lu,uu=function(){this.cx=0,this.cy=0,this.r=0},hu=(u(cu,lu=j),cu.prototype.getDefaultShape=function(){return new uu},cu.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},cu);function cu(t){return lu.call(this,t)||this}hu.prototype.type="circle";var pu,du=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},fu=(u(gu,pu=j),gu.prototype.getDefaultShape=function(){return new du},gu.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=e.rx,e=e.ry,o=.5522848*r,a=.5522848*e;t.moveTo(n-r,i),t.bezierCurveTo(n-r,i-a,n-o,i-e,n,i-e),t.bezierCurveTo(n+o,i-e,n+r,i-a,n+r,i),t.bezierCurveTo(n+r,i+a,n+o,i+e,n,i+e),t.bezierCurveTo(n-o,i+e,n-r,i+a,n-r,i),t.closePath()},gu);function gu(t){return pu.call(this,t)||this}fu.prototype.type="ellipse";var yu=Math.PI,mu=2*yu,vu=Math.sin,_u=Math.cos,xu=Math.acos,wu=Math.atan2,bu=Math.abs,Su=Math.sqrt,Mu=Math.max,Tu=Math.min,Cu=1e-4;function Iu(t,e,n,i,r,o,a){var s=t-n,l=e-i,a=(a?o:-o)/Su(s*s+l*l),l=a*l,a=-a*s,s=t+l,t=e+a,e=n+l,n=i+a,i=(s+e)/2,u=(t+n)/2,h=e-s,c=n-t,p=h*h+c*c,o=r-o,s=s*n-e*t,n=(c<0?-1:1)*Su(Mu(0,o*o*p-s*s)),e=(s*c-h*n)/p,t=(-s*h-c*n)/p,d=(s*c+h*n)/p,s=(-s*h+c*n)/p,h=e-i,c=t-u,n=d-i,p=s-u;return n*n+p*p<h*h+c*c&&(e=d,t=s),{cx:e,cy:t,x0:-l,y0:-a,x1:e*(r/o-1),y1:t*(r/o-1)}}function ku(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g,y,m,v,_,x,w,b,S,M,T,C,I,k,D,A,P,L=Mu(e.r,0),O=Mu(e.r0||0,0),R=0<L;(R||0<O)&&(R||(L=O,O=0),L<O&&(R=L,L=O,O=R),R=e.startAngle,n=e.endAngle,isNaN(R)||isNaN(n)||(i=e.cx,r=e.cy,o=!!e.clockwise,m=bu(n-R),Cu<(a=mu<m&&m%mu)&&(m=a),Cu<L?mu-Cu<m?(t.moveTo(i+L*_u(R),r+L*vu(R)),t.arc(i,r,L,R,n,!o),Cu<O&&(t.moveTo(i+O*_u(n),r+O*vu(n)),t.arc(i,r,O,n,R,o))):(S=b=w=x=_=v=c=h=I=C=T=M=u=l=s=a=void 0,p=L*_u(R),d=L*vu(R),f=O*_u(n),g=O*vu(n),(y=Cu<m)&&((e=e.cornerRadius)&&(a=(e=function(t){if(F(t)){var e=t.length;if(!e)return t;e=1===e?[t[0],t[0],0,0]:2===e?[t[0],t[0],t[1],t[1]]:3===e?t.concat(t[2]):t}else e=[t,t,t,t];return e}(e))[0],s=e[1],l=e[2],u=e[3]),e=bu(L-O)/2,M=Tu(e,l),T=Tu(e,u),C=Tu(e,a),I=Tu(e,s),v=h=Mu(M,T),_=c=Mu(C,I),Cu<h||Cu<c)&&(x=L*_u(n),w=L*vu(n),b=O*_u(R),S=O*vu(R),m<yu)&&(e=function(t,e,n,i,r,o,a,s){var l=(s=s-o)*(n=n-t)-(a=a-r)*(i=i-e);if(!(l*l<Cu))return[t+(l=(a*(e-o)-s*(t-r))/l)*n,e+l*i]}(p,d,b,S,x,w,f,g))&&(M=p-e[0],T=d-e[1],C=x-e[0],I=w-e[1],m=1/vu(xu((M*C+T*I)/(Su(M*M+T*T)*Su(C*C+I*I)))/2),M=Su(e[0]*e[0]+e[1]*e[1]),v=Tu(h,(L-M)/(1+m)),_=Tu(c,(O-M)/(m-1))),y?Cu<v?(k=Tu(l,v),D=Tu(u,v),A=Iu(b,S,p,d,L,k,o),P=Iu(x,w,f,g,L,D,o),t.moveTo(i+A.cx+A.x0,r+A.cy+A.y0),v<h&&k===D?t.arc(i+A.cx,r+A.cy,v,wu(A.y0,A.x0),wu(P.y0,P.x0),!o):(0<k&&t.arc(i+A.cx,r+A.cy,k,wu(A.y0,A.x0),wu(A.y1,A.x1),!o),t.arc(i,r,L,wu(A.cy+A.y1,A.cx+A.x1),wu(P.cy+P.y1,P.cx+P.x1),!o),0<D&&t.arc(i+P.cx,r+P.cy,D,wu(P.y1,P.x1),wu(P.y0,P.x0),!o))):(t.moveTo(i+p,r+d),t.arc(i,r,L,R,n,!o)):t.moveTo(i+p,r+d),Cu<O&&y?Cu<_?(k=Tu(a,_),A=Iu(f,g,x,w,O,-(D=Tu(s,_)),o),P=Iu(p,d,b,S,O,-k,o),t.lineTo(i+A.cx+A.x0,r+A.cy+A.y0),_<c&&k===D?t.arc(i+A.cx,r+A.cy,_,wu(A.y0,A.x0),wu(P.y0,P.x0),!o):(0<D&&t.arc(i+A.cx,r+A.cy,D,wu(A.y0,A.x0),wu(A.y1,A.x1),!o),t.arc(i,r,O,wu(A.cy+A.y1,A.cx+A.x1),wu(P.cy+P.y1,P.cx+P.x1),o),0<k&&t.arc(i+P.cx,r+P.cy,k,wu(P.y1,P.x1),wu(P.y0,P.x0),!o))):(t.lineTo(i+f,r+g),t.arc(i,r,O,n,R,o)):t.lineTo(i+f,r+g)):t.moveTo(i,r),t.closePath()))}var Du,Au=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},Pu=(u(Lu,Du=j),Lu.prototype.getDefaultShape=function(){return new Au},Lu.prototype.buildPath=function(t,e){ku(t,e)},Lu.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},Lu);function Lu(t){return Du.call(this,t)||this}Pu.prototype.type="sector";var Ou,Ru=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},Nu=(u(Eu,Ou=j),Eu.prototype.getDefaultShape=function(){return new Ru},Eu.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},Eu);function Eu(t){return Ou.call(this,t)||this}function zu(t,e,n){var i=e.smooth,r=e.points;if(r&&2<=r.length){if(i)for(var o=function(t,e,n,i){var r,o,a=[],s=[],l=[],u=[];if(i){for(var h=[1/0,1/0],c=[-1/0,-1/0],p=0,d=t.length;p<d;p++)ne(h,h,t[p]),ie(c,c,t[p]);ne(h,h,i[0]),ie(c,c,i[1])}for(p=0,d=t.length;p<d;p++){var f=t[p];if(n)r=t[p?p-1:d-1],o=t[(p+1)%d];else{if(0===p||p===d-1){a.push(Wt(t[p]));continue}r=t[p-1],o=t[p+1]}Xt(s,o,r),Zt(s,s,e);var g=Kt(f,r),y=Kt(f,o),m=g+y,m=(0!==m&&(g/=m,y/=m),Zt(l,s,-g),Zt(u,s,y),Ut([],f,l)),g=Ut([],f,u);i&&(ie(m,m,h),ne(m,m,c),ie(g,g,h),ne(g,g,c)),a.push(m),a.push(g)}return n&&a.push(a.shift()),a}(r,i,n,e.smoothConstraint),a=(t.moveTo(r[0][0],r[0][1]),r.length),s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}else{t.moveTo(r[0][0],r[0][1]);for(var s=1,c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}Nu.prototype.type="ring";var Bu,Fu=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},Vu=(u(Hu,Bu=j),Hu.prototype.getDefaultShape=function(){return new Fu},Hu.prototype.buildPath=function(t,e){zu(t,e,!0)},Hu);function Hu(t){return Bu.call(this,t)||this}Vu.prototype.type="polygon";var Gu,Wu=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},Uu=(u(Xu,Gu=j),Xu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Xu.prototype.getDefaultShape=function(){return new Wu},Xu.prototype.buildPath=function(t,e){zu(t,e,!1)},Xu);function Xu(t){return Gu.call(this,t)||this}Uu.prototype.type="polyline";var Yu,qu={},Zu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},ju=(u(Ku,Yu=j),Ku.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Ku.prototype.getDefaultShape=function(){return new Zu},Ku.prototype.buildPath=function(t,e){o=(this.subPixelOptimize?(n=(o=Ms(qu,e,this.style)).x1,i=o.y1,r=o.x2,o):(n=e.x1,i=e.y1,r=e.x2,e)).y2;var n,i,r,o,e=e.percent;0!==e&&(t.moveTo(n,i),e<1&&(r=n*(1-e)+r*e,o=i*(1-e)+o*e),t.lineTo(r,o))},Ku.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},Ku);function Ku(t){return Yu.call(this,t)||this}ju.prototype.type="line";var $u=[],Qu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function Ju(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?Bn:zn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?Bn:zn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?Un:Wn)(t.x1,t.cpx1,t.x2,e),(n?Un:Wn)(t.y1,t.cpy1,t.y2,e)]}u(nh,th=j),nh.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},nh.prototype.getDefaultShape=function(){return new Qu},nh.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,e=e.percent;0!==e&&(t.moveTo(n,i),null==l||null==u?(e<1&&(Yn(n,a,r,e,$u),a=$u[1],r=$u[2],Yn(i,s,o,e,$u),s=$u[1],o=$u[2]),t.quadraticCurveTo(a,s,r,o)):(e<1&&(Hn(n,a,l,r,e,$u),a=$u[1],l=$u[2],r=$u[3],Hn(i,s,u,o,e,$u),s=$u[1],u=$u[2],o=$u[3]),t.bezierCurveTo(a,s,l,u,r,o)))},nh.prototype.pointAt=function(t){return Ju(this.shape,t,!1)},nh.prototype.tangentAt=function(t){t=Ju(this.shape,t,!0);return jt(t,t)};var th,eh=nh;function nh(t){return th.call(this,t)||this}eh.prototype.type="bezier-curve";var ih,rh=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},oh=(u(ah,ih=j),ah.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},ah.prototype.getDefaultShape=function(){return new rh},ah.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,e=e.clockwise,s=Math.cos(o),l=Math.sin(o);t.moveTo(s*r+n,l*r+i),t.arc(n,i,r,o,a,!e)},ah);function ah(t){return ih.call(this,t)||this}oh.prototype.type="arc";u(uh,sh=j),uh.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},uh.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},uh.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},uh.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},uh.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),j.prototype.getBoundingRect.call(this)};var sh,lh=uh;function uh(){var t=null!==sh&&sh.apply(this,arguments)||this;return t.type="compound",t}ch.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})};var hh=ch;function ch(t){this.colorStops=t||[]}u(fh,ph=hh);var ph,dh=fh;function fh(t,e,n,i,r,o){r=ph.call(this,r)||this;return r.x=null==t?0:t,r.y=null==e?0:e,r.x2=null==n?1:n,r.y2=null==i?0:i,r.type="linear",r.global=o||!1,r}u(yh,gh=hh);var gh,hh=yh;function yh(t,e,n,i,r){i=gh.call(this,i)||this;return i.x=null==t?.5:t,i.y=null==e?.5:e,i.r=null==n?.5:n,i.type="radial",i.global=r||!1,i}var mh=[0,0],vh=[0,0],_h=new M,xh=new M,wh=(bh.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width,t=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,t),n[3].set(r,t),e)for(var s=0;s<4;s++)n[s].transform(e);M.sub(i[0],n[1],n[0]),M.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(s=0;s<2;s++)this._origin[s]=i[s].dot(n[0])},bh.prototype.intersect=function(t,e){var n=!0,i=!e;return _h.set(1/0,1/0),xh.set(0,0),!this._intersectCheckOneSide(this,t,_h,xh,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,_h,xh,i,-1)&&(n=!1,i)||i||M.copy(e,n?_h:xh),n},bh.prototype._intersectCheckOneSide=function(t,e,n,i,r,o){for(var a=!0,s=0;s<2;s++){var l=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,mh),this._getProjMinMaxOnAxis(s,e._corners,vh),mh[1]<vh[0]||vh[1]<mh[0]){if(a=!1,r)return a;var u=Math.abs(vh[0]-mh[1]),h=Math.abs(mh[0]-vh[1]);Math.min(u,h)>i.len()&&(u<h?M.scale(i,l,-u*o):M.scale(i,l,h*o))}else n&&(u=Math.abs(vh[0]-mh[1]),h=Math.abs(mh[0]-vh[1]),Math.min(u,h)<n.len())&&(u<h?M.scale(n,l,u*o):M.scale(n,l,-h*o))}return a},bh.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++)var u=e[l].dot(i)+r[t],a=Math.min(u,a),s=Math.max(u,s);n[0]=a,n[1]=s},bh);function bh(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new M;for(n=0;n<2;n++)this._axes[n]=new M;t&&this.fromBoundingRect(t,e)}var Sh,Mh=[],n=(u(Th,Sh=n),Th.prototype.traverse=function(t,e){t.call(e,this)},Th.prototype.useStyle=function(){this.style={}},Th.prototype.getCursor=function(){return this._cursor},Th.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},Th.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},Th.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},Th.prototype.addDisplayable=function(t,e){(e?this._temporaryDisplayables:this._displayables).push(t),this.markRedraw()},Th.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},Th.prototype.getDisplayables=function(){return this._displayables},Th.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},Th.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},Th.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;for(var e,t=0;t<this._temporaryDisplayables.length;t++)(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null},Th.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new X(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(Mh)),t.union(i)}this._rect=t}return this._rect},Th.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},Th);function Th(){var t=null!==Sh&&Sh.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}var Ch=Lo();function Ih(t,e,n,i,r,o,a){var s,l,u,h,c,p,d=!1,f=(k(r)?(a=o,o=r,r=null):R(r)&&(o=r.cb,a=r.during,d=r.isFrom,l=r.removeOpt,r=r.dataIndex),"leave"===t),g=(f||e.stopAnimation("leave"),p=t,s=r,l=f?l||{}:null,i=(g=i)&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null,g&&g.ecModel&&(u=(u=g.ecModel.getUpdatePayload())&&u.animation),p="update"===p,g&&g.isAnimationEnabled()?(c=h=r=void 0,c=l?(r=N(l.duration,200),h=N(l.easing,"cubicOut"),0):(r=g.getShallow(p?"animationDurationUpdate":"animationDuration"),h=g.getShallow(p?"animationEasingUpdate":"animationEasing"),g.getShallow(p?"animationDelayUpdate":"animationDelay")),k(c=u&&(null!=u.duration&&(r=u.duration),null!=u.easing&&(h=u.easing),null!=u.delay)?u.delay:c)&&(c=c(s,i)),{duration:(r=k(r)?r(s):r)||0,delay:c,easing:h}):null);g&&0<g.duration?(p={duration:g.duration,delay:g.delay||0,easing:g.easing,done:o,force:!!o||!!a,setToFinal:!f,scope:t,during:a},d?e.animateFrom(n,p):e.animateTo(n,p)):(e.stopAnimation(),d||e.attr(n),a&&a(1),o&&o())}function kh(t,e,n,i,r,o){Ih("update",t,e,n,i,r,o)}function Dh(t,e,n,i,r,o){Ih("enter",t,e,n,i,r,o)}function Ah(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++)if("leave"===t.animators[e].scope)return!0;return!1}function Ph(t,e,n,i,r,o){Ah(t)||Ih("leave",t,e,n,i,r,o)}function Lh(t,e,n,i){t.removeTextContent(),t.removeTextGuideLine(),Ph(t,{style:{opacity:0}},e,n,i)}function Oh(t,e,n){function i(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse(function(t){t.isGroup||Lh(t,e,n,i)}):Lh(t,e,n,i)}function Rh(t){Ch(t).oldStyle=t.style}var Nh=Math.max,Eh=Math.min,zh={};function Bh(t){return j.extend(t)}var Fh=function(t,e){var n,i=su(t,e);function r(t){t=n.call(this,t)||this;return t.applyTransform=i.applyTransform,t.buildPath=i.buildPath,t}return u(r,n=ru),r};function Vh(t,e){return Fh(t,e)}function Hh(t,e){zh[t]=e}function Gh(t){if(zh.hasOwnProperty(t))return zh[t]}function Wh(t,e,n,i){t=new ru(su(t,e));return n&&("center"===i&&(n=Xh(n,t.getBoundingRect())),qh(t,n)),t}function Uh(t,e,n){var i=new ws({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){"center"===n&&(t={width:t.width,height:t.height},i.setStyle(Xh(e,t)))}});return i}function Xh(t,e){var e=e.width/e.height,n=t.height*e,e=n<=t.width?t.height:(n=t.width)/e;return{x:t.x+t.width/2-n/2,y:t.y+t.height/2-e/2,width:n,height:e}}function Yh(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}return(e=new j(e)).createPathProxy(),e.buildPath=function(t){var e;au(t)&&(t.appendPath(n),e=t.getContext())&&t.rebuildPath(e,1)},e}function qh(t,e){t.applyTransform&&(e=t.getBoundingRect().calculateTransform(e),t.applyTransform(e))}function Zh(t,e){return Ms(t,t,{lineWidth:e}),t}var jh=Cs;function Kh(t,e){for(var n=Oe([]);t&&t!==e;)Ne(n,t.getLocalTransform(),n),t=t.parent;return n}function $h(t,e,n){return e&&!st(e)&&(e=vr.getLocalTransform(e)),ee([],t,e=n?Fe([],e):e)}function Qh(t){return!t.isGroup}function Jh(t,e,i){var r,n;function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};return null!=t.shape&&(e.shape=L({},t.shape)),e}t&&e&&(n={},t.traverse(function(t){Qh(t)&&t.anid&&(n[t.anid]=t)}),r=n,e.traverse(function(t){var e,n;Qh(t)&&t.anid&&(e=r[t.anid])&&(n=o(t),t.attr(o(e)),kh(t,n,i,D(t).dataIndex))}))}function tc(t,n){return B(t,function(t){var e=t[0],e=Nh(e,n.x),t=(e=Eh(e,n.x+n.width),t[1]),t=Nh(t,n.y);return[e,Eh(t,n.y+n.height)]})}function ec(t,e){var n=Nh(t.x,e.x),i=Eh(t.x+t.width,e.x+e.width),r=Nh(t.y,e.y),t=Eh(t.y+t.height,e.y+e.height);if(n<=i&&r<=t)return{x:n,y:r,width:i-n,height:t-r}}function nc(t,e,n){var e=L({rectHover:!0},e),i=e.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),z(i,n),new ws(e)):Wh(t.replace("path://",""),e,n,"center")}function ic(t,e,n,i,r,o,a,s){var l,n=n-t,i=i-e,a=a-r,s=s-o,u=a*i-n*s;return!((l=u)<=1e-6&&-1e-6<=l||(r=((l=t-r)*i-n*(t=e-o))/u)<0||1<r||(i=(l*s-a*t)/u)<0||1<i)}function rc(t){var e=t.itemTooltipOption,n=t.componentModel,i=t.itemName,e=V(e)?{formatter:e}:e,r=n.mainType,n=n.componentIndex,o={componentType:r,name:i,$vars:["name"]},a=(o[r+"Index"]=n,t.formatterParamsExtra),t=(a&&O(ht(a),function(t){Bt(o,t)||(o[t]=a[t],o.$vars.push(t))}),D(t.el));t.componentMainType=r,t.componentIndex=n,t.tooltipConfig={name:i,option:z({content:i,encodeHTMLContent:!0,formatterParams:o},e)}}function oc(t,e){var n;(n=t.isGroup?e(t):n)||t.traverse(e)}function ac(t,e){if(t)if(F(t))for(var n=0;n<t.length;n++)oc(t[n],e);else oc(t,e)}Hh("circle",hu),Hh("ellipse",fu),Hh("sector",Pu),Hh("ring",Nu),Hh("polygon",Vu),Hh("polyline",Uu),Hh("rect",As),Hh("line",ju),Hh("bezierCurve",eh),Hh("arc",oh);var sc=Object.freeze({__proto__:null,Arc:oh,BezierCurve:eh,BoundingRect:X,Circle:hu,CompoundPath:lh,Ellipse:fu,Group:Gr,Image:ws,IncrementalDisplayable:n,Line:ju,LinearGradient:dh,OrientedBoundingRect:wh,Path:j,Point:M,Polygon:Vu,Polyline:Uu,RadialGradient:hh,Rect:As,Ring:Nu,Sector:Pu,Text:Ns,applyTransform:$h,clipPointsByRect:tc,clipRectByRect:ec,createIcon:nc,extendPath:Vh,extendShape:Bh,getShapeClass:Gh,getTransform:Kh,groupTransition:Jh,initProps:Dh,isElementRemoved:Ah,lineLineIntersect:ic,linePolygonIntersect:function(t,e,n,i,r){for(var o=0,a=r[r.length-1];o<r.length;o++){var s=r[o];if(ic(t,e,n,i,s[0],s[1],a[0],a[1]))return!0;a=s}},makeImage:Uh,makePath:Wh,mergePath:Yh,registerShape:Hh,removeElement:Ph,removeElementWithFadeOut:Oh,resizePath:qh,setTooltipConfig:rc,subPixelOptimize:jh,subPixelOptimizeLine:Zh,subPixelOptimizeRect:function(t){return Ts(t.shape,t.shape,t.style),t},transformDirection:function(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),i=$h(["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0],e,n);return Math.abs(i[0])>Math.abs(i[1])?0<i[0]?"right":"left":0<i[1]?"bottom":"top"},traverseElements:ac,updateProps:kh}),lc={};function uc(t,e){for(var n=0;n<tl.length;n++){var i=tl[n],r=e[i],i=t.ensureState(i);i.style=i.style||{},i.style.text=r}var o=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(o,!0)}function hc(t,e,n){for(var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal,l={normal:i=null==(i=r?r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null):i)?k(t.defaultText)?t.defaultText(o,t,n):t.defaultText:i},u=0;u<tl.length;u++){var h=tl[u],c=e[h];l[h]=N(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function cc(t,e,n,i){n=n||lc;for(var r=t instanceof Ns,o=!1,a=0;a<el.length;a++)if((p=e[el[a]])&&p.getShallow("show")){o=!0;break}var s=r?t:t.getTextContent();if(o){r||(s||(s=new Ns,t.setTextContent(s)),t.stateProxy&&(s.stateProxy=t.stateProxy));var l=hc(n,e),u=e.normal,h=!!u.getShallow("show"),c=dc(u,i&&i.normal,n,!1,!r);c.text=l.normal,r||t.setTextConfig(fc(u,n,!1));for(a=0;a<tl.length;a++){var p,d,f,g=tl[a];(p=e[g])&&(d=s.ensureState(g),(f=!!N(p.getShallow("show"),h))!=h&&(d.ignore=!f),d.style=dc(p,i&&i[g],n,!0,!r),d.style.text=l[g],r||(t.ensureState(g).textConfig=fc(p,n,!0)))}s.silent=!!u.getShallow("silent"),null!=s.style.x&&(c.x=s.style.x),null!=s.style.y&&(c.y=s.style.y),s.ignore=!h,s.useStyle(c),s.dirty(),n.enableTextSetter&&(_c(s).setLabelText=function(t){t=hc(n,e,t);uc(s,t)})}else s&&(s.ignore=!0);t.dirty()}function pc(t,e){for(var n={normal:t.getModel(e=e||"label")},i=0;i<tl.length;i++){var r=tl[i];n[r]=t.getModel([r,e])}return n}function dc(t,e,n,i,r){var o,a={},s=a,l=t,u=n,h=i,c=r;u=u||lc;var p,t=l.ecModel,d=t&&t.option.textStyle,f=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||lc).rich;if(n){e=e||{};for(var i=ht(n),r=0;r<i.length;r++){var o=i[r];e[o]=1}}t=t.parentModel}return e}(l);if(f)for(var g in p={},f)f.hasOwnProperty(g)&&(o=l.getModel(["rich",g]),vc(p[g]={},o,d,u,h,c,!1,!0));return p&&(s.rich=p),(t=l.get("overflow"))&&(s.overflow=t),null!=(t=l.get("minMargin"))&&(s.margin=t),vc(s,l,d,u,h,c,!0,!1),e&&L(a,e),a}function fc(t,e,n){e=e||{};var i={},r=t.getShallow("rotate"),o=N(t.getShallow("distance"),n?null:5),a=t.getShallow("offset"),n=t.getShallow("position")||(n?null:"inside");return null!=(n="outside"===n?e.defaultOutsidePosition||"top":n)&&(i.position=n),null!=a&&(i.offset=a),null!=r&&(r*=Math.PI/180,i.rotation=r),null!=o&&(i.distance=o),i.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",i}var gc=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],yc=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],mc=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function vc(t,e,n,i,r,o,a,s){n=!r&&n||lc;var l=i&&i.inheritColor,u=e.getShallow("color"),h=e.getShallow("textBorderColor"),c=N(e.getShallow("opacity"),n.opacity),u=("inherit"!==u&&"auto"!==u||(u=l||null),"inherit"!==h&&"auto"!==h||(h=l||null),o||(u=u||n.color,h=h||n.textBorderColor),null!=u&&(t.fill=u),null!=h&&(t.stroke=h),N(e.getShallow("textBorderWidth"),n.textBorderWidth)),h=(null!=u&&(t.lineWidth=u),N(e.getShallow("textBorderType"),n.textBorderType)),u=(null!=h&&(t.lineDash=h),N(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset));null!=u&&(t.lineDashOffset=u),null!=(c=r||null!=c||s?c:i&&i.defaultOpacity)&&(t.opacity=c),r||o||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var p=0;p<gc.length;p++){var d=gc[p];null!=(f=N(e.getShallow(d),n[d]))&&(t[d]=f)}for(var p=0;p<yc.length;p++){d=yc[p];null!=(f=e.getShallow(d))&&(t[d]=f)}if(null==t.verticalAlign&&null!=(h=e.getShallow("baseline"))&&(t.verticalAlign=h),!a||!i.disableBox){for(p=0;p<mc.length;p++){var f,d=mc[p];null!=(f=e.getShallow(d))&&(t[d]=f)}u=e.getShallow("borderType");null!=u&&(t.borderDash=u),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!l||(t.backgroundColor=l),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!l||(t.borderColor=l)}}var _c=Lo();function xc(n,i,r,t,o){var a,s,l,u=_c(n);u.valueAnimation&&u.prevValue!==u.value&&(a=u.defaultInterpolatedText,s=N(u.interpolatedValue,u.prevValue),l=u.value,n.percent=0,(null==u.prevValue?Dh:kh)(n,{percent:1},t,i,null,function(t){var e=Fo(r,u.precision,s,l,t),t=(u.interpolatedValue=1===t?null:e,hc({labelDataIndex:i,labelFetcher:o,defaultText:a?a(e):e+""},u.statesModels,e));uc(n,t)}))}var wc=["textStyle","color"],bc=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],Sc=new Ns,jh=(Mc.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(wc):null)},Mc.prototype.getFont=function(){return t={fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},e=(e=this.ecModel)&&e.getModel("textStyle"),Ct([t.fontStyle||e&&e.getShallow("fontStyle")||"",t.fontWeight||e&&e.getShallow("fontWeight")||"",(t.fontSize||e&&e.getShallow("fontSize")||12)+"px",t.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "));var t,e},Mc.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<bc.length;n++)e[bc[n]]=this.getShallow(bc[n]);return Sc.useStyle(e),Sc.update(),Sc.getBoundingRect()},Mc);function Mc(){}var Tc=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],Cc=Ko(Tc),Ic=(kc.prototype.getLineStyle=function(t){return Cc(this,t)},kc);function kc(){}var Dc=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],Ac=Ko(Dc),Pc=(Lc.prototype.getItemStyle=function(t,e){return Ac(this,t,e)},Lc);function Lc(){}Nc.prototype.init=function(t,e,n){},Nc.prototype.mergeOption=function(t,e){d(this.option,t,!0)},Nc.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},Nc.prototype.getShallow=function(t,e){var n=this.option,n=null==n?n:n[t];return null!=n||e||(e=this.parentModel)&&(n=e.getShallow(t)),n},Nc.prototype.getModel=function(t,e){var n=null!=t,t=n?this.parsePath(t):null;return new Nc(n?this._doGet(t):this.option,e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(t)),this.ecModel)},Nc.prototype.isEmpty=function(){return null==this.option},Nc.prototype.restoreData=function(){},Nc.prototype.clone=function(){return new this.constructor(y(this.option))},Nc.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},Nc.prototype.resolveParentPath=function(t){return t},Nc.prototype.isAnimationEnabled=function(){if(!b.node&&this.option)return null!=this.option.animation?!!this.option.animation:this.parentModel?this.parentModel.isAnimationEnabled():void 0},Nc.prototype._doGet=function(t,e){var n=this.option;if(t){for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==typeof n?n[t[i]]:null));i++);null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel))}return n};var Oc,Rc=Nc;function Nc(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}Uo(Rc),$c=Rc,Oc=["__\0is_clz",Yo++].join("_"),$c.prototype[Oc]=!0,$c.isInstance=function(t){return!(!t||!t[Oc])},at(Rc,Ic),at(Rc,Pc),at(Rc,Qo),at(Rc,jh);var Ec=Math.round(10*Math.random());function zc(t){return[t||"",Ec++].join("_")}function Bc(t,e){return d(d({},t,!0),e,!0)}var Fc="ZH",Vc="EN",Hc=Vc,Gc={},Wc={},Uc=b.domSupported&&-1<(document.documentElement.lang||navigator.language||navigator.browserLanguage||Hc).toUpperCase().indexOf(Fc)?Fc:Hc;function Xc(t,e){t=t.toUpperCase(),Wc[t]=new Rc(e),Gc[t]=e}Xc(Vc,{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),Xc(Fc,{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var Yc=1e3,qc=60*Yc,Zc=60*qc,jc=24*Zc,Yo=365*jc,Kc={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},$c="{yyyy}-{MM}-{dd}",Qc={year:"{yyyy}",month:"{yyyy}-{MM}",day:$c,hour:$c+" "+Kc.hour,minute:$c+" "+Kc.minute,second:$c+" "+Kc.second,millisecond:Kc.none},Jc=["year","month","day","hour","minute","second","millisecond"],tp=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function ep(t,e){return"0000".substr(0,e-(t+="").length)+t}function np(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function ip(t,e,n,i){var t=uo(t),r=t[ap(n)](),o=t[sp(n)]()+1,a=Math.floor((o-1)/3)+1,s=t[lp(n)](),l=t["get"+(n?"UTC":"")+"Day"](),u=t[up(n)](),h=(u-1)%12+1,c=t[hp(n)](),p=t[cp(n)](),t=t[pp(n)](),n=12<=u?"pm":"am",d=n.toUpperCase(),i=(i instanceof Rc?i:Wc[i||Uc]||Wc[Hc]).getModel("time"),f=i.get("month"),g=i.get("monthAbbr"),y=i.get("dayOfWeek"),i=i.get("dayOfWeekAbbr");return(e||"").replace(/{a}/g,n+"").replace(/{A}/g,d+"").replace(/{yyyy}/g,r+"").replace(/{yy}/g,ep(r%100+"",2)).replace(/{Q}/g,a+"").replace(/{MMMM}/g,f[o-1]).replace(/{MMM}/g,g[o-1]).replace(/{MM}/g,ep(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,ep(s,2)).replace(/{d}/g,s+"").replace(/{eeee}/g,y[l]).replace(/{ee}/g,i[l]).replace(/{e}/g,l+"").replace(/{HH}/g,ep(u,2)).replace(/{H}/g,u+"").replace(/{hh}/g,ep(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,ep(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,ep(p,2)).replace(/{s}/g,p+"").replace(/{SSS}/g,ep(t,3)).replace(/{S}/g,t+"")}function rp(t,e){var t=uo(t),n=t[sp(e)]()+1,i=t[lp(e)](),r=t[up(e)](),o=t[hp(e)](),a=t[cp(e)](),t=0===t[pp(e)](),e=t&&0===a,a=e&&0===o,o=a&&0===r,r=o&&1===i;return r&&1===n?"year":r?"month":o?"day":a?"hour":e?"minute":t?"second":"millisecond"}function op(t,e,n){var i=H(t)?uo(t):t;switch(e=e||rp(t,n)){case"year":return i[ap(n)]();case"half-year":return 6<=i[sp(n)]()?1:0;case"quarter":return Math.floor((i[sp(n)]()+1)/4);case"month":return i[sp(n)]();case"day":return i[lp(n)]();case"half-day":return i[up(n)]()/24;case"hour":return i[up(n)]();case"minute":return i[hp(n)]();case"second":return i[cp(n)]();case"millisecond":return i[pp(n)]()}}function ap(t){return t?"getUTCFullYear":"getFullYear"}function sp(t){return t?"getUTCMonth":"getMonth"}function lp(t){return t?"getUTCDate":"getDate"}function up(t){return t?"getUTCHours":"getHours"}function hp(t){return t?"getUTCMinutes":"getMinutes"}function cp(t){return t?"getUTCSeconds":"getSeconds"}function pp(t){return t?"getUTCMilliseconds":"getMilliseconds"}function dp(t){return t?"setUTCMonth":"setMonth"}function fp(t){return t?"setUTCDate":"setDate"}function gp(t){return t?"setUTCHours":"setHours"}function yp(t){return t?"setUTCMinutes":"setMinutes"}function mp(t){return t?"setUTCSeconds":"setSeconds"}function vp(t){return t?"setUTCMilliseconds":"setMilliseconds"}function _p(t){var e;return go(t)?(e=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(1<e.length?"."+e[1]:""):V(t)?t:"-"}function xp(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),t=e?t&&t.charAt(0).toUpperCase()+t.slice(1):t}var wp=Mt;function bp(t,e,n){function i(t){return t&&Ct(t)?t:"-"}function r(t){return null!=t&&!isNaN(t)&&isFinite(t)}var o="time"===e,a=t instanceof Date;if(o||a){var o=o?uo(t):t;if(!isNaN(+o))return ip(o,"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}",n);if(a)return"-"}return"ordinal"===e?dt(t)?i(t):H(t)&&r(t)?t+"":"-":r(o=fo(t))?_p(o):dt(t)?i(t):"boolean"==typeof t?t+"":"-"}function Sp(t,e){return"{"+t+(null==e?"":e)+"}"}var Mp=["a","b","c","d","e","f","g"];function Tp(t,e,n){var i=(e=F(e)?e:[e]).length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=Mp[o];t=t.replace(Sp(a),Sp(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(Sp(Mp[l],s),n?_e(u):u)}return t}function Cp(t,e){var t=V(t)?{color:t,extraCssText:e}:t||{},n=t.color,i=t.type,r=(e=t.extraCssText,t.renderMode||"html");return n?"html"===r?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+_e(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+_e(n)+";"+(e||"")+'"></span>':{renderMode:r,content:"{"+(t.markerId||"markerX")+"|}  ",style:"subItem"===i?{width:4,height:4,borderRadius:2,backgroundColor:n}:{width:10,height:10,borderRadius:5,backgroundColor:n}}:""}function Ip(t,e){return e=e||"transparent",V(t)?t:R(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}function kp(t,e){var n;"_blank"===e||"blank"===e?((n=window.open()).opener=null,n.location.href=t):window.open(t,e)}var Dp=O,Ap=["left","right","top","bottom","width","height"],Pp=[["width","left","right"],["height","top","bottom"]];function Lp(a,s,l,u,h){var c=0,p=0,d=(null==u&&(u=1/0),null==h&&(h=1/0),0);s.eachChild(function(t,e){var n,i,r,o=t.getBoundingRect(),e=s.childAt(e+1),e=e&&e.getBoundingRect();d="horizontal"===a?(i=o.width+(e?-e.x+o.x:0),u<(n=c+i)||t.newline?(c=0,n=i,p+=d+l,o.height):Math.max(d,o.height)):(i=o.height+(e?-e.y+o.y:0),h<(r=p+i)||t.newline?(c+=d+l,p=0,r=i,o.width):Math.max(d,o.width)),t.newline||(t.x=c,t.y=p,t.markRedraw(),"horizontal"===a?c=n+l:p=r+l)})}var Op=Lp;function Rp(t,e,n){n=wp(n||0);var i=e.width,r=e.height,o=eo(t.left,i),a=eo(t.top,r),e=eo(t.right,i),s=eo(t.bottom,r),l=eo(t.width,i),u=eo(t.height,r),h=n[2]+n[0],c=n[1]+n[3],p=t.aspect;switch(isNaN(l)&&(l=i-e-c-o),isNaN(u)&&(u=r-s-h-a),null!=p&&(isNaN(l)&&isNaN(u)&&(i/r<p?l=.8*i:u=.8*r),isNaN(l)&&(l=p*u),isNaN(u))&&(u=l/p),isNaN(o)&&(o=i-e-l-c),isNaN(a)&&(a=r-s-u-h),t.left||t.right){case"center":o=i/2-l/2-n[3];break;case"right":o=i-l-c}switch(t.top||t.bottom){case"middle":case"center":a=r/2-u/2-n[0];break;case"bottom":a=r-u-h}o=o||0,a=a||0,isNaN(l)&&(l=i-c-o-(e||0)),isNaN(u)&&(u=r-h-a-(s||0));p=new X(o+n[3],a+n[0],l,u);return p.margin=n,p}function Np(t){t=t.layoutMode||t.constructor.layoutMode;return R(t)?t:t?{type:t}:null}function Ep(l,u,t){var h=t&&t.ignoreSize,t=(F(h)||(h=[h,h]),n(Pp[0],0)),e=n(Pp[1],1);function n(t,e){var n={},i=0,r={},o=0;if(Dp(t,function(t){r[t]=l[t]}),Dp(t,function(t){c(u,t)&&(n[t]=r[t]=u[t]),p(n,t)&&i++,p(r,t)&&o++}),h[e])p(u,t[1])?r[t[2]]=null:p(u,t[2])&&(r[t[1]]=null);else if(2!==o&&i){if(!(2<=i))for(var a=0;a<t.length;a++){var s=t[a];if(!c(n,s)&&c(l,s)){n[s]=l[s];break}}return n}return r}function c(t,e){return t.hasOwnProperty(e)}function p(t,e){return null!=t[e]&&"auto"!==t[e]}function i(t,e,n){Dp(t,function(t){e[t]=n[t]})}i(Pp[0],l,t),i(Pp[1],l,e)}function zp(t){return e={},(n=t)&&e&&Dp(Ap,function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e;var e,n}pt(Lp,"vertical"),pt(Lp,"horizontal");var Bp,Fp,Vp,Hp,Gp=Lo(),g=(u(Wp,Bp=Rc),Wp.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},Wp.prototype.mergeDefaultAndTheme=function(t,e){var n=Np(this),i=n?zp(t):{};d(t,e.getTheme().get(this.mainType)),d(t,this.getDefaultOption()),n&&Ep(t,i,n)},Wp.prototype.mergeOption=function(t,e){d(this.option,t,!0);var n=Np(this);n&&Ep(this.option,t,n)},Wp.prototype.optionUpdated=function(t,e){},Wp.prototype.getDefaultOption=function(){var t=this.constructor;if(!(e=t)||!e[Go])return t.defaultOption;var e=Gp(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;0<=a;a--)o=d(o,n[a],!0);e.defaultOption=o}return e.defaultOption},Wp.prototype.getReferringComponents=function(t,e){var n=t+"Id";return zo(this.ecModel,t,{index:this.get(t+"Index",!0),id:this.get(n,!0)},e)},Wp.prototype.getBoxLayoutParams=function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}},Wp.prototype.getZLevelKey=function(){return""},Wp.prototype.setZLevel=function(t){this.option.zlevel=t},Wp.protoInitialize=((Ic=Wp.prototype).type="component",Ic.id="",Ic.name="",Ic.mainType="",Ic.subType="",void(Ic.componentIndex=0)),Wp);function Wp(t,e,n){t=Bp.call(this,t,e,n)||this;return t.uid=zc("ec_cpt_model"),t}function Up(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}Xo(g,Rc),jo(g),Vp={},(Fp=g).registerSubTypeDefaulter=function(t,e){t=Wo(t);Vp[t.main]=e},Fp.determineSubType=function(t,e){var n,i=e.type;return i||(n=Wo(t).main,Fp.hasSubTypes(t)&&Vp[n]&&(i=Vp[n](e))),i},Hp=function(t){var e=[];O(g.getClassesByMainType(t),function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])}),e=B(e,function(t){return Wo(t).main}),"dataset"!==t&&I(e,"dataset")<=0&&e.unshift("dataset");return e},g.topologicalTravel=function(t,e,n,i){if(t.length){a={},s=[],O(o=e,function(n){var e,i,r=Up(a,n),t=r.originalDeps=Hp(n),t=(e=o,i=[],O(t,function(t){0<=I(e,t)&&i.push(t)}),i);r.entryCount=t.length,0===r.entryCount&&s.push(n),O(t,function(t){I(r.predecessor,t)<0&&r.predecessor.push(t);var e=Up(a,t);I(e.successor,t)<0&&e.successor.push(n)})});var o,a,s,e={graph:a,noEntryList:s},r=e.graph,l=e.noEntryList,u={};for(O(t,function(t){u[t]=!0});l.length;){var h=l.pop(),c=r[h],p=!!u[h];p&&(n.call(i,h,c.originalDeps.slice()),delete u[h]),O(c.successor,p?f:d)}O(u,function(){throw new Error("")})}function d(t){r[t].entryCount--,0===r[t].entryCount&&l.push(t)}function f(t){u[t]=!0,d(t)}};var Pc="",Qo=("undefined"!=typeof navigator&&(Pc=navigator.platform||""),"rgba(0, 0, 0, 0.2)"),Xp={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:Qo,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:Qo,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:Qo,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:Qo,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:Qo,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:Qo,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Pc.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},Yp=E(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),qp="original",Zp="arrayRows",jp="objectRows",Kp="keyedColumns",$p="typedArray",Qp="unknown",Jp="column",td="row",ed={Must:1,Might:2,Not:3},nd=Lo();function id(n,t,e){var r,o,a,i,s,l={},u=rd(t);return u&&n&&(r=[],o=[],t=t.ecModel,t=nd(t).datasetMap,u=u.uid+"_"+e.seriesLayoutBy,O(n=n.slice(),function(t,e){t=R(t)?t:n[e]={name:t};"ordinal"===t.type&&null==a&&(a=e,i=c(t)),l[t.name]=[]}),s=t.get(u)||t.set(u,{categoryWayDim:i,valueWayDim:0}),O(n,function(t,e){var n,i=t.name,t=c(t);null==a?(n=s.valueWayDim,h(l[i],n,t),h(o,n,t),s.valueWayDim+=t):a===e?(h(l[i],0,t),h(r,0,t)):(n=s.categoryWayDim,h(l[i],n,t),h(o,n,t),s.categoryWayDim+=t)}),r.length&&(l.itemName=r),o.length)&&(l.seriesName=o),l;function h(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function c(t){t=t.dimsDef;return t?t.length:1}}function rd(t){if(!t.get("data",!0))return zo(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},Eo).models[0]}function od(t,e){var n,i,r,o=t.data,a=t.sourceFormat,s=t.seriesLayoutBy,l=t.dimensionsDefine,u=t.startIndex,h=e;if(!gt(o)){if(l&&(R(l=l[h])?(i=l.name,r=l.type):V(l)&&(i=l)),null!=r)return"ordinal"===r?ed.Must:ed.Not;if(a===Zp){var c=o;if(s===td){for(var p=c[h],d=0;d<(p||[]).length&&d<5;d++)if(null!=(n=_(p[u+d])))return n}else for(d=0;d<c.length&&d<5;d++){var f=c[u+d];if(f&&null!=(n=_(f[h])))return n}}else if(a===jp){var g=o;if(!i)return ed.Not;for(d=0;d<g.length&&d<5;d++)if((m=g[d])&&null!=(n=_(m[i])))return n}else if(a===Kp){l=o;if(!i)return ed.Not;if(!(p=l[i])||gt(p))return ed.Not;for(d=0;d<p.length&&d<5;d++)if(null!=(n=_(p[d])))return n}else if(a===qp)for(var y=o,d=0;d<y.length&&d<5;d++){var m,v=So(m=y[d]);if(!F(v))return ed.Not;if(null!=(n=_(v[h])))return n}}return ed.Not;function _(t){var e=V(t);return null!=t&&Number.isFinite(Number(t))&&""!==t?e?ed.Might:ed.Not:e&&"-"!==t?ed.Must:void 0}}var ad=E();var sd,ld,ud,hd=Lo(),cd=(Lo(),pd.prototype.getColorFromPalette=function(t,e,n){var i=xo(this.get("color",!0)),r=this.get("colorLayer",!0),o=this,a=hd;return a=a(e=e||o),o=a.paletteIdx||0,(e=a.paletteNameMap=a.paletteNameMap||{}).hasOwnProperty(t)?e[t]:(r=(r=n==null||!r?i:dd(r,n))||i)&&r.length?(n=r[o],t&&(e[t]=n),a.paletteIdx=(o+1)%r.length,n):void 0},pd.prototype.clearColorPalette=function(){var t,e;(e=hd)(t=this).paletteIdx=0,e(t).paletteNameMap={}},pd);function pd(){}function dd(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}var fd,gd="\0_ec_inner",yd=(u(s,fd=Rc),s.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new Rc(i),this._locale=new Rc(r),this._optionManager=o},s.prototype.setOption=function(t,e,n){e=_d(e);this._optionManager.setOption(t,n,e),this._resetOption(null,e)},s.prototype.resetOption=function(t,e){return this._resetOption(t,_d(e))},s.prototype._resetOption=function(t,e){var n,i=!1,r=this._optionManager;return t&&"recreate"!==t||(n=r.mountOption("recreate"===t),this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(n,e)):ud(this,n),i=!0),"timeline"!==t&&"media"!==t||this.restoreData(),t&&"recreate"!==t&&"timeline"!==t||(n=r.getTimelineOption(this))&&(i=!0,this._mergeOption(n,e)),t&&"recreate"!==t&&"media"!==t||(n=r.getMediaOption(this)).length&&O(n,function(t){i=!0,this._mergeOption(t,e)},this),i},s.prototype.mergeOption=function(t){this._mergeOption(t,null)},s.prototype._mergeOption=function(i,t){var r=this.option,h=this._componentsMap,c=this._componentsCount,n=[],o=E(),p=t&&t.replaceMergeMainTypeMap;nd(this).datasetMap=E(),O(i,function(t,e){null!=t&&(g.hasClass(e)?e&&(n.push(e),o.set(e,!0)):r[e]=null==r[e]?y(t):d(r[e],t,!0))}),p&&p.each(function(t,e){g.hasClass(e)&&!o.get(e)&&(n.push(e),o.set(e,!0))}),g.topologicalTravel(n,g.getAllClassMainTypes(),function(o){var a,t=function(t,e,n){return(e=(e=ad.get(e))&&e(t))?n.concat(e):n}(this,o,xo(i[o])),e=h.get(o),n=e?p&&p.get(o)?"replaceMerge":"normalMerge":"replaceAll",e=Mo(e,t,n),s=(Ao(e,o,g),r[o]=null,h.set(o,null),c.set(o,0),[]),l=[],u=0;O(e,function(t,e){var n=t.existing,i=t.newOption;if(i){var r=g.getClass(o,t.keyInfo.subType,!("series"===o));if(!r)return;if("tooltip"===o){if(a)return;a=!0}n&&n.constructor===r?(n.name=t.keyInfo.name,n.mergeOption(i,this),n.optionUpdated(i,!1)):(e=L({componentIndex:e},t.keyInfo),L(n=new r(i,this,this,e),e),t.brandNew&&(n.__requireNewView=!0),n.init(i,this,this),n.optionUpdated(null,!0))}else n&&(n.mergeOption({},this),n.optionUpdated({},!1));n?(s.push(n.option),l.push(n),u++):(s.push(void 0),l.push(void 0))},this),r[o]=s,h.set(o,l),c.set(o,u),"series"===o&&sd(this)},this),this._seriesIndices||sd(this)},s.prototype.getOption=function(){var a=y(this.option);return O(a,function(t,e){if(g.hasClass(e)){for(var n=xo(t),i=n.length,r=!1,o=i-1;0<=o;o--)n[o]&&!Do(n[o])?r=!0:(n[o]=null,r||i--);n.length=i,a[e]=n}}),delete a[gd],a},s.prototype.getTheme=function(){return this._theme},s.prototype.getLocaleModel=function(){return this._locale},s.prototype.setUpdatePayload=function(t){this._payload=t},s.prototype.getUpdatePayload=function(){return this._payload},s.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){t=n[e||0];if(t)return t;if(null==e)for(var i=0;i<n.length;i++)if(n[i])return n[i]}},s.prototype.queryComponents=function(t){var e,n,i,r,o,a=t.mainType;return a&&(e=t.index,n=t.id,i=t.name,r=this._componentsMap.get(a))&&r.length?(null!=e?(o=[],O(xo(e),function(t){r[t]&&o.push(r[t])})):o=null!=n?md("id",n,r):null!=i?md("name",i,r):ut(r,function(t){return!!t}),vd(o,t)):[]},s.prototype.findComponents=function(t){var e,n=t.query,i=t.mainType,r=(r=i+"Index",o=i+"Id",e=i+"Name",!(n=n)||null==n[r]&&null==n[o]&&null==n[e]?null:{mainType:i,index:n[r],id:n[o],name:n[e]}),o=r?this.queryComponents(r):ut(this._componentsMap.get(i),function(t){return!!t});return n=vd(o,t),t.filter?ut(n,t.filter):n},s.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(k(t)){var r=e,o=t;i.each(function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}})}else for(var a=V(t)?i.get(t):R(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},s.prototype.getSeriesByName=function(t){var e=Io(t,null);return ut(this._componentsMap.get("series"),function(t){return!!t&&null!=e&&t.name===e})},s.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},s.prototype.getSeriesByType=function(e){return ut(this._componentsMap.get("series"),function(t){return!!t&&t.subType===e})},s.prototype.getSeries=function(){return ut(this._componentsMap.get("series"),function(t){return!!t})},s.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},s.prototype.eachSeries=function(n,i){ld(this),O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)},this)},s.prototype.eachRawSeries=function(e,n){O(this._componentsMap.get("series"),function(t){t&&e.call(n,t,t.componentIndex)})},s.prototype.eachSeriesByType=function(n,i,r){ld(this),O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];e.subType===n&&i.call(r,e,t)},this)},s.prototype.eachRawSeriesByType=function(t,e,n){return O(this.getSeriesByType(t),e,n)},s.prototype.isSeriesFiltered=function(t){return ld(this),null==this._seriesIndicesMap.get(t.componentIndex)},s.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},s.prototype.filterSeries=function(n,i){ld(this);var r=[];O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)&&r.push(t)},this),this._seriesIndices=r,this._seriesIndicesMap=E(r)},s.prototype.restoreData=function(n){sd(this);var t=this._componentsMap,i=[];t.each(function(t,e){g.hasClass(e)&&i.push(e)}),g.topologicalTravel(i,g.getAllClassMainTypes(),function(e){O(t.get(e),function(t){!t||"series"===e&&function(t,e){{var n,i;if(e)return n=e.seriesIndex,i=e.seriesId,e=e.seriesName,null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=e&&t.name!==e}}(t,n)||t.restoreData()})})},s.internalField=(sd=function(t){var e=t._seriesIndices=[];O(t._componentsMap.get("series"),function(t){t&&e.push(t.componentIndex)}),t._seriesIndicesMap=E(e)},ld=function(t){},void(ud=function(t,e){t.option={},t.option[gd]=1,t._componentsMap=E({series:[]}),t._componentsCount=E();var n,i,r=e.aria;R(r)&&null==r.enabled&&(r.enabled=!0),n=e,r=t._theme.option,i=n.color&&!n.colorLayer,O(r,function(t,e){"colorLayer"===e&&i||g.hasClass(e)||("object"==typeof t?n[e]=n[e]?d(n[e],t,!1):y(t):null==n[e]&&(n[e]=t))}),d(e,Xp,!1),t._mergeOption(e,null)})),s);function s(){return null!==fd&&fd.apply(this,arguments)||this}function md(e,t,n){var i,r;return F(t)?(i=E(),O(t,function(t){null!=t&&null!=Io(t,null)&&i.set(t,!0)}),ut(n,function(t){return t&&i.get(t[e])})):(r=Io(t,null),ut(n,function(t){return t&&null!=r&&t[e]===r}))}function vd(t,e){return e.hasOwnProperty("subType")?ut(t,function(t){return t&&t.subType===e.subType}):t}function _d(t){var e=E();return t&&O(xo(t.replaceMerge),function(t){e.set(t,!0)}),{replaceMergeMainTypeMap:e}}at(yd,cd);function xd(e){O(wd,function(t){this[t]=ct(e[t],e)},this)}var wd=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],bd={},Sd=(Md.prototype.create=function(n,i){var r=[];O(bd,function(t,e){t=t.create(n,i);r=r.concat(t||[])}),this._coordinateSystems=r},Md.prototype.update=function(e,n){O(this._coordinateSystems,function(t){t.update&&t.update(e,n)})},Md.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},Md.register=function(t,e){bd[t]=e},Md.get=function(t){return bd[t]},Md);function Md(){this._coordinateSystems=[]}var Td=/^(min|max)?(.+)$/,Cd=(Id.prototype.setOption=function(t,e,n){t&&(O(xo(t.series),function(t){t&&t.data&&gt(t.data)&&kt(t.data)}),O(xo(t.dataset),function(t){t&&t.source&&gt(t.source)&&kt(t.source)})),t=y(t);var i=this._optionBackup,t=function(t,n,i){var e,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t);h&&F(u)&&O(u,function(t){t&&t.option&&(t.query?o.push(t):e=e||t)});function p(e){O(n,function(t){t(e,i)})}return p(r),O(l,p),O(o,function(t){return p(t.option)}),{baseOption:r,timelineOptions:l||[],mediaDefault:e,mediaList:o}}(t,e,!i);this._newBaseOption=t.baseOption,i?(t.timelineOptions.length&&(i.timelineOptions=t.timelineOptions),t.mediaList.length&&(i.mediaList=t.mediaList),t.mediaDefault&&(i.mediaDefault=t.mediaDefault)):this._optionBackup=t},Id.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],y(t?e.baseOption:this._newBaseOption)},Id.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;return e=n.length&&(t=t.getComponent("timeline"))?y(n[t.getCurrentIndex()]):e},Id.prototype.getMediaOption=function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,o=[],a=[];if(i.length||r){for(var s,l,u=0,h=i.length;u<h;u++)!function(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return O(t,function(t,e){var n,e=e.match(Td);e&&e[1]&&e[2]&&(n=e[1],e=e[2].toLowerCase(),e=i[e],t=t,("min"===(n=n)?t<=e:"max"===n?e<=t:e===t)||(r=!1))}),r}(i[u].query,e,n)||o.push(u);(o=!o.length&&r?[-1]:o).length&&(s=o,l=this._currentMediaIndices,s.join(",")!==l.join(","))&&(a=B(o,function(t){return y((-1===t?r:i[t]).option)})),this._currentMediaIndices=o}return a},Id);function Id(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}var kd=O,Dd=R,Ad=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Pd(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=Ad.length;n<i;n++){var r=Ad[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?d(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?d(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function Ld(t,e,n){var i,r;t&&t[e]&&(t[e].normal||t[e].emphasis)&&(i=t[e].normal,r=t[e].emphasis,i&&(n?(t[e].normal=t[e].emphasis=null,z(t[e],i)):t[e]=i),r)&&(t.emphasis=t.emphasis||{},(t.emphasis[e]=r).focus&&(t.emphasis.focus=r.focus),r.blurScope)&&(t.emphasis.blurScope=r.blurScope)}function Od(t){Ld(t,"itemStyle"),Ld(t,"lineStyle"),Ld(t,"areaStyle"),Ld(t,"label"),Ld(t,"labelLine"),Ld(t,"upperLabel"),Ld(t,"edgeLabel")}function Rd(t,e){var n=Dd(t)&&t[e],i=Dd(n)&&n.textStyle;if(i)for(var r=0,o=bo.length;r<o;r++){var a=bo[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}function Nd(t){t&&(Od(t),Rd(t,"label"),t.emphasis)&&Rd(t.emphasis,"label")}function Ed(t){return F(t)?t:t?[t]:[]}function zd(t){return(F(t)?t[0]:t)||{}}function Bd(e,t){kd(Ed(e.series),function(t){if(Dd(t))if(Dd(t)){Pd(t),Od(t),Rd(t,"label"),Rd(t,"upperLabel"),Rd(t,"edgeLabel"),t.emphasis&&(Rd(t.emphasis,"label"),Rd(t.emphasis,"upperLabel"),Rd(t.emphasis,"edgeLabel"));var e=t.markPoint,n=(e&&(Pd(e),Nd(e)),t.markLine),i=(n&&(Pd(n),Nd(n)),t.markArea),r=(i&&Nd(i),t.data);if("graph"===t.type){var r=r||t.nodes,o=t.links||t.edges;if(o&&!gt(o))for(var a=0;a<o.length;a++)Nd(o[a]);O(t.categories,function(t){Od(t)})}if(r&&!gt(r))for(a=0;a<r.length;a++)Nd(r[a]);if((e=t.markPoint)&&e.data)for(var s=e.data,a=0;a<s.length;a++)Nd(s[a]);if((n=t.markLine)&&n.data)for(var l=n.data,a=0;a<l.length;a++)F(l[a])?(Nd(l[a][0]),Nd(l[a][1])):Nd(l[a]);"gauge"===t.type?(Rd(t,"axisLabel"),Rd(t,"title"),Rd(t,"detail")):"treemap"===t.type?(Ld(t.breadcrumb,"itemStyle"),O(t.levels,function(t){Od(t)})):"tree"===t.type&&Od(t.leaves)}});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),kd(n,function(t){kd(Ed(e[t]),function(t){t&&(Rd(t,"axisLabel"),Rd(t.axisPointer,"label"))})}),kd(Ed(e.parallel),function(t){t=t&&t.parallelAxisDefault;Rd(t,"axisLabel"),Rd(t&&t.axisPointer,"label")}),kd(Ed(e.calendar),function(t){Ld(t,"itemStyle"),Rd(t,"dayLabel"),Rd(t,"monthLabel"),Rd(t,"yearLabel")}),kd(Ed(e.radar),function(t){Rd(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)}),kd(Ed(e.geo),function(t){Dd(t)&&(Nd(t),kd(Ed(t.regions),function(t){Nd(t)}))}),kd(Ed(e.timeline),function(t){Nd(t),Ld(t,"label"),Ld(t,"itemStyle"),Ld(t,"controlStyle",!0);t=t.data;F(t)&&O(t,function(t){R(t)&&(Ld(t,"label"),Ld(t,"itemStyle"))})}),kd(Ed(e.toolbox),function(t){Ld(t,"iconStyle"),kd(t.feature,function(t){Ld(t,"iconStyle")})}),Rd(zd(e.axisPointer),"label"),Rd(zd(e.tooltip).axisPointer,"label")}function Fd(e){e&&O(Vd,function(t){t[0]in e&&!(t[1]in e)&&(e[t[1]]=e[t[0]])})}var Vd=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Hd=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Gd=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Wd(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<Gd.length;n++){var i=Gd[n][1],r=Gd[n][0];null!=e[i]&&(e[r]=e[i])}}function Ud(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function Xd(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function Yd(e,t){Bd(e,t),e.series=xo(e.series),O(e.series,function(t){if(R(t)){var e,n=t.type;if("line"===n)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===n||"gauge"===n){if(null!=t.clockWise&&(t.clockwise=t.clockWise),Ud(t.label),(e=t.data)&&!gt(e))for(var i=0;i<e.length;i++)Ud(e[i]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset)}else if("gauge"===n){var r=function(t,e){for(var n=e.split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t,"pointer.color");if(null!=r){var o=t;var a="itemStyle.color";var s=void 0;for(var l,u=a.split(","),h=o,c=0;c<u.length-1;c++)null==h[l=u[c]]&&(h[l]={}),h=h[l];!s&&null!=h[u[c]]||(h[u[c]]=r)}}else if("bar"===n){if(Wd(t),Wd(t.backgroundStyle),Wd(t.emphasis),(e=t.data)&&!gt(e))for(i=0;i<e.length;i++)"object"==typeof e[i]&&(Wd(e[i]),Wd(e[i]&&e[i].emphasis))}else"sunburst"===n?((a=t.highlightPolicy)&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=a)),Xd(t),function t(e,n){if(e)for(var i=0;i<e.length;i++)n(e[i]),e[i]&&t(e[i].children,n)}(t.data,Xd)):"graph"===n||"sankey"===n?(o=t)&&null!=o.focusNodeAdjacency&&(o.emphasis=o.emphasis||{},null==o.emphasis.focus)&&(o.emphasis.focus="adjacency"):"map"===n&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation)&&z(t,t.mapLocation);null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis)&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation),Fd(t)}}),e.dataRange&&(e.visualMap=e.dataRange),O(Hd,function(t){t=e[t];t&&O(t=F(t)?t:[t],function(t){Fd(t)})})}function qd(_){O(_,function(p,d){var f=[],g=[NaN,NaN],t=[p.stackResultDimension,p.stackedOverDimension],y=p.data,m=p.isStackedByIndex,v=p.seriesModel.get("stackStrategy")||"samesign";y.modify(t,function(t,e,n){var i,r,o=y.get(p.stackedDimension,n);if(isNaN(o))return g;m?r=y.getRawIndex(n):i=y.get(p.stackedByDimension,n);for(var a,s,l,u=NaN,h=d-1;0<=h;h--){var c=_[h];if(0<=(r=m?r:c.data.rawIndexOf(c.stackedByDimension,i))){c=c.data.getByRawIndex(c.stackResultDimension,r);if("all"===v||"positive"===v&&0<c||"negative"===v&&c<0||"samesign"===v&&0<=o&&0<c||"samesign"===v&&o<=0&&c<0){a=o,s=c,l=void 0,l=Math.max(io(a),io(s)),a+=s,o=Jr<l?a:no(a,l),u=c;break}}}return f[0]=o,f[1]=u,f})})}var Zd,jd,Kd=function(t){this.data=t.data||(t.sourceFormat===Kp?{}:[]),this.sourceFormat=t.sourceFormat||Qp,this.seriesLayoutBy=t.seriesLayoutBy||Jp,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&od(this,n)===ed.Must&&(i.type="ordinal")}};function $d(t){return t instanceof Kd}function Qd(t,e,n){n=n||tf(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a;if(!t)return{dimensionsDefine:ef(r),startIndex:a,dimensionsDetectedCount:o};{var s;e===Zp?(s=t,"auto"===i||null==i?nf(function(t){null!=t&&"-"!==t&&(V(t)?null==a&&(a=1):a=0)},n,s,10):a=H(i)?i:i?1:0,r||1!==a||(r=[],nf(function(t,e){r[e]=null!=t?t+"":""},n,s,1/0)),o=r?r.length:n===td?s.length:s[0]?s[0].length:null):e===jp?r=r||function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e)return ht(e)}(t):e===Kp?r||(r=[],O(t,function(t,e){r.push(e)})):e===qp&&(i=So(t[0]),o=F(i)&&i.length||1)}return{startIndex:a,dimensionsDefine:ef(r),dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new Kd({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:y(e)})}function Jd(t){return new Kd({data:t,sourceFormat:gt(t)?$p:qp})}function tf(t){var e=Qp;if(gt(t))e=$p;else if(F(t)){0===t.length&&(e=Zp);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(F(r)||gt(r)){e=Zp;break}if(R(r)){e=jp;break}}}}else if(R(t))for(var o in t)if(Bt(t,o)&&st(t[o])){e=Kp;break}return e}function ef(t){var i;if(t)return i=E(),B(t,function(t,e){var n,t={name:(t=R(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};return null!=t.name&&(t.name+="",null==t.displayName&&(t.displayName=t.name),(n=i.get(t.name))?t.name+="-"+n.count++:i.set(t.name,{count:1})),t})}function nf(t,e,n,i){if(e===td)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else for(var o=n[0]||[],r=0;r<o.length&&r<i;r++)t(o[r],r)}function rf(t){t=t.sourceFormat;return t===jp||t===Kp}uf.prototype.getSource=function(){return this._source},uf.prototype.count=function(){return 0},uf.prototype.getItem=function(t,e){},uf.prototype.appendData=function(t){},uf.prototype.clean=function(){},uf.protoInitialize=((jh=uf.prototype).pure=!1,void(jh.persistent=!0)),uf.internalField=(jd=function(t,e,n){var i,r=n.sourceFormat,o=n.seriesLayoutBy,a=n.startIndex,n=n.dimensionsDefine;L(t,Zd[xf(r,o)]),r===$p?(t.getItem=of,t.count=sf,t.fillStorage=af):(i=df(r,o),t.getItem=ct(i,null,e,a,n),i=yf(r,o),t.count=ct(i,null,e,a,n))},of=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},af=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var d=r[p*o+a];(c[t+p]=d)<l&&(l=d),u<d&&(u=d)}s[0]=l,s[1]=u}},sf=function(){return this._data?this._data.length/this._dimSize:0},(jh={})[Zp+"_"+Jp]={pure:!0,appendData:hf},jh[Zp+"_"+td]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},jh[jp]={pure:!0,appendData:hf},jh[Kp]={pure:!0,appendData:function(t){var r=this._data;O(t,function(t,e){for(var n=r[e]||(r[e]=[]),i=0;i<(t||[]).length;i++)n.push(t[i])})}},jh[qp]={appendData:hf},jh[$p]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},void(Zd=jh));var of,af,sf,lf=uf;function uf(t,e){var t=$d(t)?t:Jd(t),n=(this._source=t,this._data=t.data);t.sourceFormat===$p&&(this._offset=0,this._dimSize=e,this._data=n),jd(this,n,t)}function hf(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function cf(t,e,n,i){return t[i]}($c={})[Zp+"_"+Jp]=function(t,e,n,i){return t[i+e]},$c[Zp+"_"+td]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},$c[jp]=cf,$c[Kp]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=t[n[a].name];o[a]=s?s[i]:null}return o},$c[qp]=cf;var pf=$c;function df(t,e){return pf[xf(t,e)]}function ff(t,e,n){return t.length}(Ic={})[Zp+"_"+Jp]=function(t,e,n){return Math.max(0,t.length-e)},Ic[Zp+"_"+td]=function(t,e,n){t=t[0];return t?Math.max(0,t.length-e):0},Ic[jp]=ff,Ic[Kp]=function(t,e,n){t=t[n[0].name];return t?t.length:0},Ic[qp]=ff;var gf=Ic;function yf(t,e){return gf[xf(t,e)]}function mf(t,e,n){return t[e]}(Qo={})[Zp]=mf,Qo[jp]=function(t,e,n){return t[n]},Qo[Kp]=mf,Qo[qp]=function(t,e,n){t=So(t);return t instanceof Array?t[e]:t},Qo[$p]=mf;var vf=Qo;function _f(t){return vf[t]}function xf(t,e){return t===Zp?t+"_"+e:t}function wf(t,e,n){if(t){var i,r,e=t.getRawDataItem(e);if(null!=e)return i=(r=t.getStore()).getSource().sourceFormat,null!=n?(t=t.getDimensionIndex(n),n=r.getDimensionProperty(t),_f(i)(e,t,n)):(r=e,i===qp?So(e):r)}}var bf=/\{@(.+?)\}/g,Pc=(Sf.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),t=s&&s[n.getItemVisual(t,"drawType")||"fill"],s=s&&s.stroke,l=this.mainType,u="series"===l,n=n.userOutput&&n.userOutput.get();return{componentType:l,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:u?this.subType:null,seriesIndex:this.seriesIndex,seriesId:u?this.id:null,seriesName:u?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:t,borderColor:s,dimensionNames:n?n.fullDimensions:null,encode:n?n.encode:null,$vars:["seriesName","name","value"]}},Sf.prototype.getFormattedLabel=function(i,t,e,n,r,o){t=t||"normal";var a=this.getData(e),e=this.getDataParams(i,e);return o&&(e.value=o.interpolatedValue),null!=n&&F(e.value)&&(e.value=e.value[n]),k(r=r||a.getItemModel(i).get("normal"===t?["label","formatter"]:[t,"label","formatter"]))?(e.status=t,e.dimensionIndex=n,r(e)):V(r)?Tp(r,e).replace(bf,function(t,e){var n=e.length,n=("["===e.charAt(0)&&"]"===e.charAt(n-1)&&(e=+e.slice(1,n-1)),wf(a,i,e));return null!=(n=o&&F(o.interpolatedValue)&&0<=(e=a.getDimensionIndex(e))?o.interpolatedValue[e]:n)?n+"":""}):void 0},Sf.prototype.getRawValue=function(t,e){return wf(this.getData(e),t)},Sf.prototype.formatTooltip=function(t,e,n){},Sf);function Sf(){}function Mf(t){var e,n;return R(t)?t.type&&(n=t):e=t,{text:e,frag:n}}function Tf(t){return new Cf(t)}If.prototype.perform=function(t){var e,n,i=this._upstream,r=t&&t.skip,o=(this._dirty&&i&&((o=this.context).data=o.outputData=i.context.outputData),this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!r&&(e=this._plan(this.context)),u(this._modBy)),a=this._modDataCount||0,s=u(t&&t.modBy),l=t&&t.modDataCount||0;function u(t){return t=1<=t?t:1}o===s&&a===l||(e="reset"),!this._dirty&&"reset"!==e||(this._dirty=!1,n=this._doReset(r)),this._modBy=s,this._modDataCount=l;o=t&&t.step;if(this._dueEnd=i?i._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var h=this._dueIndex,c=Math.min(null!=o?this._dueIndex+o:1/0,this._dueEnd);if(!r&&(n||h<c)){var p=this._progress;if(F(p))for(var d=0;d<p.length;d++)this._doProgress(p[d],h,c,s,l);else this._doProgress(p,h,c,s,l)}this._dueIndex=c;a=null!=this._settedOutputEnd?this._settedOutputEnd:c;this._outputDueEnd=a}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},If.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},If.prototype._doProgress=function(t,e,n,i,r){Rf.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:Rf.next},this.context)},If.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),F(e))&&!e.length&&(e=null),this._progress=e,this._modBy=this._modDataCount=null;var e,n,t=this._downstream;return t&&t.dirty(),n},If.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},If.prototype.pipe=function(t){this._downstream===t&&!this._dirty||((this._downstream=t)._upstream=this,t.dirty())},If.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},If.prototype.getUpstream=function(){return this._upstream},If.prototype.getDownstream=function(){return this._downstream},If.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var Cf=If;function If(t){this._reset=(t=t||{}).reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}var kf,Df,Af,Pf,Lf,Of,Rf=Of={reset:function(t,e,n,i){Df=t,kf=e,Af=n,Pf=i,Lf=Math.ceil(Pf/Af),Of.next=1<Af&&0<Pf?Ef:Nf}};function Nf(){return Df<kf?Df++:null}function Ef(){var t=Df%Lf*Af+Math.ceil(Df/Lf),t=kf<=Df?null:t<Pf?t:Df;return Df++,t}function zf(t,e){e=e&&e.type;return"ordinal"===e?t:null==(t="time"!==e||H(t)||null==t||"-"===t?t:+uo(t))||""===t?NaN:Number(t)}var Bf=E({number:function(t){return parseFloat(t)},time:function(t){return+uo(t)},trim:function(t){return V(t)?Ct(t):t}});function Ff(t){return Bf.get(t)}var Vf={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return e<t},gte:function(t,e){return e<=t}},Hf=(Gf.prototype.evaluate=function(t){return H(t)?this._opFn(t,this._rvalFloat):this._opFn(fo(t),this._rvalFloat)},Gf);function Gf(t,e){H(e)||f(""),this._opFn=Vf[t],this._rvalFloat=fo(e)}Uf.prototype.evaluate=function(t,e){var n=H(t)?t:fo(t),i=H(e)?e:fo(e),r=isNaN(n),o=isNaN(i);return r&&(n=this._incomparable),o&&(i=this._incomparable),r&&o&&(r=V(t),o=V(e),r&&(n=o?t:0),o)&&(i=r?e:0),n<i?this._resultLT:i<n?-this._resultLT:0};var Wf=Uf;function Uf(t,e){t="desc"===t;this._resultLT=t?1:-1,this._incomparable="min"===(e=null==e?t?"min":"max":e)?-1/0:1/0}Yf.prototype.evaluate=function(t){var e,n=t===this._rval;return n||(e=typeof t)===this._rvalTypeof||"number"!=e&&"number"!==this._rvalTypeof||(n=fo(t)===this._rvalFloat),this._isEQ?n:!n};var Xf=Yf;function Yf(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=fo(e)}Zf.prototype.getRawData=function(){throw new Error("not supported")},Zf.prototype.getRawDataItem=function(t){throw new Error("not supported")},Zf.prototype.cloneRawData=function(){},Zf.prototype.getDimensionInfo=function(t){},Zf.prototype.cloneAllDimensionInfo=function(){},Zf.prototype.count=function(){},Zf.prototype.retrieveValue=function(t,e){},Zf.prototype.retrieveValueFromItem=function(t,e){},Zf.prototype.convertValue=zf;var qf=Zf;function Zf(){}function jf(t){return eg(t.sourceFormat)||f(""),t.data}function Kf(t){var e=t.sourceFormat,n=t.data;if(eg(e)||f(""),e===Zp){for(var i=[],r=0,o=n.length;r<o;r++)i.push(n[r].slice());return i}if(e===jp){for(i=[],r=0,o=n.length;r<o;r++)i.push(L({},n[r]));return i}}function $f(t,e,n){if(null!=n)return H(n)||!isNaN(n)&&!Bt(e,n)?t[n]:Bt(e,n)?e[n]:void 0}function Qf(t){return y(t)}var Jf=E();function tg(t,e){var n=xo(t),t=n.length;t||f("");for(var i=0,r=t;i<r;i++)e=function(t,i){i.length||f("");R(t)||f("");var e=t.type,d=Jf.get(e);d||f("");e=B(i,function(t){var e=t,t=d,n=new qf,i=e.data,r=n.sourceFormat=e.sourceFormat,o=e.startIndex,a=(e.seriesLayoutBy!==Jp&&f(""),[]),s={};if(h=e.dimensionsDefine)O(h,function(t,e){var n=t.name,e={index:e,name:n,displayName:t.displayName};a.push(e),null!=n&&(Bt(s,n)&&f(""),s[n]=e)});else for(var l=0;l<e.dimensionsDetectedCount;l++)a.push({index:l});var u=df(r,Jp),h=(t.__isBuiltIn&&(n.getRawDataItem=function(t){return u(i,o,a,t)},n.getRawData=ct(jf,null,e)),n.cloneRawData=ct(Kf,null,e),yf(r,Jp)),c=(n.count=ct(h,null,i,o,a),_f(r)),p=(n.retrieveValue=function(t,e){t=u(i,o,a,t);return p(t,e)},n.retrieveValueFromItem=function(t,e){var n;return null!=t&&(n=a[e])?c(t,e,n.name):void 0});return n.getDimensionInfo=ct($f,null,a,s),n.cloneAllDimensionInfo=ct(Qf,null,a),n});return B(xo(d.transform({upstream:e[0],upstreamList:e,config:y(t.config)})),function(t,e){R(t)||f(""),t.data||f("");eg(tf(t.data))||f("");var n=i[0],e=n&&0===e&&!t.dimensions?((e=n.startIndex)&&(t.data=n.data.slice(0,e).concat(t.data)),{seriesLayoutBy:Jp,sourceHeader:e,dimensions:n.metaRawOption.dimensions}):{seriesLayoutBy:Jp,sourceHeader:0,dimensions:t.dimensions};return Qd(t.data,e,null)})}(n[i],e),i!==r-1&&(e.length=Math.max(e.length,1));return e}function eg(t){return t===Zp||t===jp}var ng,jh="undefined",ig=typeof Uint32Array==jh?Array:Uint32Array,rg=typeof Uint16Array==jh?Array:Uint16Array,og=typeof Int32Array==jh?Array:Int32Array,$c=typeof Float64Array==jh?Array:Float64Array,ag={float:$c,int:og,ordinal:Array,number:Array,time:$c};function sg(t){return 65535<t?ig:rg}function lg(){return[1/0,-1/0]}function ug(t,e,n,i,r){n=ag[n||"float"];if(r){var o=t[e],a=o&&o.length;if(a!==i){for(var s=new n(i),l=0;l<a;l++)s[l]=o[l];t[e]=s}}else t[e]=new n(i)}l.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=ng[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[],rf(i),this._dimensions=B(e,function(t){return{type:t.type,property:t.property}}),this._initDataFromProvider(0,t.count())},l.prototype.getProvider=function(){return this._provider},l.prototype.getSource=function(){return this._provider.getSource()},l.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new ag[e||"float"](this._rawCount),this._rawExtent[r]=lg(),r},l.prototype.collectOrdinalMeta=function(t,e){for(var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length,s=(0===o&&(r[t]=lg()),r[t]),l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);isNaN(u)||(s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1]))}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},l.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},l.prototype.getDimensionProperty=function(t){t=this._dimensions[t];return t&&t.property},l.prototype.appendData=function(t){var e=this._provider,n=this.count(),t=(e.appendData(t),e.count());return e.persistent||(t+=n),n<t&&this._initDataFromProvider(n,t,!0),[n,t]},l.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++)ug(n,l,(d=i[l]).type,s,!0);for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var d=i[p],f=ng.arrayRows.call(this,t[c]||u,d.property,c,p),g=(n[p][h]=f,o[p]);f<g[0]&&(g[0]=f),f>g[1]&&(g[1]=f)}return{start:a,end:this._rawCount=this._count=s}},l.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=B(o,function(t){return t.property}),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=lg()),ug(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++)for(var c=i.getItem(p,c),d=0;d<a;d++){var f=r[d],g=this._dimValueGetter(c,l[d],p,d),f=(f[p]=g,s[d]);g<f[0]&&(f[0]=g),g>f[1]&&(f[1]=g)}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},l.prototype.count=function(){return this._count},l.prototype.get=function(t,e){return 0<=e&&e<this._count&&(t=this._chunks[t])?t[this.getRawIndex(e)]:NaN},l.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;for(var r=0,o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},l.prototype.getByRawIndex=function(t,e){return 0<=e&&e<this._rawCount&&(t=this._chunks[t])?t[e]:NaN},l.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},l.prototype.getMedian=function(t){var e=[],t=(this.each([t],function(t){isNaN(t)||e.push(t)}),e.sort(function(t,e){return t-e})),n=this.count();return 0===n?0:n%2==1?t[(n-1)/2]:(t[n/2]+t[n/2-1])/2},l.prototype.indexOfRawIndex=function(t){if(!(t>=this._rawCount||t<0)){if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=1+o;else{if(!(e[o]>t))return o;r=o-1}}}return-1},l.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks[t],r=[];if(i){null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,l=0,u=this.count();l<u;l++){var h=e-i[this.getRawIndex(l)],c=Math.abs(h);c<=n&&((c<o||c===o&&0<=h&&a<0)&&(o=c,a=h,s=0),h===a)&&(r[s++]=l)}r.length=s}return r},l.prototype.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array)for(var i=new e(n),r=0;r<n;r++)i[r]=t[r];else i=new e(t.buffer,0,n)}else{i=new(e=sg(this._rawCount))(this.count());for(r=0;r<i.length;r++)i[r]=r}return i},l.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(sg(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a)c=e(u[l][p],h);else{for(var d=0;d<a;d++)o[d]=u[t[d]][p];o[d]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},l.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=ht(t),r=i.length;if(!r)return this;var o=e.count(),a=new(sg(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks,l=!1;if(!e._indices){var p=0;if(1===r){for(var d=c[i[0]],f=0;f<n;f++)(u<=(v=d[f])&&v<=h||isNaN(v))&&(a[s++]=p),p++;l=!0}else if(2===r){for(var d=c[i[0]],g=c[i[1]],y=t[i[1]][0],m=t[i[1]][1],f=0;f<n;f++){var v=d[f],_=g[f];(u<=v&&v<=h||isNaN(v))&&(y<=_&&_<=m||isNaN(_))&&(a[s++]=p),p++}l=!0}}if(!l)if(1===r)for(f=0;f<o;f++){var x=e.getRawIndex(f);(u<=(v=c[i[0]][x])&&v<=h||isNaN(v))&&(a[s++]=x)}else for(f=0;f<o;f++){for(var w=!0,x=e.getRawIndex(f),b=0;b<r;b++){var S=i[b];((v=c[S][x])<t[S][0]||v>t[S][1])&&(w=!1)}w&&(a[s++]=e.getRawIndex(f))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},l.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},l.prototype.modify=function(t,e){this._updateDims(this,t,e)},l.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],l=t._rawExtent,u=0;u<e.length;u++)l[e[u]]=lg();for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),p=0;p<o;p++)s[p]=i[e[p]][c];s[o]=h;var d=n&&n.apply(null,s);if(null!=d){"object"!=typeof d&&(r[0]=d,d=r);for(u=0;u<d.length;u++){var f=e[u],g=d[u],y=l[f],f=i[f];f&&(f[c]=g),g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}}},l.prototype.lttbDownSample=function(t,e){var n,i=this.clone([t],!0),r=i._chunks[t],o=this.count(),a=0,s=Math.floor(1/e),l=this.getRawIndex(0),u=new(sg(this._rawCount))(Math.min(2*(Math.ceil(o/s)+2),o));u[a++]=l;for(var h=1;h<o-1;h+=s){for(var c=Math.min(h+s,o-1),p=Math.min(h+2*s,o),d=(p+c)/2,f=0,g=c;g<p;g++){var y=r[M=this.getRawIndex(g)];isNaN(y)||(f+=y)}f/=p-c;for(var c=h,m=Math.min(h+s,o),v=h-1,_=r[l],x=-1,w=c,b=-1,S=0,g=c;g<m;g++){var M,y=r[M=this.getRawIndex(g)];isNaN(y)?(S++,b<0&&(b=M)):x<(n=Math.abs((v-d)*(y-_)-(v-g)*(f-_)))&&(x=n,w=M)}0<S&&S<m-c&&(u[a++]=Math.min(b,w),w=Math.max(b,w)),l=u[a++]=w}return u[a++]=this.getRawIndex(o-1),i._count=a,i._indices=u,i.getRawIndex=this._getRawIdx,i},l.prototype.minmaxDownSample=function(t,e){for(var n=this.clone([t],!0),i=n._chunks,r=Math.floor(1/e),o=i[t],a=this.count(),s=new(sg(this._rawCount))(2*Math.ceil(a/r)),l=0,u=0;u<a;u+=r){var h=u,c=o[this.getRawIndex(h)],p=u,d=o[this.getRawIndex(p)],f=r;a<u+r&&(f=a-u);for(var g=0;g<f;g++){var y=o[this.getRawIndex(u+g)];y<c&&(c=y,h=u+g),d<y&&(d=y,p=u+g)}var m=this.getRawIndex(h),v=this.getRawIndex(p);h<p?(s[l++]=m,s[l++]=v):(s[l++]=v,s[l++]=m)}return n._count=l,n._indices=s,n._updateGetRawIdx(),n},l.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=lg(),c=new(sg(this._rawCount))(Math.ceil(u/s)),p=0,d=0;d<u;d+=s){u-d<s&&(a.length=s=u-d);for(var f=0;f<s;f++){var g=this.getRawIndex(d+f);a[f]=l[g]}var y=n(a),m=this.getRawIndex(Math.min(d+i(a,y)||0,u-1));(l[m]=y)<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[p++]=m}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},l.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},l.prototype.getDataExtent=function(t){var e=this._chunks[t],n=lg();if(!e)return n;var i=this.count();if(!this._indices)return this._rawExtent[t].slice();if(r=this._extent[t])return r.slice();for(var r,o=(r=n)[0],a=r[1],s=0;s<i;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),a<l&&(a=l)}return this._extent[t]=r=[o,a]},l.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},l.prototype.clone=function(t,e){var n,i,r=new l,o=this._chunks,a=t&&lt(t,function(t,e){return t[e]=!0,t},{});if(a)for(var s=0;s<o.length;s++)r._chunks[s]=a[s]?(n=o[s],i=void 0,(i=n.constructor)===Array?n.slice():new i(n)):o[s];else r._chunks=o;return this._copyCommonProps(r),e||(r._indices=this._cloneIndices()),r._updateGetRawIdx(),r},l.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=y(this._extent),t._rawExtent=y(this._rawExtent)},l.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array)for(var n=this._indices.length,e=new t(n),i=0;i<n;i++)e[i]=this._indices[i];else e=new t(this._indices);return e}return null},l.prototype._getRawIdxIdentity=function(t){return t},l.prototype._getRawIdx=function(t){return t<this._count&&0<=t?this._indices[t]:-1},l.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},l.internalField=void(ng={arrayRows:cg,objectRows:function(t,e,n,i){return zf(t[e],this._dimensions[i])},keyedColumns:cg,original:function(t,e,n,i){t=t&&(null==t.value?t:t.value);return zf(t instanceof Array?t[i]:t,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}});var hg=l;function l(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=E()}function cg(t,e,n,i){return zf(t[i],this._dimensions[i])}dg.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},dg.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,9e10<this._versionSignBase&&(this._versionSignBase=0)},dg.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},dg.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},dg.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n,i,r,o,a,s=this._sourceHost,l=this._getUpstreamSourceManagers(),u=!!l.length;gg(s)?(i=s,r=t=o=void 0,e=u?((e=l[0]).prepareSource(),o=(r=e.getSource()).data,t=r.sourceFormat,[e._getVersionSign()]):(t=gt(o=i.get("data",!0))?$p:qp,[]),i=this._getSourceMetaRawOption()||{},r=r&&r.metaRawOption||{},a=N(i.seriesLayoutBy,r.seriesLayoutBy)||null,n=N(i.sourceHeader,r.sourceHeader),i=N(i.dimensions,r.dimensions),r=a!==r.seriesLayoutBy||!!n!=!!r.sourceHeader||i?[Qd(o,{seriesLayoutBy:a,sourceHeader:n,dimensions:i},t)]:[]):(o=s,e=u?(r=(a=this._applyTransform(l)).sourceList,a.upstreamSignList):(r=[Qd(o.get("source",!0),this._getSourceMetaRawOption(),null)],[])),this._setLocalSource(r,e)},dg.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0),o=(null!=r&&1!==t.length&&yg(""),[]),a=[];return O(t,function(t){t.prepareSource();var e=t.getSource(r||0);null==r||e||yg(""),o.push(e),a.push(t._getVersionSign())}),i?e=tg(i,o,n.componentIndex):null!=r&&(e=[new Kd({data:(t=o[0]).data,sourceFormat:t.sourceFormat,seriesLayoutBy:t.seriesLayoutBy,dimensionsDefine:y(t.dimensionsDefine),startIndex:t.startIndex,dimensionsDetectedCount:t.dimensionsDetectedCount})]),{sourceList:e,upstreamSignList:a}},dg.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},dg.prototype.getSource=function(t){var e=this._sourceList[t=t||0];return e||(e=this._getUpstreamSourceManagers())[0]&&e[0].getSource(t)},dg.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},dg.prototype._innerGetDataStore=function(t,e,n){var i,r=this._storeList,o=r[0],r=(o=o||(r[0]={}))[n];return r||(i=this._getUpstreamSourceManagers()[0],gg(this._sourceHost)&&i?r=i._innerGetDataStore(t,e,n):(r=new hg).initData(new lf(e,t.length),t),o[n]=r),r},dg.prototype._getUpstreamSourceManagers=function(){var t,e=this._sourceHost;return gg(e)?(t=rd(e))?[t.getSourceManager()]:[]:B((t=e).get("transform",!0)||t.get("fromTransformResult",!0)?zo(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},Eo).models:[],function(t){return t.getSourceManager()})},dg.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;return gg(i)?(t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)):this._getUpstreamSourceManagers().length||(t=(i=i).get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)),{seriesLayoutBy:t,sourceHeader:e,dimensions:n}};var pg=dg;function dg(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}function fg(t){t.option.transform&&kt(t.option.transform)}function gg(t){return"series"===t.mainType}function yg(t){throw new Error(t)}var mg="line-height:1";function vg(t){t=t.lineHeight;return null==t?mg:"line-height:"+_e(t+"")+"px"}function _g(t,e){var n=t.color||"#6e7079",i=t.fontSize||12,r=t.fontWeight||"400",o=t.color||"#464646",a=t.fontSize||14,t=t.fontWeight||"900";return"html"===e?{nameStyle:"font-size:"+_e(i+"")+"px;color:"+_e(n)+";font-weight:"+_e(r+""),valueStyle:"font-size:"+_e(a+"")+"px;color:"+_e(o)+";font-weight:"+_e(t+"")}:{nameStyle:{fontSize:i,fill:n,fontWeight:r},valueStyle:{fontSize:a,fill:o,fontWeight:t}}}var xg=[0,10,20,30],wg=["","\n","\n\n","\n\n\n"];function bg(t,e){return e.type=t,e}function Sg(t){return"section"===t.type}function Mg(t){return Sg(t)?Tg:Cg}function Tg(i,r,t,o){var n,e=r.noHeader,a=(l=function n(t){var i,e,r;return Sg(t)?(i=0,e=t.blocks.length,r=1<e||0<e&&!t.noHeader,O(t.blocks,function(t){var e=n(t);i<=e&&(i=e+ +(r&&(!e||Sg(t)&&!t.noHeader)))}),i):0}(r),{html:xg[l],richText:wg[l]}),s=[],l=r.blocks||[],u=(Tt(!l||F(l)),l=l||[],i.orderMode),h=(r.sortBlocks&&u&&(l=l.slice(),Bt(h={valueAsc:"asc",valueDesc:"desc"},u)?(n=new Wf(h[u],null),l.sort(function(t,e){return n.evaluate(t.sortParam,e.sortParam)})):"seriesDesc"===u&&l.reverse()),O(l,function(t,e){var n=r.valueFormatter,n=Mg(t)(n?L(L({},i),{valueFormatter:n}):i,t,0<e?a.html:0,o);null!=n&&s.push(n)}),"richText"===i.renderMode?s.join(a.richText):kg(o,s.join(""),e?t:a.html));return e?h:(u=bp(r.header,"ordinal",i.useUTC),l=_g(o,i.renderMode).nameStyle,e=vg(o),"richText"===i.renderMode?Dg(i,u,l)+a.richText+h:kg(o,'<div style="'+l+";"+e+';">'+_e(u)+"</div>"+h,t))}function Cg(t,e,n,i){var r,o,a,s,l,u=t.renderMode,h=e.noName,c=e.noValue,p=!e.markerType,d=e.name,f=t.useUTC,g=e.valueFormatter||t.valueFormatter||function(t){return B(t=F(t)?t:[t],function(t,e){return bp(t,F(o)?o[e]:o,f)})};if(!h||!c)return r=p?"":t.markupStyleCreator.makeTooltipMarker(e.markerType,e.markerColor||"#333",u),d=h?"":bp(d,"ordinal",f),o=e.valueType,g=c?[]:g(e.value,e.dataIndex),e=!p||!h,a=!p&&h,l=_g(i,u),s=l.nameStyle,l=l.valueStyle,"richText"===u?(p?"":r)+(h?"":Dg(t,d,s))+(c?"":function(t,e,n,i,r){r=[r],i=i?10:20;return n&&r.push({padding:[0,0,0,i],align:"right"}),t.markupStyleCreator.wrapRichTextStyle(F(e)?e.join("  "):e,r)}(t,g,e,a,l)):kg(i,(p?"":r)+(h?"":'<span style="'+s+";"+(!p?"margin-left:2px":"")+'">'+_e(d)+"</span>")+(c?"":function(t,e,n,i){n=n?"10px":"20px",e=e?"float:right;margin-left:"+n:"";return t=F(t)?t:[t],'<span style="'+e+";"+i+'">'+B(t,_e).join("&nbsp;&nbsp;")+"</span>"}(g,e,a,l)),n)}function Ig(t,e,n,i,r,o){if(t)return Mg(t)({useUTC:r,renderMode:n,orderMode:i,markupStyleCreator:e,valueFormatter:t.valueFormatter},t,0,o)}function kg(t,e,n){return'<div style="'+("margin: "+n+"px 0 0")+";"+vg(t)+';">'+e+'<div style="clear:both"></div></div>'}function Dg(t,e,n){return t.markupStyleCreator.wrapRichTextStyle(e,n)}function Ag(t,e){t=t.get("padding");return null!=t?t:"richText"===e?[8,10]:10}Lg.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},Lg.prototype.makeTooltipMarker=function(t,e,n){var i="richText"===n?this._generateStyleName():null,e=Cp({color:e,type:t,renderMode:n,markerId:i});return V(e)?e:(this.richTextStyles[i]=e.style,e.content)},Lg.prototype.wrapRichTextStyle=function(t,e){var n={},e=(F(e)?O(e,function(t){return L(n,t)}):L(n,e),this._generateStyleName());return this.richTextStyles[e]=n,"{"+e+"|"+t+"}"};var Pg=Lg;function Lg(){this.richTextStyles={},this._nextStyleNameId=yo()}function Og(t){var e,n,i,r,o,a,s,l,u,h,c,p=t.series,d=t.dataIndex,t=t.multipleSeries,f=p.getData(),g=f.mapDimensionsAll("defaultedTooltip"),y=g.length,m=p.getRawValue(d),v=F(m),_=(_=d,Ip((w=p).getData().getItemVisual(_,"style")[w.visualDrawType]));function x(t,e){e=s.getDimensionInfo(e);e&&!1!==e.otherDims.tooltip&&(l?c.push(bg("nameValue",{markerType:"subItem",markerColor:a,name:e.displayName,value:t,valueType:e.type})):(u.push(t),h.push(e.type)))}1<y||v&&!y?(w=m,r=d,o=g,a=_,s=p.getData(),l=lt(w,function(t,e,n){n=s.getDimensionInfo(n);return t||n&&!1!==n.tooltip&&null!=n.displayName},!1),u=[],h=[],c=[],o.length?O(o,function(t){x(wf(s,r,t),t)}):O(w,x),e=(o={inlineValues:u,inlineValueTypes:h,blocks:c}).inlineValueTypes,n=o.blocks,i=(o=o.inlineValues)[0]):y?(w=f.getDimensionInfo(g[0]),i=o=wf(f,d,g[0]),e=w.type):i=o=v?m[0]:m;var y=ko(p),g=y&&p.name||"",w=f.getName(d),v=t?g:w;return bg("section",{header:g,noHeader:t||!y,sortParam:i,blocks:[bg("nameValue",{markerType:"item",markerColor:_,name:v,noName:!Ct(v),value:o,valueType:e,dataIndex:d})].concat(n||[])})}var Rg=Lo();function Ng(t,e){return t.getName(e)||t.getId(e)}u(h,Eg=g),h.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=Tf({count:Fg,reset:Vg}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n);(Rg(this).sourceManager=new pg(this)).prepareSource();t=this.getInitialData(t,n);Gg(t,this),this.dataTask.context.data=t,Rg(this).dataBeforeProcessed=t,Bg(this),this._initSelectedMapFromData(t)},h.prototype.mergeDefaultAndTheme=function(t,e){var n=Np(this),i=n?zp(t):{},r=this.subType;g.hasClass(r),d(t,e.getTheme().get(this.subType)),d(t,this.getDefaultOption()),wo(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&Ep(t,i,n)},h.prototype.mergeOption=function(t,e){t=d(this.option,t,!0),this.fillDataTextStyle(t.data);var n=Np(this),n=(n&&Ep(this.option,t,n),Rg(this).sourceManager),n=(n.dirty(),n.prepareSource(),this.getInitialData(t,e));Gg(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,Rg(this).dataBeforeProcessed=n,Bg(this),this._initSelectedMapFromData(n)},h.prototype.fillDataTextStyle=function(t){if(t&&!gt(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&wo(t[n],"label",e)},h.prototype.getInitialData=function(t,e){},h.prototype.appendData=function(t){this.getRawData().appendData(t.data)},h.prototype.getData=function(t){var e=Ug(this);return e?(e=e.context.data,null!=t&&e.getLinkedData?e.getLinkedData(t):e):Rg(this).data},h.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},h.prototype.setData=function(t){var e,n=Ug(this);n&&((e=n.context).outputData=t,n!==this.dataTask)&&(e.data=t),Rg(this).data=t},h.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return E(t)},h.prototype.getSourceManager=function(){return Rg(this).sourceManager},h.prototype.getSource=function(){return this.getSourceManager().getSource()},h.prototype.getRawData=function(){return Rg(this).dataBeforeProcessed},h.prototype.getColorBy=function(){return this.get("colorBy")||"series"},h.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},h.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},h.prototype.formatTooltip=function(t,e,n){return Og({series:this,dataIndex:t,multipleSeries:e})},h.prototype.isAnimationEnabled=function(){var t=this.ecModel;return!!(!b.node||t&&t.ssr)&&!!(t=(t=this.getShallow("animation"))&&this.getData().count()>this.getShallow("animationThreshold")?!1:t)},h.prototype.restoreData=function(){this.dataTask.dirty()},h.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel;return cd.prototype.getColorFromPalette.call(this,t,e,n)||i.getColorFromPalette(t,e,n)},h.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},h.prototype.getProgressive=function(){return this.get("progressive")},h.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},h.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},h.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)this.option.selectedMap={},this._selectedDataIndicesMap={};else for(var o=0;o<t.length;o++){var a=Ng(r,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},h.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},h.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=ht(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];0<=r&&n.push(r)}return n},h.prototype.isSelected=function(t,e){var n=this.option.selectedMap;return!!n&&(e=this.getData(e),"all"===n||n[Ng(e,t)])&&!e.getItemModel(t).get(["select","disabled"])},h.prototype.isUniversalTransitionEnabled=function(){var t;return!!this.__universalTransitionEnabled||!!(t=this.option.universalTransition)&&(!0===t||t&&t.enabled)},h.prototype._innerSelect=function(t,e){var n=this.option,i=n.selectedMode,r=e.length;if(i&&r)if("series"===i)n.selectedMap="all";else if("multiple"===i){R(n.selectedMap)||(n.selectedMap={});for(var o=n.selectedMap,a=0;a<r;a++){var s,l=e[a];o[s=Ng(t,l)]=!0,this._selectedDataIndicesMap[s]=t.getRawIndex(l)}}else"single"!==i&&!0!==i||(s=Ng(t,i=e[r-1]),n.selectedMap=((n={})[s]=!0,n),this._selectedDataIndicesMap=((n={})[s]=t.getRawIndex(i),n))},h.prototype._initSelectedMapFromData=function(n){var i;this.option.selectedMap||(i=[],n.hasItemOption&&n.each(function(t){var e=n.getRawDataItem(t);e&&e.selected&&i.push(t)}),0<i.length&&this._innerSelect(n,i))},h.registerClass=function(t){return g.registerClass(t)},h.protoInitialize=((Ic=h.prototype).type="series.__base__",Ic.seriesIndex=0,Ic.ignoreStyleOnData=!1,Ic.hasSymbolVisual=!1,Ic.defaultSymbol="circle",Ic.visualStyleAccessPath="itemStyle",void(Ic.visualDrawType="fill"));var Eg,zg=h;function h(){var t=null!==Eg&&Eg.apply(this,arguments)||this;return t._selectedDataIndicesMap={},t}function Bg(t){var e,n,i=t.name;ko(t)||(t.name=(t=(e=(t=t).getRawData()).mapDimensionsAll("seriesName"),n=[],O(t,function(t){t=e.getDimensionInfo(t);t.displayName&&n.push(t.displayName)}),n.join(" ")||i))}function Fg(t){return t.model.getRawData().count()}function Vg(t){t=t.model;return t.setData(t.getRawData().cloneShallow()),Hg}function Hg(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function Gg(e,n){O(Nt(e.CHANGABLE_METHODS,e.DOWNSAMPLE_METHODS),function(t){e.wrapMethod(t,pt(Wg,n))})}function Wg(t,e){t=Ug(t);return t&&t.setOutputEnd((e||this).count()),e}function Ug(t){var e,n=(t.ecModel||{}).scheduler,n=n&&n.getPipeline(t.uid);if(n)return(n=n.currentTask)&&(e=n.agentStubMap)?e.get(t.uid):n}at(zg,Pc),at(zg,cd),Xo(zg,g);Yg.prototype.init=function(t,e){},Yg.prototype.render=function(t,e,n,i){},Yg.prototype.dispose=function(t,e){},Yg.prototype.updateView=function(t,e,n,i){},Yg.prototype.updateLayout=function(t,e,n,i){},Yg.prototype.updateVisual=function(t,e,n,i){},Yg.prototype.toggleBlurSeries=function(t,e,n){},Yg.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)};var Xg=Yg;function Yg(){this.group=new Gr,this.uid=zc("viewComponent")}function qg(){var o=Lo();return function(t){var e=o(t),t=t.pipelineContext,n=!!e.large,i=!!e.progressiveRender,r=e.large=!(!t||!t.large),e=e.progressiveRender=!(!t||!t.progressiveRender);return!(n==r&&i==e)&&"reset"}}Uo(Xg),jo(Xg);var Zg=Lo(),jg=qg(),Kg=($g.prototype.init=function(t,e){},$g.prototype.render=function(t,e,n,i){},$g.prototype.highlight=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&Jg(t,i,"emphasis")},$g.prototype.downplay=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&Jg(t,i,"normal")},$g.prototype.remove=function(t,e){this.group.removeAll()},$g.prototype.dispose=function(t,e){},$g.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},$g.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},$g.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},$g.prototype.eachRendered=function(t){ac(this.group,t)},$g.markUpdateMethod=function(t,e){Zg(t).updateMethod=e},$g.protoInitialize=void($g.prototype.type="chart"),$g);function $g(){this.group=new Gr,this.uid=zc("viewChart"),this.renderTask=Tf({plan:ty,reset:ey}),this.renderTask.context={view:this}}function Qg(t,e,n){t&&Vl(t)&&("emphasis"===e?Sl:Ml)(t,n)}function Jg(e,t,n){var i,r=Po(e,t),o=t&&null!=t.highlightKey?(t=t.highlightKey,i=null==(i=Zs[t])&&qs<=32?Zs[t]=qs++:i):null;null!=r?O(xo(r),function(t){Qg(e.getItemGraphicEl(t),n,o)}):e.eachItemGraphicEl(function(t){Qg(t,n,o)})}function ty(t){return jg(t.model)}function ey(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,t=t.view,a=r&&Zg(r).updateMethod,o=o?"incrementalPrepareRender":a&&t[a]?a:"render";return"render"!==o&&t[o](e,n,i,r),ny[o]}Uo(Kg),jo(Kg);var ny={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},iy="\0__throttleOriginMethod",ry="\0__throttleRate",oy="\0__throttleType";function ay(t,r,o){var a,s,l,u,h,c=0,p=0,d=null;function f(){p=(new Date).getTime(),d=null,t.apply(l,u||[])}r=r||0;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];a=(new Date).getTime(),l=this,u=t;var n=h||r,i=h||o;h=null,s=a-(i?c:p)-n,clearTimeout(d),i?d=setTimeout(f,n):0<=s?f():d=setTimeout(f,-s),c=a}return e.clear=function(){d&&(clearTimeout(d),d=null)},e.debounceNextCall=function(t){h=t},e}function sy(t,e,n,i){var r=t[e];if(r){var o=r[iy]||r,a=r[oy];if(r[ry]!==n||a!==i){if(null==n||!i)return t[e]=o;(r=t[e]=ay(o,n,"debounce"===i))[iy]=o,r[oy]=i,r[ry]=n}}}function ly(t,e){var n=t[e];n&&n[iy]&&(n.clear&&n.clear(),t[e]=n[iy])}var uy=Lo(),hy={itemStyle:Ko(Dc,!0),lineStyle:Ko(Tc,!0)},cy={lineStyle:"stroke",itemStyle:"fill"};function py(t,e){t=t.visualStyleMapper||hy[e];return t||(console.warn("Unknown style type '"+e+"'."),hy.itemStyle)}function dy(t,e){t=t.visualDrawType||cy[e];return t||(console.warn("Unknown style type '"+e+"'."),"fill")}var Qo={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),n=r.visualStyleAccessPath||"itemStyle",i=r.getModel(n),o=py(r,n)(i),i=i.getShallow("decal"),a=(i&&(e.setVisual("decal",i),i.dirty=!0),dy(r,n)),i=o[a],s=k(i)?i:null,n="auto"===o.fill||"auto"===o.stroke;if(o[a]&&!s&&!n||(i=r.getColorFromPalette(r.name,null,t.getSeriesCount()),o[a]||(o[a]=i,e.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||k(o.fill)?i:o.fill,o.stroke="auto"===o.stroke||k(o.stroke)?i:o.stroke),e.setVisual("style",o),e.setVisual("drawType",a),!t.isSeriesFiltered(r)&&s)return e.setVisual("colorFromPalette",!1),{dataEach:function(t,e){var n=r.getDataParams(e),i=L({},o);i[a]=s(n),t.setItemVisual(e,"style",i)}}}},fy=new Rc,jh={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var i,r,o;if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t))return e=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=py(t,i),o=e.getVisual("drawType"),{dataEach:e.hasItemOption?function(t,e){var n=t.getRawDataItem(e);n&&n[i]&&(fy.option=n[i],n=r(fy),L(t.ensureUniqueItemVisual(e,"style"),n),fy.option.decal&&(t.setItemVisual(e,"decal",fy.option.decal),fy.option.decal.dirty=!0),o in n)&&t.setItemVisual(e,"colorFromPalette",!1)}:null}}},$c={performRawSeries:!0,overallReset:function(e){var i=E();e.eachSeries(function(t){var e,n=t.getColorBy();t.isColorBySeries()||(n=t.type+"-"+n,(e=i.get(n))||i.set(n,e={}),uy(t).scope=e)}),e.eachSeries(function(i){var r,o,a,s,t,l;i.isColorBySeries()||e.isSeriesFiltered(i)||(r=i.getRawData(),o={},a=i.getData(),s=uy(i).scope,t=i.visualStyleAccessPath||"itemStyle",l=dy(i,t),a.each(function(t){var e=a.getRawIndex(t);o[e]=t}),r.each(function(t){var e,n=o[t];a.getItemVisual(n,"colorFromPalette")&&(n=a.ensureUniqueItemVisual(n,"style"),t=r.getName(t)||t+"",e=r.count(),n[l]=i.getColorFromPalette(t,s,e))}))})}},gy=Math.PI;my.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){t=t.overallTask;t&&t.dirty()})},my.prototype.getPerformArgs=function(t,e){var n,i;if(t.__pipeline)return i=(n=this._pipelineMap.get(t.__pipeline.id)).context,{step:e=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,modBy:null!=(t=i&&i.modDataCount)?Math.ceil(t/e):null,modDataCount:t}},my.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},my.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),e=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,r=t.get("large")&&i>=t.get("largeThreshold"),i="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:e,modDataCount:i,large:r}},my.prototype.restorePipelines=function(t){var i=this,r=i._pipelineMap=E();t.eachSeries(function(t){var e=t.getProgressive(),n=t.uid;r.set(n,{id:n,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:e&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(e||700),count:0}),i._pipe(t,t.dataTask)})},my.prototype.prepareStageTasks=function(){var n=this._stageTaskMap,i=this.api.getModel(),r=this.api;O(this._allHandlers,function(t){var e=n.get(t.uid)||n.set(t.uid,{});Tt(!(t.reset&&t.overallReset),""),t.reset&&this._createSeriesStageTask(t,e,i,r),t.overallReset&&this._createOverallStageTask(t,e,i,r)},this)},my.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},my.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},my.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},my.prototype._performStageTasks=function(t,s,l,u){u=u||{};var h=!1,c=this;function p(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}O(t,function(i,t){var e,n,r,o,a;u.visualType&&u.visualType!==i.visualType||(e=(n=c._stageTaskMap.get(i.uid)).seriesTaskMap,(n=n.overallTask)?((o=n.agentStubMap).each(function(t){p(u,t)&&(t.dirty(),r=!0)}),r&&n.dirty(),c.updatePayload(n,l),a=c.getPerformArgs(n,u.block),o.each(function(t){t.perform(a)}),n.perform(a)&&(h=!0)):e&&e.each(function(t,e){p(u,t)&&t.dirty();var n=c.getPerformArgs(t,u.block);n.skip=!i.performRawSeries&&s.isSeriesFiltered(t.context.model),c.updatePayload(t,l),t.perform(n)&&(h=!0)}))}),this.unfinished=h||this.unfinished},my.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e=t.dataTask.perform()||e}),this.unfinished=e||this.unfinished},my.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}}while(e=e.getUpstream())})},my.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},my.prototype._createSeriesStageTask=function(n,t,i,r){var o=this,a=t.seriesTaskMap,s=t.seriesTaskMap=E(),t=n.seriesType,e=n.getTargetSeries;function l(t){var e=t.uid,e=s.set(e,a&&a.get(e)||Tf({plan:by,reset:Sy,count:Cy}));e.context={model:t,ecModel:i,api:r,useClearVisual:n.isVisual&&!n.isLayout,plan:n.plan,reset:n.reset,scheduler:o},o._pipe(t,e)}n.createOnAllSeries?i.eachRawSeries(l):t?i.eachRawSeriesByType(t,l):e&&e(i,r).each(l)},my.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||Tf({reset:vy}),a=(o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r},o.agentStubMap),s=o.agentStubMap=E(),e=t.seriesType,l=t.getTargetSeries,u=!0,h=!1;function c(t){var e=t.uid,e=s.set(e,a&&a.get(e)||(h=!0,Tf({reset:_y,onDirty:wy})));e.context={model:t,overallProgress:u},e.agent=o,e.__block=u,r._pipe(t,e)}Tt(!t.createOnAllSeries,""),e?n.eachRawSeriesByType(e,c):l?l(n,i).each(c):(u=!1,O(n.getSeries(),c)),h&&o.dirty()},my.prototype._pipe=function(t,e){t=t.uid,t=this._pipelineMap.get(t);t.head||(t.head=e),t.tail&&t.tail.pipe(e),(t.tail=e).__idxInPipeline=t.count++,e.__pipeline=t},my.wrapStageHandler=function(t,e){return(t=k(t)?{overallReset:t,seriesType:function(t){Iy=null;try{t(ky,Dy)}catch(t){}return Iy}(t)}:t).uid=zc("stageHandler"),e&&(t.visualType=e),t};var yy=my;function my(t,e,n,i){this._stageTaskMap=E(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}function vy(t){t.overallReset(t.ecModel,t.api,t.payload)}function _y(t){return t.overallProgress&&xy}function xy(){this.agent.dirty(),this.getDownstream().dirty()}function wy(){this.agent&&this.agent.dirty()}function by(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function Sy(t){t.useClearVisual&&t.data.clearAllVisual();t=t.resetDefines=xo(t.reset(t.model,t.ecModel,t.api,t.payload));return 1<t.length?B(t,function(t,e){return Ty(e)}):My}var My=Ty(0);function Ty(o){return function(t,e){var n=e.data,i=e.resetDefines[o];if(i&&i.dataEach)for(var r=t.start;r<t.end;r++)i.dataEach(n,r);else i&&i.progress&&i.progress(t,n)}}function Cy(t){return t.data.count()}var Iy,ky={},Dy={};function Ay(t,e){for(var n in e.prototype)t[n]=Ft}Ay(ky,yd),Ay(Dy,xd),ky.eachSeriesByType=ky.eachRawSeriesByType=function(t){Iy=t},ky.eachComponent=function(t){"series"===t.mainType&&t.subType&&(Iy=t.subType)};function Py(){return{axisLine:{lineStyle:{color:Ly}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}}var Ic=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],Pc={color:Ic,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],Ic]},Ly="#B9B8CE",Dc="#100C2A",Tc=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],Ic={darkMode:!0,color:Tc,backgroundColor:Dc,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Ly},pageTextStyle:{color:Ly}},textStyle:{color:Ly},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Ly}},dataZoom:{borderColor:"#71708A",textStyle:{color:Ly},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Ly}},timeline:{lineStyle:{color:Ly},label:{color:Ly},controlStyle:{color:Ly,borderColor:Ly}},calendar:{itemStyle:{color:Dc},dayLabel:{color:Ly},monthLabel:{color:Ly},yearLabel:{color:Ly}},timeAxis:Py(),logAxis:Py(),valueAxis:Py(),categoryAxis:Py(),line:{symbol:"circle"},graph:{color:Tc},gauge:{title:{color:Ly},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Ly},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}},Oy=(Ic.categoryAxis.splitLine.show=!1,Ry.prototype.normalizeQuery=function(t){var e,a,s,l={},u={},h={};return V(t)?(e=Wo(t),l.mainType=e.main||null,l.subType=e.sub||null):(a=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1},O(t,function(t,e){for(var n=!1,i=0;i<a.length;i++){var r=a[i],o=e.lastIndexOf(r);0<o&&o===e.length-r.length&&"data"!==(o=e.slice(0,o))&&(l.mainType=o,l[r.toLowerCase()]=t,n=!0)}s.hasOwnProperty(e)&&(u[e]=t,n=!0),n||(h[e]=t)})),{cptQuery:l,dataQuery:u,otherQuery:h}},Ry.prototype.filter=function(t,e){var n,i,r,o,a,s=this.eventInfo;return!s||(n=s.targetEl,i=s.packedEvent,r=s.model,s=s.view,!r)||!s||(o=e.cptQuery,a=e.dataQuery,l(o,r,"mainType")&&l(o,r,"subType")&&l(o,r,"index","componentIndex")&&l(o,r,"name")&&l(o,r,"id")&&l(a,i,"name")&&l(a,i,"dataIndex")&&l(a,i,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,i)));function l(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},Ry.prototype.afterTrigger=function(){this.eventInfo=null},Ry);function Ry(){}var Ny=["symbol","symbolSize","symbolRotate","symbolOffset"],Ey=Ny.concat(["symbolKeepAspect"]),Dc={createOnAllSeries:!0,performRawSeries:!0,reset:function(a,t){var e=a.getData();if(a.legendIcon&&e.setVisual("legendIcon",a.legendIcon),a.hasSymbolVisual){for(var s,n={},l={},i=!1,r=0;r<Ny.length;r++){var o=Ny[r],u=a.get(o);k(u)?(i=!0,l[o]=u):n[o]=u}if(n.symbol=n.symbol||a.defaultSymbol,e.setVisual(L({legendIcon:a.legendIcon||n.symbol,symbolKeepAspect:a.get("symbolKeepAspect")},n)),!t.isSeriesFiltered(a))return s=ht(l),{dataEach:i?function(t,e){for(var n=a.getRawValue(e),i=a.getDataParams(e),r=0;r<s.length;r++){var o=s[r];t.setItemVisual(e,o,l[o](n,i))}}:null}}}},Tc={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),i=0;i<Ey.length;i++){var r=Ey[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}:null}}};function zy(t,e,s,n,l){var u=t+e;s.isSilent(u)||n.eachComponent({mainType:"series",subType:"pie"},function(t){for(var e,n,i=t.seriesIndex,r=t.option.selectedMap,o=l.selected,a=0;a<o.length;a++)o[a].seriesIndex===i&&(n=Po(e=t.getData(),l.fromActionPayload),s.trigger(u,{type:u,seriesId:t.id,name:F(n)?e.getName(n[0]):e.getName(n),selected:V(r)?r:L({},r)}))})}function By(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var Fy=Math.round(9*Math.random()),Vy="function"==typeof Object.defineProperty,Hy=(Gy.prototype.get=function(t){return this._guard(t)[this._id]},Gy.prototype.set=function(t,e){t=this._guard(t);return Vy?Object.defineProperty(t,this._id,{value:e,enumerable:!1,configurable:!0}):t[this._id]=e,this},Gy.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},Gy.prototype.has=function(t){return!!this._guard(t)[this._id]},Gy.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},Gy);function Gy(){this._id="__ec_inner_"+Fy++}var Wy=j.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i+e),t.lineTo(n-r,i+e),t.closePath()}}),Uy=j.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i),t.lineTo(n,i+e),t.lineTo(n-r,i),t.closePath()}}),Xy=j.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,e=Math.max(r,e.height),r=r/2,o=r*r/(e-r),e=i-e+r+o,a=Math.asin(o/r),s=Math.cos(a)*r,l=Math.sin(a),u=Math.cos(a),h=.6*r,c=.7*r;t.moveTo(n-s,e+o),t.arc(n,e,r,Math.PI-a,2*Math.PI+a),t.bezierCurveTo(n+s-l*h,e+o+u*h,n,i-c,n,i),t.bezierCurveTo(n,i-c,n-s+l*h,e+o+u*h,n-s,e+o),t.closePath()}}),Yy=j.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,e=e.y,i=i/3*2;t.moveTo(r,e),t.lineTo(r+i,e+n),t.lineTo(r,e+n/4*3),t.lineTo(r-i,e+n),t.lineTo(r,e),t.closePath()}}),qy={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){n=Math.min(n,i);r.x=t,r.y=e,r.width=n,r.height=n},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},Zy={},jy=(O({line:ju,rect:As,roundRect:As,square:As,circle:hu,diamond:Uy,pin:Xy,arrow:Yy,triangle:Wy},function(t,e){Zy[e]=new t}),j.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var t=Dr(t,e,n),i=this.shape;return i&&"pin"===i.symbolType&&"inside"===e.position&&(t.y=n.y+.4*n.height),t},buildPath:function(t,e,n){var i,r=e.symbolType;"none"!==r&&(i=(i=Zy[r])||Zy[r="rect"],qy[r](e.x,e.y,e.width,e.height,i.shape),i.buildPath(t,i.shape,n))}}));function Ky(t,e){var n;"image"!==this.type&&(n=this.style,this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw())}function $y(t,e,n,i,r,o,a){var s=0===t.indexOf("empty");return(a=0===(t=s?t.substr(5,1).toLowerCase()+t.substr(6):t).indexOf("image://")?Uh(t.slice(8),new X(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?Wh(t.slice(7),{},new X(e,n,i,r),a?"center":"cover"):new jy({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=s,a.setColor=Ky,o&&a.setColor(o),a}function Qy(t,e){if(null!=t)return[eo((t=F(t)?t:[t,t])[0],e[0])||0,eo(N(t[1],t[0]),e[1])||0]}function Jy(t){return isFinite(t)}function tm(t,e,n){for(var i,r,o,a,s,l,u,h,c,p="radial"===e.type?(i=t,r=e,a=(o=n).width,s=o.height,l=Math.min(a,s),u=null==r.x?.5:r.x,h=null==r.y?.5:r.y,c=null==r.r?.5:r.r,r.global||(u=u*a+o.x,h=h*s+o.y,c*=l),u=Jy(u)?u:.5,h=Jy(h)?h:.5,c=0<=c&&Jy(c)?c:.5,i.createRadialGradient(u,h,0,u,h,c)):(r=t,a=n,o=null==(s=e).x?0:s.x,l=null==s.x2?1:s.x2,i=null==s.y?0:s.y,u=null==s.y2?0:s.y2,s.global||(o=o*a.width+a.x,l=l*a.width+a.x,i=i*a.height+a.y,u=u*a.height+a.y),o=Jy(o)?o:0,l=Jy(l)?l:1,i=Jy(i)?i:0,u=Jy(u)?u:0,r.createLinearGradient(o,i,l,u)),d=e.colorStops,f=0;f<d.length;f++)p.addColorStop(d[f].offset,d[f].color);return p}function em(t){return parseInt(t,10)}function nm(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],e=["paddingRight","paddingBottom"][e];return null!=n[i]&&"auto"!==n[i]?parseFloat(n[i]):(n=document.defaultView.getComputedStyle(t),(t[r]||em(n[i])||em(t.style[i]))-(em(n[o])||0)-(em(n[e])||0)|0)}function im(t){var e,n=t.style,i=n.lineDash&&0<n.lineWidth&&(r=n.lineDash,i=n.lineWidth,r&&"solid"!==r&&0<i?"dashed"===r?[4*i,2*i]:"dotted"===r?[i]:H(r)?[r]:F(r)?r:null:null),r=n.lineDashOffset;return i&&(e=n.strokeNoScale&&t.getLineScale?t.getLineScale():1)&&1!==e&&(i=B(i,function(t){return t/e}),r/=e),[i,r]}var rm=new Ka(!0);function om(t){var e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))}function am(t){return"string"==typeof t&&"none"!==t}function sm(t){t=t.fill;return null!=t&&"none"!==t}function lm(t,e){var n;null!=e.fillOpacity&&1!==e.fillOpacity?(n=t.globalAlpha,t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n):t.fill()}function um(t,e){var n;null!=e.strokeOpacity&&1!==e.strokeOpacity?(n=t.globalAlpha,t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n):t.stroke()}function hm(t,e,n){var n=ea(e.image,e.__image,n);if(ia(n))return t=t.createPattern(n,e.repeat||"repeat"),"function"==typeof DOMMatrix&&t&&t.setTransform&&((n=new DOMMatrix).translateSelf(e.x||0,e.y||0),n.rotateSelf(0,0,(e.rotation||0)*Vt),n.scaleSelf(e.scaleX||1,e.scaleY||1),t.setTransform(n)),t}var cm=["shadowBlur","shadowOffsetX","shadowOffsetY"],pm=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function dm(t,e,n,i,r){var o,a=!1;if(!i&&e===(n=n||{}))return!1;!i&&e.opacity===n.opacity||(xm(t,r),a=!0,o=Math.max(Math.min(e.opacity,1),0),t.globalAlpha=isNaN(o)?ma.opacity:o),!i&&e.blend===n.blend||(a||(xm(t,r),a=!0),t.globalCompositeOperation=e.blend||ma.blend);for(var s=0;s<cm.length;s++){var l=cm[s];!i&&e[l]===n[l]||(a||(xm(t,r),a=!0),t[l]=t.dpr*(e[l]||0))}return!i&&e.shadowColor===n.shadowColor||(a||(xm(t,r),a=!0),t.shadowColor=e.shadowColor||ma.shadowColor),a}function fm(t,e,n,i,r){var o=wm(e,r.inHover),a=i?null:n&&wm(n,r.inHover)||{};if(o!==a){var s=dm(t,o,a,i,r);(i||o.fill!==a.fill)&&(s||(xm(t,r),s=!0),am(o.fill))&&(t.fillStyle=o.fill),(i||o.stroke!==a.stroke)&&(s||(xm(t,r),s=!0),am(o.stroke))&&(t.strokeStyle=o.stroke),!i&&o.opacity===a.opacity||(s||(xm(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()&&(n=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1),t.lineWidth!==n)&&(s||(xm(t,r),s=!0),t.lineWidth=n);for(var l=0;l<pm.length;l++){var u=pm[l],h=u[0];!i&&o[h]===a[h]||(s||(xm(t,r),s=!0),t[h]=o[h]||u[1])}}}function gm(t,e){var e=e.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)}var ym=1,mm=2,vm=3,_m=4;function xm(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function wm(t,e){return e&&t.__hoverStyle||t.style}function bm(t,e){Sm(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Sm(t,e,n,E){var i=e.transform;if(e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1)){var r=e.__clipPaths,o=n.prevElClipPaths,a=!1,s=!1;if(!o||function(t,e){if(t!==e&&(t||e)){if(!t||!e||t.length!==e.length)return 1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return 1}}(r,o)){if(o&&o.length&&(xm(t,n),t.restore(),s=a=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),r&&r.length){xm(t,n),t.save();for(var z=r,l=t,o=n,B=!1,F=0;F<z.length;F++){var V=z[F],B=B||V.isZeroArea();gm(l,V),l.beginPath(),V.buildPath(l,V.shape),l.clip()}o.allClipped=B,a=!0}n.prevElClipPaths=r}if(n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var u,h,c,p,d,f,g,y,m,v,_,x,w,H,b,S,M,T,C,I,k,D,A,o=n.prevEl,P=(o||(s=a=!0),e instanceof j&&e.autoBatch&&(r=e.style,P=sm(r),u=om(r),!(r.lineDash||!(+P^+u)||P&&"string"!=typeof r.fill||u&&"string"!=typeof r.stroke||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1))),a=(a||(u=i,r=o.transform,u&&r?u[0]!==r[0]||u[1]!==r[1]||u[2]!==r[2]||u[3]!==r[3]||u[4]!==r[4]||u[5]!==r[5]:u||r)?(xm(t,n),gm(t,e)):P||xm(t,n),wm(e,n.inHover));if(e instanceof j)n.lastDrawType!==ym&&(s=!0,n.lastDrawType=ym),fm(t,e,o,s,n),P&&(n.batchFill||n.batchStroke)||t.beginPath(),i=t,r=e,R=P,b=om(p=a),S=sm(p),M=p.strokePercent,T=M<1,C=!r.path,r.silent&&!T||!C||r.createPathProxy(),I=r.path||rm,k=r.__dirty,R||(d=p.fill,A=p.stroke,f=S&&!!d.colorStops,g=b&&!!A.colorStops,y=S&&!!d.image,m=b&&!!A.image,D=w=x=_=v=void 0,(f||g)&&(D=r.getBoundingRect()),f&&(v=k?tm(i,d,D):r.__canvasFillGradient,r.__canvasFillGradient=v),g&&(_=k?tm(i,A,D):r.__canvasStrokeGradient,r.__canvasStrokeGradient=_),y&&(x=k||!r.__canvasFillPattern?hm(i,d,r):r.__canvasFillPattern,r.__canvasFillPattern=x),m&&(w=k||!r.__canvasStrokePattern?hm(i,A,r):r.__canvasStrokePattern,r.__canvasStrokePattern=x),f?i.fillStyle=v:y&&(x?i.fillStyle=x:S=!1),g?i.strokeStyle=_:m&&(w?i.strokeStyle=w:b=!1)),D=r.getGlobalScale(),I.setScale(D[0],D[1],r.segmentIgnoreThreshold),i.setLineDash&&p.lineDash&&(H=(d=im(r))[0],O=d[1]),A=!0,(C||k&_n)&&(I.setDPR(i.dpr),T?I.setContext(null):(I.setContext(i),A=!1),I.reset(),r.buildPath(I,r.shape,R),I.toStatic(),r.pathUpdated()),A&&I.rebuildPath(i,T?M:1),H&&(i.setLineDash(H),i.lineDashOffset=O),R||(p.strokeFirst?(b&&um(i,p),S&&lm(i,p)):(S&&lm(i,p),b&&um(i,p))),H&&i.setLineDash([]),P&&(n.batchFill=a.fill||"",n.batchStroke=a.stroke||"");else if(e instanceof ys)n.lastDrawType!==vm&&(s=!0,n.lastDrawType=vm),fm(t,e,o,s,n),f=t,v=e,null!=(x=(y=a).text)&&(x+=""),x&&(f.font=y.font||K,f.textAlign=y.textAlign,f.textBaseline=y.textBaseline,_=g=void 0,f.setLineDash&&y.lineDash&&(g=(v=im(v))[0],_=v[1]),g&&(f.setLineDash(g),f.lineDashOffset=_),y.strokeFirst?(om(y)&&f.strokeText(x,y.x,y.y),sm(y)&&f.fillText(x,y.x,y.y)):(sm(y)&&f.fillText(x,y.x,y.y),om(y)&&f.strokeText(x,y.x,y.y)),g)&&f.setLineDash([]);else if(e instanceof ws)n.lastDrawType!==mm&&(s=!0,n.lastDrawType=mm),m=o,w=s,dm(t,wm(e,(D=n).inHover),m&&wm(m,D.inHover),w,D),d=t,C=a,(r=(k=e).__image=ea(C.image,k.__image,k,k.onload))&&ia(r)&&(A=C.x||0,I=C.y||0,T=k.getWidth(),k=k.getHeight(),M=r.width/r.height,null==T&&null!=k?T=k*M:null==k&&null!=T?k=T/M:null==T&&null==k&&(T=r.width,k=r.height),C.sWidth&&C.sHeight?(h=C.sx||0,c=C.sy||0,d.drawImage(r,h,c,C.sWidth,C.sHeight,A,I,T,k)):C.sx&&C.sy?(h=C.sx,c=C.sy,d.drawImage(r,h,c,T-h,k-c,A,I,T,k)):d.drawImage(r,A,I,T,k));else if(e.getTemporalDisplayables){n.lastDrawType!==_m&&(s=!0,n.lastDrawType=_m);var L,G,W=t,O=e,R=n,U=O.getDisplayables(),X=O.getTemporalDisplayables(),Y=(W.save(),{prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:R.viewWidth,viewHeight:R.viewHeight,inHover:R.inHover});for(L=O.getCursor(),G=U.length;L<G;L++)(N=U[L]).beforeBrush&&N.beforeBrush(),N.innerBeforeBrush(),Sm(W,N,Y,L===G-1),N.innerAfterBrush(),N.afterBrush&&N.afterBrush(),Y.prevEl=N;for(var N,q=0,Z=X.length;q<Z;q++)(N=X[q]).beforeBrush&&N.beforeBrush(),N.innerBeforeBrush(),Sm(W,N,Y,q===Z-1),N.innerAfterBrush(),N.afterBrush&&N.afterBrush(),Y.prevEl=N;O.clearTemporalDisplayables(),O.notClear=!0,W.restore()}P&&E&&xm(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),(n.prevEl=e).__dirty=0,e.__isRendered=!0}}else e.__dirty&=~vn,e.__isRendered=!1}var Mm=new Hy,Tm=new ei(100),Cm=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Im(t,e){if("none"===t)return null;var a=e.getDevicePixelRatio(),s=e.getZr(),l="svg"===s.painter.type,e=(t.dirty&&Mm.delete(t),Mm.get(t));if(e)return e;for(var n,u=z(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512}),e=("none"===u.backgroundColor&&(u.backgroundColor=null),{repeat:"repeat"}),i=e,r=[a],o=!0,h=0;h<Cm.length;++h){var c=u[Cm[h]];if(null!=c&&!F(c)&&!V(c)&&!H(c)&&"boolean"!=typeof c){o=!1;break}r.push(c)}o&&(n=r.join(",")+(l?"-svg":""),v=Tm.get(n))&&(l?i.svgElement=v:i.image=v);var p,d=function t(e){if(!e||0===e.length)return[[0,0]];if(H(e))return[[o=Math.ceil(e),o]];var n=!0;for(var i=0;i<e.length;++i)if(!H(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i){var o;H(e[i])?(o=Math.ceil(e[i]),r.push([o,o])):(o=B(e[i],function(t){return Math.ceil(t)})).length%2==1?r.push(o.concat(o)):r.push(o)}return r}(u.dashArrayX),f=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if(H(t))return[e=Math.ceil(t),e];var e=B(t,function(t){return Math.ceil(t)});return t.length%2?e.concat(e):e}(u.dashArrayY),g=function t(e){if(!e||0===e.length)return[["rect"]];if(V(e))return[[e]];var n=!0;for(var i=0;i<e.length;++i)if(!V(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i)V(e[i])?r.push([e[i]]):r.push(e[i]);return r}(u.symbol),y=function(t){return B(t,km)}(d),m=km(f),v=!l&&G.createCanvas(),_=l&&{tag:"g",attrs:{},key:"dcl",children:[]},x=function(){for(var t=1,e=0,n=y.length;e<n;++e)t=mo(t,y[e]);for(var i=1,e=0,n=g.length;e<n;++e)i=mo(i,g[e].length);t*=i;var r=m*y.length*g.length;return{width:Math.max(1,Math.min(t,u.maxTileWidth)),height:Math.max(1,Math.min(r,u.maxTileHeight))}}();v&&(v.width=x.width*a,v.height=x.height*a,p=v.getContext("2d")),p&&(p.clearRect(0,0,v.width,v.height),u.backgroundColor)&&(p.fillStyle=u.backgroundColor,p.fillRect(0,0,v.width,v.height));for(var w=0,b=0;b<f.length;++b)w+=f[b];if(!(w<=0))for(var S=-m,M=0,T=0,C=0;S<x.height;){if(M%2==0){for(var I=T/2%g.length,k=0,D=0,A=0;k<2*x.width;){for(var P,L,O,R,N,E=0,b=0;b<d[C].length;++b)E+=d[C][b];if(E<=0)break;D%2==0&&(L=.5*(1-u.symbolSize),P=k+d[C][D]*L,L=S+f[M]*L,O=d[C][D]*u.symbolSize,R=f[M]*u.symbolSize,N=A/2%g[I].length,function(t,e,n,i,r){var o=l?1:a,r=$y(r,t*o,e*o,n*o,i*o,u.color,u.symbolKeepAspect);l?(t=s.painter.renderOneToVNode(r))&&_.children.push(t):bm(p,r)}(P,L,O,R,g[I][N])),k+=d[C][D],++A,++D===d[C].length&&(D=0)}++C===d.length&&(C=0)}S+=f[M],++T,++M===f.length&&(M=0)}return o&&Tm.put(n,v||_),i.image=v,i.svgElement=_,i.svgWidth=x.width,i.svgHeight=x.height,e.rotation=u.rotation,e.scaleX=e.scaleY=l?1:1/a,Mm.set(t,e),t.dirty=!1,e}function km(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}var Dm=new le,Am={};var Uy={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:4500,ARIA:6e3,DECAL:7e3}},Pm="__flagInMainProcess",Lm="__pendingUpdate",Om="__needsUpdateStatus",Rm=/^[a-zA-Z0-9_]+$/,Nm="__connectUpdateStatus";function Em(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(!this.isDisposed())return Bm(this,n,t);this.id}}function zm(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return Bm(this,n,t)}}function Bm(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),le.prototype[e].apply(t,n)}u(Hm,Fm=le);var Fm,Vm=Hm;function Hm(){return null!==Fm&&Fm.apply(this,arguments)||this}var Gm,Wm,Um,Xm,Ym,qm,Zm,jm,Km,$m,Qm,Jm,t0,e0,n0,i0,r0,o0,a0,Xy=Vm.prototype,s0=(Xy.on=zm("on"),Xy.off=zm("off"),u(c,a0=le),c.prototype._onframe=function(){if(!this._disposed){o0(this);var t=this._scheduler;if(this[Lm]){var e=this[Lm].silent;this[Pm]=!0;try{Gm(this),Xm.update.call(this,null,this[Lm].updateParams)}catch(t){throw this[Pm]=!1,this[Lm]=null,t}this._zr.flush(),this[Pm]=!1,this[Lm]=null,jm.call(this,e),Km.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date}while(t.performSeriesTasks(i),t.performDataProcessorTasks(i),qm(this,i),t.performVisualTasks(i),e0(this,this._model,r,"remain",{}),0<(n-=+new Date-o)&&t.unfinished);t.unfinished||this._zr.flush()}}},c.prototype.getDom=function(){return this._dom},c.prototype.getId=function(){return this.id},c.prototype.getZr=function(){return this._zr},c.prototype.isSSR=function(){return this._ssr},c.prototype.setOption=function(t,e,n){if(!this[Pm])if(this._disposed)this.id;else{R(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this[Pm]=!0,this._model&&!e||(e=new Cd(this._api),a=this._theme,(s=this._model=new yd).scheduler=this._scheduler,s.ssr=this._ssr,s.init(null,null,null,a,this._locale,e)),this._model.setOption(t,{replaceMerge:r},m0);var i,r,o,a,s={seriesTransition:o,optionChanged:!0};if(n)this[Lm]={silent:i,updateParams:s},this[Pm]=!1,this.getZr().wakeUp();else{try{Gm(this),Xm.update.call(this,null,s)}catch(t){throw this[Lm]=null,this[Pm]=!1,t}this._ssr||this._zr.flush(),this[Lm]=null,this[Pm]=!1,jm.call(this,i),Km.call(this,i)}}},c.prototype.setTheme=function(){},c.prototype.getModel=function(){return this._model},c.prototype.getOption=function(){return this._model&&this._model.getOption()},c.prototype.getWidth=function(){return this._zr.getWidth()},c.prototype.getHeight=function(){return this._zr.getHeight()},c.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||b.hasGlobalWindow&&window.devicePixelRatio||1},c.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},c.prototype.renderToCanvas=function(t){return this._zr.painter.getRenderedCanvas({backgroundColor:(t=t||{}).backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},c.prototype.renderToSVGString=function(t){return this._zr.painter.renderToString({useViewBox:(t=t||{}).useViewBox})},c.prototype.getSvgDataURL=function(){var t;if(b.svgSupported)return O((t=this._zr).storage.getDisplayList(),function(t){t.stopAnimation(null,!0)}),t.painter.toDataURL()},c.prototype.getDataURL=function(t){var e,n,i,r;if(!this._disposed)return r=(t=t||{}).excludeComponents,e=this._model,n=[],i=this,O(r,function(t){e.eachComponent({mainType:t},function(t){t=i._componentsMap[t.__viewId];t.group.ignore||(n.push(t),t.group.ignore=!0)})}),r="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png")),O(n,function(t){t.group.ignore=!1}),r;this.id},c.prototype.getConnectedDataURL=function(i){var r,o,a,s,l,u,h,c,p,e,t,n,d,f,g;if(!this._disposed)return r="svg"===i.type,o=this.group,a=Math.min,s=Math.max,b0[o]?(u=l=1/0,c=h=-1/0,p=[],e=i&&i.pixelRatio||this.getDevicePixelRatio(),O(w0,function(t,e){var n;t.group===o&&(n=r?t.getZr().painter.getSvgDom().innerHTML:t.renderToCanvas(y(i)),t=t.getDom().getBoundingClientRect(),l=a(t.left,l),u=a(t.top,u),h=s(t.right,h),c=s(t.bottom,c),p.push({dom:n,left:t.left,top:t.top}))}),t=(h*=e)-(l*=e),n=(c*=e)-(u*=e),d=G.createCanvas(),(f=jr(d,{renderer:r?"svg":"canvas"})).resize({width:t,height:n}),r?(g="",O(p,function(t){var e=t.left-l,n=t.top-u;g+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),f.painter.getSvgRoot().innerHTML=g,i.connectedBackgroundColor&&f.painter.setBackgroundColor(i.connectedBackgroundColor),f.refreshImmediately(),f.painter.toDataURL()):(i.connectedBackgroundColor&&f.add(new As({shape:{x:0,y:0,width:t,height:n},style:{fill:i.connectedBackgroundColor}})),O(p,function(t){t=new ws({style:{x:t.left*e-l,y:t.top*e-u,image:t.dom}});f.add(t)}),f.refreshImmediately(),d.toDataURL("image/"+(i&&i.type||"png")))):this.getDataURL(i);this.id},c.prototype.convertToPixel=function(t,e){return Ym(this,"convertToPixel",t,e)},c.prototype.convertFromPixel=function(t,e){return Ym(this,"convertFromPixel",t,e)},c.prototype.containPixel=function(t,i){var r;if(!this._disposed)return O(Ro(this._model,t),function(t,n){0<=n.indexOf("Models")&&O(t,function(t){var e=t.coordinateSystem;e&&e.containPoint?r=r||!!e.containPoint(i):"seriesModels"===n&&(e=this._chartsMap[t.__viewId])&&e.containPoint&&(r=r||e.containPoint(i,t))},this)},this),!!r;this.id},c.prototype.getVisual=function(t,e){var t=Ro(this._model,t,{defaultMainType:"series"}),n=t.seriesModel.getData(),t=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?n.indexOfRawIndex(t.dataIndex):null;if(null!=t){var i=n,r=t,o=e;switch(o){case"color":return i.getItemVisual(r,"style")[i.getVisual("drawType")];case"opacity":return i.getItemVisual(r,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return i.getItemVisual(r,o)}}else{var a=n,s=e;switch(s){case"color":return a.getVisual("style")[a.getVisual("drawType")];case"opacity":return a.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return a.getVisual(s)}}},c.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},c.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},c.prototype._initEvents=function(){var t,n,i,s=this;O(d0,function(a){function t(t){var n,e,i,r=s.getModel(),o=t.target;"globalout"===a?n={}:o&&By(o,function(t){var e,t=D(t);return t&&null!=t.dataIndex?(e=t.dataModel||r.getSeriesByIndex(t.seriesIndex),n=e&&e.getDataParams(t.dataIndex,t.dataType,o)||{},1):t.eventData&&(n=L({},t.eventData),1)},!0),n&&(e=n.componentType,i=n.componentIndex,"markLine"!==e&&"markPoint"!==e&&"markArea"!==e||(e="series",i=n.seriesIndex),i=(e=e&&null!=i&&r.getComponent(e,i))&&s["series"===e.mainType?"_chartsMap":"_componentsMap"][e.__viewId],n.event=t,n.type=a,s._$eventProcessor.eventInfo={targetEl:o,packedEvent:n,model:e,view:i},s.trigger(a,n))}t.zrEventfulCallAtLast=!0,s._zr.on(a,t,s)}),O(g0,function(t,e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),O(["selectchanged"],function(e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),t=this._messageCenter,i=(n=this)._api,t.on("selectchanged",function(t){var e=i.getModel();t.isFromClick?(zy("map","selectchanged",n,e,t),zy("pie","selectchanged",n,e,t)):"select"===t.fromAction?(zy("map","selected",n,e,t),zy("pie","selected",n,e,t)):"unselect"===t.fromAction&&(zy("map","unselected",n,e,t),zy("pie","unselected",n,e,t))})},c.prototype.isDisposed=function(){return this._disposed},c.prototype.clear=function(){this._disposed?this.id:this.setOption({series:[]},!0)},c.prototype.dispose=function(){var t,e,n;this._disposed?this.id:(this._disposed=!0,this.getDom()&&Bo(this.getDom(),T0,""),e=(t=this)._api,n=t._model,O(t._componentsViews,function(t){t.dispose(n,e)}),O(t._chartsViews,function(t){t.dispose(n,e)}),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete w0[t.id])},c.prototype.resize=function(t){if(!this[Pm])if(this._disposed)this.id;else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var e=e.resetOption("media"),n=t&&t.silent;this[Lm]&&(null==n&&(n=this[Lm].silent),e=!0,this[Lm]=null),this[Pm]=!0;try{e&&Gm(this),Xm.update.call(this,{type:"resize",animation:L({duration:0},t&&t.animation)})}catch(t){throw this[Pm]=!1,t}this[Pm]=!1,jm.call(this,n),Km.call(this,n)}}},c.prototype.showLoading=function(t,e){this._disposed?this.id:(R(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),x0[t]&&(t=x0[t](this._api,e),e=this._zr,this._loadingFX=t,e.add(t)))},c.prototype.hideLoading=function(){this._disposed?this.id:(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},c.prototype.makeActionFromEvent=function(t){var e=L({},t);return e.type=g0[t.type],e},c.prototype.dispatchAction=function(t,e){var n;this._disposed?this.id:(R(e)||(e={silent:!!e}),f0[t.type]&&this._model&&(this[Pm]?this._pendingActions.push(t):(n=e.silent,Zm.call(this,t,n),(t=e.flush)?this._zr.flush():!1!==t&&b.browser.weChat&&this._throttledZrFlush(),jm.call(this,n),Km.call(this,n))))},c.prototype.updateLabelLayout=function(){Dm.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},c.prototype.appendData=function(t){var e;this._disposed?this.id:(e=t.seriesIndex,this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp())},c.internalField=(Gm=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),Wm(t,!0),Wm(t,!1),e.plan()},Wm=function(t,r){for(var o=t._model,a=t._scheduler,s=r?t._componentsViews:t._chartsViews,l=r?t._componentsMap:t._chartsMap,u=t._zr,h=t._api,e=0;e<s.length;e++)s[e].__alive=!1;function n(t){var e,n=t.__requireNewView,i=(t.__requireNewView=!1,"_ec_"+t.id+"_"+t.type),n=!n&&l[i];n||(e=Wo(t.type),(n=new(r?Xg.getClass(e.main,e.sub):Kg.getClass(e.sub))).init(o,h),l[i]=n,s.push(n),u.add(n.group)),t.__viewId=n.__id=i,n.__alive=!0,n.__model=t,n.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},r||a.prepareView(n,t,o,h)}r?o.eachComponent(function(t,e){"series"!==t&&n(e)}):o.eachSeries(n);for(e=0;e<s.length;){var i=s[e];i.__alive?e++:(r||i.renderTask.dispose(),u.remove(i.group),i.dispose(o,h),s.splice(e,1),l[i.__id]===i&&delete l[i.__id],i.__id=i.group.__ecComponentInfo=null)}},Um=function(c,e,p,n,t){var i,d,r=c._model;function o(t){t&&t.__alive&&t[e]&&t[e](t.__model,r,c._api,p)}r.setUpdatePayload(p),n?((i={})[n+"Id"]=p[n+"Id"],i[n+"Index"]=p[n+"Index"],i[n+"Name"]=p[n+"Name"],i={mainType:n,query:i},t&&(i.subType=t),null!=(t=p.excludeSeriesId)&&(d=E(),O(xo(t),function(t){t=Io(t,null);null!=t&&d.set(t,!0)})),r&&r.eachComponent(i,function(t){var e,n,i=d&&null!=d.get(t.id);if(!i)if(Gl(p))if(t instanceof zg){if(p.type===nl&&!p.notBlur&&!t.get(["emphasis","disabled"])){var i=t,r=p,o=c._api,a=i.seriesIndex,s=i.getData(r.dataType);if(s){var r=(F(r=Po(s,r))?r[0]:r)||0,l=s.getItemGraphicEl(r);if(!l)for(var u=s.count(),h=0;!l&&h<u;)l=s.getItemGraphicEl(h++);l?Al(a,(r=D(l)).focus,r.blurScope,o):(r=i.get(["emphasis","focus"]),i=i.get(["emphasis","blurScope"]),null!=r&&Al(a,r,i,o))}}}else{a=Ll(t.mainType,t.componentIndex,p.name,c._api),r=a.focusSelf,i=a.dispatchers;p.type===nl&&r&&!p.notBlur&&Pl(t.mainType,t.componentIndex,c._api),i&&O(i,function(t){(p.type===nl?Sl:Ml)(t)})}else Hl(p)&&t instanceof zg&&(o=t,i=p,c._api,Hl(i)&&(e=i.dataType,F(n=Po(o.getData(e),i))||(n=[n]),o[i.type===al?"toggleSelect":i.type===rl?"select":"unselect"](n,e)),Ol(t),r0(c))},c),r&&r.eachComponent(i,function(t){d&&null!=d.get(t.id)||o(c["series"===n?"_chartsMap":"_componentsMap"][t.__viewId])},c)):O([].concat(c._componentsViews).concat(c._chartsViews),o)},Xm={prepareAndUpdate:function(t){Gm(this),Xm.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(t,e){var n=this._model,i=this._api,r=this._zr,o=this._coordSysMgr,a=this._scheduler;n&&(n.setUpdatePayload(t),a.restoreData(n,t),a.performSeriesTasks(n),o.create(n,i),a.performDataProcessorTasks(n,t),qm(this,n),o.update(n,i),l0(n),a.performVisualTasks(n,t),Jm(this,n,i,t,e),o=n.get("backgroundColor")||"transparent",a=n.get("darkMode"),r.setBackgroundColor(o),null!=a&&"auto"!==a&&r.setDarkMode(a),Dm.trigger("afterupdate",n,i))},updateTransform:function(n){var i,r,o=this,a=this._model,s=this._api;a&&(a.setUpdatePayload(n),i=[],a.eachComponent(function(t,e){"series"!==t&&(t=o.getViewOfComponentModel(e))&&t.__alive&&(!t.updateTransform||(e=t.updateTransform(e,a,s,n))&&e.update)&&i.push(t)}),r=E(),a.eachSeries(function(t){var e=o._chartsMap[t.__viewId];(!e.updateTransform||(e=e.updateTransform(t,a,s,n))&&e.update)&&r.set(t.uid,1)}),l0(a),this._scheduler.performVisualTasks(a,n,{setDirty:!0,dirtyMap:r}),e0(this,a,s,n,{},r),Dm.trigger("afterupdate",a,s))},updateView:function(t){var e=this._model;e&&(e.setUpdatePayload(t),Kg.markUpdateMethod(t,"updateView"),l0(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),Jm(this,e,this._api,t,{}),Dm.trigger("afterupdate",e,this._api))},updateVisual:function(n){var i=this,r=this._model;r&&(r.setUpdatePayload(n),r.eachSeries(function(t){t.getData().clearAllVisual()}),Kg.markUpdateMethod(n,"updateVisual"),l0(r),this._scheduler.performVisualTasks(r,n,{visualType:"visual",setDirty:!0}),r.eachComponent(function(t,e){"series"!==t&&(t=i.getViewOfComponentModel(e))&&t.__alive&&t.updateVisual(e,r,i._api,n)}),r.eachSeries(function(t){i._chartsMap[t.__viewId].updateVisual(t,r,i._api,n)}),Dm.trigger("afterupdate",r,this._api))},updateLayout:function(t){Xm.update.call(this,t)}},Ym=function(t,e,n,i){if(t._disposed)t.id;else for(var r=t._model,o=t._coordSysMgr.getCoordinateSystems(),a=Ro(r,n),s=0;s<o.length;s++){var l=o[s];if(l[e]&&null!=(l=l[e](r,a,i)))return l}},qm=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})},Zm=function(i,t){var r,o,a=this,e=this.getModel(),n=i.type,s=i.escapeConnect,l=f0[n],u=l.actionInfo,h=(u.update||"update").split(":"),c=h.pop(),p=null!=h[0]&&Wo(h[0]),h=(this[Pm]=!0,[i]),d=!1,f=(i.batch&&(d=!0,h=B(i.batch,function(t){return(t=z(L({},t),i)).batch=null,t})),[]),g=Hl(i),y=Gl(i);if(y&&Dl(this._api),O(h,function(t){var e,n;(r=(r=l.action(t,a._model,a._api))||L({},t)).type=u.event||r.type,f.push(r),y?(e=(n=No(i)).queryOptionMap,n=n.mainTypeSpecified?e.keys()[0]:"series",Um(a,c,t,n),r0(a)):g?(Um(a,c,t,"series"),r0(a)):p&&Um(a,c,t,p.main,p.sub)}),"none"!==c&&!y&&!g&&!p)try{this[Lm]?(Gm(this),Xm.update.call(this,i),this[Lm]=null):Xm[c].call(this,i)}catch(t){throw this[Pm]=!1,t}r=d?{type:u.event||n,escapeConnect:s,batch:f}:f[0],this[Pm]=!1,t||((h=this._messageCenter).trigger(r.type,r),g&&(d={type:"selectchanged",escapeConnect:s,selected:(o=[],e.eachSeries(function(n){O(n.getAllData(),function(t){t.data;var t=t.type,e=n.getSelectedDataIndices();0<e.length&&(e={dataIndex:e,seriesIndex:n.seriesIndex},null!=t&&(e.dataType=t),o.push(e))})}),o),isFromClick:i.isFromClick||!1,fromAction:i.type,fromActionPayload:i},h.trigger(d.type,d)))},jm=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();Zm.call(this,n,t)}},Km=function(t){t||this.trigger("updated")},$m=function(e,n){e.on("rendered",function(t){n.trigger("rendered",t),!e.animation.isFinished()||n[Lm]||n._scheduler.unfinished||n._pendingActions.length||n.trigger("finished")})},Qm=function(t,a){t.on("mouseover",function(t){var e,n,i,r,o=By(t.target,Vl);o&&(o=o,e=t,t=a._api,n=D(o),i=(r=Ll(n.componentMainType,n.componentIndex,n.componentHighDownName,t)).dispatchers,r=r.focusSelf,i?(r&&Pl(n.componentMainType,n.componentIndex,t),O(i,function(t){return wl(t,e)})):(Al(n.seriesIndex,n.focus,n.blurScope,t),"self"===n.focus&&Pl(n.componentMainType,n.componentIndex,t),wl(o,e)),r0(a))}).on("mouseout",function(t){var e,n,i=By(t.target,Vl);i&&(i=i,e=t,Dl(t=a._api),(n=Ll((n=D(i)).componentMainType,n.componentIndex,n.componentHighDownName,t).dispatchers)?O(n,function(t){return bl(t,e)}):bl(i,e),r0(a))}).on("click",function(t){var e,t=By(t.target,function(t){return null!=D(t).dataIndex},!0);t&&(e=t.selected?"unselect":"select",t=D(t),a._api.dispatchAction({type:e,dataType:t.dataType,dataIndexInside:t.dataIndex,seriesIndex:t.seriesIndex,isFromClick:!0}))})},Jm=function(t,e,n,i,r){var o,a,s,l,u,h,c;u=[],c=!(h=[]),(o=e).eachComponent(function(t,e){var n=e.get("zlevel")||0,i=e.get("z")||0,r=e.getZLevelKey();c=c||!!r,("series"===t?h:u).push({zlevel:n,z:i,idx:e.componentIndex,type:t,key:r})}),c&&(mn(a=u.concat(h),function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel}),O(a,function(t){var e=o.getComponent(t.type,t.idx),n=t.zlevel,t=t.key;null!=s&&(n=Math.max(s,n)),t?(n===s&&t!==l&&n++,l=t):l&&(n===s&&n++,l=""),s=n,e.setZLevel(n)})),t0(t,e,n,i,r),O(t._chartsViews,function(t){t.__alive=!1}),e0(t,e,n,i,r),O(t._chartsViews,function(t){t.__alive||t.remove(e,n)})},t0=function(t,n,i,r,e,o){O(o||t._componentsViews,function(t){var e=t.__model;c0(0,t),t.render(e,n,i,r),h0(e,t),p0(e,t)})},e0=function(r,t,e,o,n,a){var i,s,l,u,h=r._scheduler,c=(n=L(n||{},{updatedSeries:t.getSeries()}),Dm.trigger("series:beforeupdate",t,e,n),!1);t.eachSeries(function(t){var e,n=r._chartsMap[t.__viewId],i=(n.__alive=!0,n.renderTask);h.updatePayload(i,o),c0(0,n),a&&a.get(t.uid)&&i.dirty(),i.perform(h.getPerformArgs(i))&&(c=!0),n.group.silent=!!t.get("silent"),i=n,e=t.get("blendMode")||null,i.eachRendered(function(t){t.isGroup||(t.style.blend=e)}),Ol(t)}),h.unfinished=c||h.unfinished,Dm.trigger("series:layoutlabels",t,e,n),Dm.trigger("series:transition",t,e,n),t.eachSeries(function(t){var e=r._chartsMap[t.__viewId];h0(t,e),p0(t,e)}),s=t,l=(i=r)._zr.storage,u=0,l.traverse(function(t){t.isGroup||u++}),u>s.get("hoverLayerThreshold")&&!b.node&&!b.worker&&s.eachSeries(function(t){t.preventUsingHoverLayer||(t=i._chartsMap[t.__viewId]).__alive&&t.eachRendered(function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)})}),Dm.trigger("series:afterupdate",t,e,n)},r0=function(t){t[Om]=!0,t.getZr().wakeUp()},o0=function(t){t[Om]&&(t.getZr().storage.traverse(function(t){Ah(t)||u0(t)}),t[Om]=!1)},n0=function(n){return u(t,e=xd),t.prototype.getCoordinateSystems=function(){return n._coordSysMgr.getCoordinateSystems()},t.prototype.getComponentByElement=function(t){for(;t;){var e=t.__ecComponentInfo;if(null!=e)return n._model.getComponent(e.mainType,e.index);t=t.parent}},t.prototype.enterEmphasis=function(t,e){Sl(t,e),r0(n)},t.prototype.leaveEmphasis=function(t,e){Ml(t,e),r0(n)},t.prototype.enterBlur=function(t){yl(t,cl),r0(n)},t.prototype.leaveBlur=function(t){Tl(t),r0(n)},t.prototype.enterSelect=function(t){Cl(t),r0(n)},t.prototype.leaveSelect=function(t){Il(t),r0(n)},t.prototype.getModel=function(){return n.getModel()},t.prototype.getViewOfComponentModel=function(t){return n.getViewOfComponentModel(t)},t.prototype.getViewOfSeriesModel=function(t){return n.getViewOfSeriesModel(t)},new t(n);function t(){return null!==e&&e.apply(this,arguments)||this}var e},void(i0=function(i){function r(t,e){for(var n=0;n<t.length;n++)t[n][Nm]=e}O(g0,function(t,e){i._messageCenter.on(e,function(t){var e,n;!b0[i.group]||0===i[Nm]||t&&t.escapeConnect||(e=i.makeActionFromEvent(t),n=[],O(w0,function(t){t!==i&&t.group===i.group&&n.push(t)}),r(n,0),O(n,function(t){1!==t[Nm]&&t.dispatchAction(e)}),r(n,2))})})})),c);function c(t,e,n){var i=a0.call(this,new Oy)||this,t=(i._chartsViews=[],i._chartsMap={},i._componentsViews=[],i._componentsMap={},i._pendingActions=[],n=n||{},V(e)&&(e=_0[e]),i._dom=t,n.ssr&&$r(function(t){var e,t=D(t),n=t.dataIndex;if(null!=n)return(e=E()).set("series_index",t.seriesIndex),e.set("data_index",n),t.ssrType&&e.set("ssr_type",t.ssrType),e}),i._zr=jr(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:N(n.useDirtyRect,!1),useCoarsePointer:N(n.useCoarsePointer,"auto"),pointerSize:n.pointerSize})),n=(i._ssr=n.ssr,i._throttledZrFlush=ay(ct(t.flush,t),17),(e=y(e))&&Yd(e,!0),i._theme=e,i._locale=V(e=n.locale||Uc)?(n=Gc[e.toUpperCase()]||{},e===Fc||e===Vc?y(n):d(y(n),y(Gc[Hc]),!1)):d(y(e),y(Gc[Hc]),!1),i._coordSysMgr=new Sd,i._api=n0(i));function r(t,e){return t.__prio-e.__prio}return mn(v0,r),mn(y0,r),i._scheduler=new yy(i,n,y0,v0),i._messageCenter=new Vm,i._initEvents(),i.resize=ct(i.resize,i),t.animation.on("frame",i._onframe,i),$m(t,i),Qm(t,i),kt(i),i}function l0(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function u0(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),t.hoverState===Js&&t.states.emphasis?e.push("emphasis"):t.hoverState===Qs&&t.states.blur&&e.push("blur"),t.useStates(e)}function h0(t,e){var n,i;t.preventAutoZ||(n=t.get("z")||0,i=t.get("zlevel")||0,e.eachRendered(function(t){return function t(e,n,i,r){var o=e.getTextContent();var a=e.getTextGuideLine();var s=e.isGroup;if(s)for(var l=e.childrenRef(),u=0;u<l.length;u++)r=Math.max(t(l[u],n,i,r),r);else e.z=n,e.zlevel=i,r=Math.max(e.z2,r);o&&(o.z=n,o.zlevel=i,isFinite(r))&&(o.z2=r+2);a&&(s=e.textGuideLineConfig,a.z=n,a.zlevel=i,isFinite(r))&&(a.z2=r+(s&&s.showAbove?1:-1));return r}(t,n,i,-1/0),!0}))}function c0(t,e){e.eachRendered(function(t){var e,n;Ah(t)||(e=t.getTextContent(),n=t.getTextGuideLine(),t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null))})}function p0(t,e){var n=t.getModel("stateAnimation"),r=t.isAnimationEnabled(),t=n.get("duration"),o=0<t?{duration:t,delay:n.get("delay"),easing:n.get("easing")}:null;e.eachRendered(function(t){var e,n,i;t.states&&t.states.emphasis&&(Ah(t)||(t instanceof j&&((i=js(n=t)).normalFill=n.style.fill,i.normalStroke=n.style.stroke,n=n.states.select||{},i.selectFill=n.style&&n.style.fill||null,i.selectStroke=n.style&&n.style.stroke||null),t.__dirty&&(i=t.prevStates)&&t.useStates(i),r&&(t.stateTransition=o,n=t.getTextContent(),e=t.getTextGuideLine(),n&&(n.stateTransition=o),e)&&(e.stateTransition=o),t.__dirty&&u0(t)))})}var Yy=s0.prototype,d0=(Yy.on=Em("on"),Yy.off=Em("off"),Yy.one=function(i,r,t){var o=this;this.on.call(this,i,function t(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];r&&r.apply&&r.apply(this,e),o.off(i,t)},t)},["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"]);var f0={},g0={},y0=[],m0=[],v0=[],_0={},x0={},w0={},b0={},S0=+new Date,M0=+new Date,T0="_echarts_instance_";function C0(t){b0[t]=!1}Wy=C0;function I0(t){return w0[e=T0,(t=t).getAttribute?t.getAttribute(e):t[e]];var e}function k0(t,e){_0[t]=e}function D0(t){I(m0,t)<0&&m0.push(t)}function A0(t,e){F0(y0,t,e,2e3)}function P0(t){O0("afterinit",t)}function L0(t){O0("afterupdate",t)}function O0(t,e){Dm.on(t,e)}function R0(t,e,n){k(e)&&(n=e,e="");var i=R(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,g0[e]||(Tt(Rm.test(i)&&Rm.test(e)),f0[i]||(f0[i]={action:n,actionInfo:t}),g0[e]=i)}function N0(t,e){Sd.register(t,e)}function E0(t,e){F0(v0,t,e,1e3,"layout")}function z0(t,e){F0(v0,t,e,3e3,"visual")}var B0=[];function F0(t,e,n,i,r){(k(e)||R(e))&&(n=e,e=i),0<=I(B0,n)||(B0.push(n),(i=yy.wrapStageHandler(n,r)).__prio=e,i.__raw=n,t.push(i))}function V0(t,e){x0[t]=e}function H0(t,e,n){var i=Am.registerMap;i&&i(t,e,n)}function G0(t){var e=(t=y(t)).type,n=(e||f(""),e.split(":")),i=(2!==n.length&&f(""),!1);"echarts"===n[0]&&(e=n[1],i=!0),t.__isBuiltIn=i,Jf.set(e,t)}z0(2e3,Qo),z0(4500,jh),z0(4500,$c),z0(2e3,Dc),z0(4500,Tc),z0(7e3,function(e,i){e.eachRawSeries(function(t){var n;!e.isSeriesFiltered(t)&&((n=t.getData()).hasItemVisual()&&n.each(function(t){var e=n.getItemVisual(t,"decal");e&&(n.ensureUniqueItemVisual(t,"style").decal=Im(e,i))}),t=n.getVisual("decal"))&&(n.getVisual("style").decal=Im(t,i))})}),D0(Yd),A0(900,function(t){var i=E();t.eachSeries(function(t){var e,n=t.get("stack");n&&(n=i.get(n)||i.set(n,[]),(t={stackResultDimension:(e=t.getData()).getCalculationInfo("stackResultDimension"),stackedOverDimension:e.getCalculationInfo("stackedOverDimension"),stackedDimension:e.getCalculationInfo("stackedDimension"),stackedByDimension:e.getCalculationInfo("stackedByDimension"),isStackedByIndex:e.getCalculationInfo("isStackedByIndex"),data:e,seriesModel:t}).stackedDimension)&&(t.isStackedByIndex||t.stackedByDimension)&&(n.length&&e.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(t))}),i.each(qd)}),V0("default",function(i,r){z(r=r||{},{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var o,t=new Gr,a=new As({style:{fill:r.maskColor},zlevel:r.zlevel,z:1e4}),s=(t.add(a),new Ns({style:{text:r.text,fill:r.textColor,fontSize:r.fontSize,fontWeight:r.fontWeight,fontStyle:r.fontStyle,fontFamily:r.fontFamily},zlevel:r.zlevel,z:10001})),l=new As({style:{fill:"none"},textContent:s,textConfig:{position:"right",distance:10},zlevel:r.zlevel,z:10001});return t.add(l),r.showSpinner&&((o=new oh({shape:{startAngle:-gy/2,endAngle:-gy/2+.1,r:r.spinnerRadius},style:{stroke:r.color,lineCap:"round",lineWidth:r.lineWidth},zlevel:r.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*gy/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*gy/2}).delay(300).start("circularInOut"),t.add(o)),t.resize=function(){var t=s.getBoundingRect().width,e=r.showSpinner?r.spinnerRadius:0,t=(i.getWidth()-2*e-(r.showSpinner&&t?10:0)-t)/2-(r.showSpinner&&t?0:5+t/2)+(r.showSpinner?0:t/2)+(t?0:e),n=i.getHeight()/2;r.showSpinner&&o.setShape({cx:t,cy:n}),l.setShape({x:t-e,y:n-e,width:2*e,height:2*e}),a.setShape({x:0,y:0,width:i.getWidth(),height:i.getHeight()})},t.resize(),t}),R0({type:nl,event:nl,update:nl},Ft),R0({type:il,event:il,update:il},Ft),R0({type:rl,event:rl,update:rl},Ft),R0({type:ol,event:ol,update:ol},Ft),R0({type:al,event:al,update:al},Ft),k0("light",Pc),k0("dark",Ic);function W0(t){return null==t?0:t.length||1}function U0(t){return t}Y0.prototype.add=function(t){return this._add=t,this},Y0.prototype.update=function(t){return this._update=t,this},Y0.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},Y0.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},Y0.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},Y0.prototype.remove=function(t){return this._remove=t,this},Y0.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},Y0.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a,s=i[o],l=n[s],u=W0(l);1<u?(a=l.shift(),1===l.length&&(n[s]=l[0]),this._update&&this._update(a,o)):1===u?(n[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},Y0.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=W0(l),c=W0(u);if(1<h&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&1<c)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(1<h&&1<c)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(1<h)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},Y0.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=W0(r);if(1<o)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},Y0.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a,s,l="_ec_"+this[i](t[o],o);r||(n[o]=l),e&&(0===(s=W0(a=e[l]))?(e[l]=o,r&&n.push(l)):1===s?e[l]=[a,o]:a.push(o))}};var X0=Y0;function Y0(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||U0,this._newKeyGetter=i||U0,this.context=r,this._diffModeMultiple="multiple"===o}Z0.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},Z0.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames};var q0=Z0;function Z0(t,e){this._encode=t,this._schema=e}function j0(o,t){var e={},a=e.encode={},s=E(),l=[],u=[],h={},i=(O(o.dimensions,function(t){var e,n,i=o.getDimensionInfo(t),r=i.coordDim;r&&(e=i.coordDimIndex,K0(a,r)[e]=t,i.isExtraCoord||(s.set(r,1),"ordinal"!==(n=i.type)&&"time"!==n&&(l[0]=t),K0(h,r)[e]=o.getDimensionIndex(i.name)),i.defaultTooltip)&&u.push(t),Yp.each(function(t,e){var n=K0(a,e),e=i.otherDims[e];null!=e&&!1!==e&&(n[e]=i.name)})}),[]),r={},n=(s.each(function(t,e){var n=a[e];r[e]=n[0],i=i.concat(n)}),e.dataDimsOnCoord=i,e.dataDimIndicesOnCoord=B(i,function(t){return o.getDimensionInfo(t).storeDimIndex}),e.encodeFirstDimNotExtra=r,a.label),n=(n&&n.length&&(l=n.slice()),a.tooltip);return n&&n.length?u=n.slice():u.length||(u=l.slice()),a.defaultedLabel=l,a.defaultedTooltip=u,e.userOutput=new q0(h,t),e}function K0(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}var $0=function(t){this.otherDims={},null!=t&&L(this,t)},Q0=Lo(),J0={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},tv=(ev.prototype.isDimensionOmitted=function(){return this._dimOmitted},ev.prototype._updateDimOmitted=function(t){(this._dimOmitted=t)&&!this._dimNameMap&&(this._dimNameMap=rv(this.source))},ev.prototype.getSourceDimensionIndex=function(t){return N(this._dimNameMap.get(t),-1)},ev.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},ev.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=rf(this.source),n=!(30<t),i="",r=[],o=0,a=0;o<t;o++){var s,l=void 0,u=void 0,h=void 0,c=this.dimensions[a];c&&c.storeDimIndex===o?(l=e?c.name:null,u=c.type,h=c.ordinalMeta,a++):(s=this.getSourceDimension(o))&&(l=e?s.name:null,u=s.type),r.push({property:l,type:u,ordinalMeta:h}),!e||null==l||c&&c.isCalculationCoord||(i+=n?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),i=i+"$"+(J0[u]||"f"),h&&(i+=h.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},ev.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];r&&r.storeDimIndex===e?(r.isCalculationCoord||(i=r.name),n++):(r=this.getSourceDimension(e))&&(i=r.name),t.push(i)}return t},ev.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},ev);function ev(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}function nv(t){return t instanceof tv}function iv(t){for(var e=E(),n=0;n<(t||[]).length;n++){var i=t[n],i=R(i)?i.name:i;null!=i&&null==e.get(i)&&e.set(i,n)}return e}function rv(t){var e=Q0(t);return e.dimNameMap||(e.dimNameMap=iv(t.dimensionsDefine))}var ov,av,sv,lv,uv,hv,cv,pv=R,dv=B,fv="undefined"==typeof Int32Array?Array:Int32Array,gv=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],yv=["_approximateExtent"],mv=(p.prototype.getDimension=function(t){var e;return null==(e=this._recognizeDimIndex(t))?t:(e=t,this._dimOmitted?null!=(t=this._dimIdxToName.get(e))?t:(t=this._schema.getSourceDimension(e))?t.name:void 0:this.dimensions[e])},p.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);return null!=e?e:null==t?-1:(e=this._getDimInfo(t))?e.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},p.prototype._recognizeDimIndex=function(t){if(H(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},p.prototype._getStoreDimIndex=function(t){return this.getDimensionIndex(t)},p.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},p.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},p.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},p.prototype.mapDimension=function(t,e){var n=this._dimSummary;return null==e?n.encodeFirstDimNotExtra[t]:(n=n.encode[t])?n[e]:null},p.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},p.prototype.getStore=function(){return this._store},p.prototype.initData=function(t,e,n){var i,r,o=this;(i=t instanceof hg?t:i)||(r=this.dimensions,t=$d(t)||st(t)?new lf(t,r.length):t,i=new hg,r=dv(r,function(t){return{type:o._dimInfos[t].type,property:t}}),i.initData(t,r,n)),this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=j0(this,this._schema),this.userOutput=this._dimSummary.userOutput},p.prototype.appendData=function(t){t=this._store.appendData(t);this._doInit(t[0],t[1])},p.prototype.appendValues=function(t,e){var t=this._store.appendValues(t,e&&e.length),n=t.start,i=t.end,r=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var o=n;o<i;o++)this._nameList[o]=e[o-n],r&&cv(this,o)},p.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},p.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==$p&&!t.fillStorage},p.prototype._doInit=function(t,e){if(!(e<=t)){var n=this._store.getProvider(),i=(this._updateOrdinalMeta(),this._nameList),r=this._idList;if(n.getSource().sourceFormat===qp&&!n.pure)for(var o=[],a=t;a<e;a++){var s,l=n.getItem(a,o);this.hasItemOption||!R(s=l)||s instanceof Array||(this.hasItemOption=!0),l&&(s=l.name,null==i[a]&&null!=s&&(i[a]=Io(s,null)),l=l.id,null==r[a])&&null!=l&&(r[a]=Io(l,null))}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)cv(this,a);ov(this)}},p.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},p.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},p.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},p.prototype.setCalculationInfo=function(t,e){pv(t)?L(this._calculationInfo,t):this._calculationInfo[t]=e},p.prototype.getName=function(t){var t=this.getRawIndex(t),e=this._nameList[t];return e=null==(e=null==e&&null!=this._nameDimIdx?sv(this,this._nameDimIdx,t):e)?"":e},p.prototype._getCategory=function(t,e){e=this._store.get(t,e),t=this._store.getOrdinalMeta(t);return t?t.categories[e]:e},p.prototype.getId=function(t){return av(this,this.getRawIndex(t))},p.prototype.count=function(){return this._store.count()},p.prototype.get=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.get(t.storeDimIndex,e)},p.prototype.getByRawIndex=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.getByRawIndex(t.storeDimIndex,e)},p.prototype.getIndices=function(){return this._store.getIndices()},p.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},p.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},p.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},p.prototype.getValues=function(t,e){var n=this,i=this._store;return F(t)?i.getValues(dv(t,function(t){return n._getStoreDimIndex(t)}),e):i.getValues(t)},p.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},p.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},p.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},p.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},p.prototype.rawIndexOf=function(t,e){t=t&&this._invertedIndicesMap[t],t=t&&t[e];return null==t||isNaN(t)?-1:t},p.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},p.prototype.each=function(t,e,n){k(t)&&(n=e,e=t,t=[]);n=n||this,t=dv(lv(t),this._getStoreDimIndex,this);this._store.each(t,n?ct(e,n):e)},p.prototype.filterSelf=function(t,e,n){k(t)&&(n=e,e=t,t=[]);n=n||this,t=dv(lv(t),this._getStoreDimIndex,this);return this._store=this._store.filter(t,n?ct(e,n):e),this},p.prototype.selectRange=function(n){var i=this,r={};return O(ht(n),function(t){var e=i._getStoreDimIndex(t);r[e]=n[t]}),this._store=this._store.selectRange(r),this},p.prototype.mapArray=function(t,e,n){k(t)&&(n=e,e=t,t=[]);var i=[];return this.each(t,function(){i.push(e&&e.apply(this,arguments))},n=n||this),i},p.prototype.map=function(t,e,n,i){n=n||i||this,i=dv(lv(t),this._getStoreDimIndex,this),t=hv(this);return t._store=this._store.map(i,n?ct(e,n):e),t},p.prototype.modify=function(t,e,n,i){n=n||i||this,i=dv(lv(t),this._getStoreDimIndex,this);this._store.modify(i,n?ct(e,n):e)},p.prototype.downSample=function(t,e,n,i){var r=hv(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},p.prototype.minmaxDownSample=function(t,e){var n=hv(this);return n._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),n},p.prototype.lttbDownSample=function(t,e){var n=hv(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},p.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},p.prototype.getItemModel=function(t){var e=this.hostModel,t=this.getRawDataItem(t);return new Rc(t,e,e&&e.ecModel)},p.prototype.diff=function(e){var n=this;return new X0(e?e.getStore().getIndices():[],this.getStore().getIndices(),function(t){return av(e,t)},function(t){return av(n,t)})},p.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},p.prototype.setVisual=function(t,e){this._visual=this._visual||{},pv(t)?L(this._visual,t):this._visual[t]=e},p.prototype.getItemVisual=function(t,e){t=this._itemVisuals[t],t=t&&t[e];return null==t?this.getVisual(e):t},p.prototype.hasItemVisual=function(){return 0<this._itemVisuals.length},p.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t],n=(i=i||(n[t]={}))[e];return null==n&&(F(n=this.getVisual(e))?n=n.slice():pv(n)&&(n=L({},n)),i[e]=n),n},p.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,pv(e)?L(i,e):i[e]=n},p.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},p.prototype.setLayout=function(t,e){pv(t)?L(this._layout,t):this._layout[t]=e},p.prototype.getLayout=function(t){return this._layout[t]},p.prototype.getItemLayout=function(t){return this._itemLayouts[t]},p.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?L(this._itemLayouts[t]||{},e):e},p.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},p.prototype.setItemGraphicEl=function(t,e){var n,i,r,o,a=this.hostModel&&this.hostModel.seriesIndex;n=a,i=this.dataType,r=t,(a=e)&&((o=D(a)).dataIndex=r,o.dataType=i,o.seriesIndex=n,o.ssrType="chart","group"===a.type)&&a.traverse(function(t){t=D(t);t.seriesIndex=n,t.dataIndex=r,t.dataType=i,t.ssrType="chart"}),this._graphicEls[t]=e},p.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},p.prototype.eachItemGraphicEl=function(n,i){O(this._graphicEls,function(t,e){t&&n&&n.call(i,t,e)})},p.prototype.cloneShallow=function(t){return t=t||new p(this._schema||dv(this.dimensions,this._getDimInfo,this),this.hostModel),uv(t,this),t._store=this._store,t},p.prototype.wrapMethod=function(t,e){var n=this[t];k(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(St(arguments)))})},p.internalField=(ov=function(a){var s=a._invertedIndicesMap;O(s,function(t,e){var n=a._dimInfos[e],i=n.ordinalMeta,r=a._store;if(i){t=s[e]=new fv(i.categories.length);for(var o=0;o<t.length;o++)t[o]=-1;for(o=0;o<r.count();o++)t[r.get(n.storeDimIndex,o)]=o}})},sv=function(t,e,n){return Io(t._getCategory(e,n),null)},av=function(t,e){var n=t._idList[e];return n=null==(n=null==n&&null!=t._idDimIdx?sv(t,t._idDimIdx,e):n)?"e\0\0"+e:n},lv=function(t){return t=F(t)?t:null!=t?[t]:[]},hv=function(t){var e=new p(t._schema||dv(t.dimensions,t._getDimInfo,t),t.hostModel);return uv(e,t),e},uv=function(e,n){O(gv.concat(n.__wrappedMethods||[]),function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e.__wrappedMethods=n.__wrappedMethods,O(yv,function(t){e[t]=y(n[t])}),e._calculationInfo=L({},n._calculationInfo)},void(cv=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];null==a&&null!=r&&(n[e]=a=sv(t,r,e)),null==s&&null!=o&&(i[e]=s=sv(t,o,e)),null==s&&null!=a&&(s=a,1<(r=(n=t._nameRepeatCount)[a]=(n[a]||0)+1)&&(s+="__ec__"+r),i[e]=s)})),p);function p(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"];for(var n,i,r=!(this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"]),o=(nv(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(r=!0,n=t),n=n||["x","y"],{}),a=[],s={},l=!1,u={},h=0;h<n.length;h++){var c=n[h],c=V(c)?new $0({name:c}):c instanceof $0?c:new $0(c),p=c.name,d=(c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0),c.otherDims=c.otherDims||{});a.push(p),null!=u[p]&&(l=!0),(o[p]=c).createInvertedIndices&&(s[p]=[]),0===d.itemName&&(this._nameDimIdx=h),0===d.itemId&&(this._idDimIdx=h),r&&(c.storeDimIndex=h)}this.dimensions=a,this._dimInfos=o,this._initGetDimensionInfo(l),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted&&(i=this._dimIdxToName=E(),O(a,function(t){i.set(o[t].storeDimIndex,t)}))}function vv(t,e){$d(t)||(t=Jd(t));for(var n,i,r=(e=e||{}).coordDimensions||[],o=e.dimensionsDefine||t.dimensionsDefine||[],a=E(),s=[],l=(u=t,n=r,p=e.dimensionsCount,i=Math.max(u.dimensionsDetectedCount||1,n.length,o.length,p||0),O(n,function(t){R(t)&&(t=t.dimsDef)&&(i=Math.max(i,t.length))}),i),u=e.canOmitUnusedDimensions&&30<l,h=o===t.dimensionsDefine,c=h?rv(t):iv(o),p=e.encodeDefine,d=E(p=!p&&e.encodeDefaulter?e.encodeDefaulter(t,l):p),f=new og(l),g=0;g<f.length;g++)f[g]=-1;function y(t){var e,n,i,r=f[t];return r<0?(e=R(e=o[t])?e:{name:e},n=new $0,null!=(i=e.name)&&null!=c.get(i)&&(n.name=n.displayName=i),null!=e.type&&(n.type=e.type),null!=e.displayName&&(n.displayName=e.displayName),f[t]=s.length,n.storeDimIndex=t,s.push(n),n):s[r]}if(!u)for(g=0;g<l;g++)y(g);d.each(function(t,n){var i,t=xo(t).slice();1===t.length&&!V(t[0])&&t[0]<0?d.set(n,!1):(i=d.set(n,[]),O(t,function(t,e){t=V(t)?c.get(t):t;null!=t&&t<l&&v(y(i[e]=t),n,e)}))});var m=0;function v(t,e,n){null!=Yp.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,a.set(e,!0))}O(r,function(t){V(t)?(o=t,r={}):(o=(r=t).name,t=r.ordinalMeta,r.ordinalMeta=null,(r=L({},r)).ordinalMeta=t,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null);var n,i,r,o,e=d.get(o);if(!1!==e){if(!(e=xo(e)).length)for(var a=0;a<(n&&n.length||1);a++){for(;m<l&&null!=y(m).coordDim;)m++;m<l&&e.push(m++)}O(e,function(t,e){t=y(t);h&&null!=r.type&&(t.type=r.type),v(z(t,r),o,e),null==t.name&&n&&(R(e=n[e])||(e={name:e}),t.name=t.displayName=e.name,t.defaultTooltip=e.defaultTooltip),i&&z(t.otherDims,i)})}});var _=e.generateCoord,x=null!=(w=e.generateCoordCount),w=_?w||1:0,b=_||"value";function S(t){null==t.name&&(t.name=t.coordDim)}if(u)O(s,function(t){S(t)}),s.sort(function(t,e){return t.storeDimIndex-e.storeDimIndex});else for(var M=0;M<l;M++){var T=y(M);null==T.coordDim&&(T.coordDim=function(t,e,n){if(n||e.hasKey(t)){for(var i=0;e.hasKey(t+i);)i++;t+=i}return e.set(t,!0),t}(b,a,x),T.coordDimIndex=0,(!_||w<=0)&&(T.isExtraCoord=!0),w--),S(T),null!=T.type||od(t,M)!==ed.Must&&(!T.isExtraCoord||null==T.otherDims.itemName&&null==T.otherDims.seriesName)||(T.type="ordinal")}for(var C=s,I=E(),k=0;k<C.length;k++){var D=C[k],A=D.name,P=I.get(A)||0;0<P&&(D.name=A+(P-1)),P++,I.set(A,P)}return new tv({source:t,dimensions:s,fullDimensionCount:l,dimensionOmitted:u})}var _v=function(t){this.coordSysDims=[],this.axisMap=E(),this.categoryAxisMap=E(),this.coordSysName=t};var xv={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",Eo).models[0],t=t.getReferringComponents("yAxis",Eo).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",t),wv(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),wv(t)&&(i.set("y",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){t=t.getReferringComponents("singleAxis",Eo).models[0];e.coordSysDims=["single"],n.set("single",t),wv(t)&&(i.set("single",t),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var t=t.getReferringComponents("polar",Eo).models[0],r=t.findAxisModel("radiusAxis"),t=t.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",r),n.set("angle",t),wv(r)&&(i.set("radius",r),e.firstCategoryDimIndex=0),wv(t)&&(i.set("angle",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,i,r,o){var a=t.ecModel,t=a.getComponent("parallel",t.get("parallelIndex")),s=i.coordSysDims=t.dimensions.slice();O(t.parallelAxisIndex,function(t,e){var t=a.getComponent("parallelAxis",t),n=s[e];r.set(n,t),wv(t)&&(o.set(n,t),null==i.firstCategoryDimIndex)&&(i.firstCategoryDimIndex=e)})}};function wv(t){return"category"===t.get("type")}function bv(t,e,n){var i,r,o,a,s,l,u,h,c,p=(n=n||{}).byIndex,d=n.stackedCoordDimension,f=(nv(e.schema)?(r=e.schema,i=r.dimensions,o=e.store):i=e,!(!t||!t.get("stack")));return O(i,function(t,e){V(t)&&(i[e]=t={name:t}),f&&!t.isExtraCoord&&(p||a||!t.ordinalMeta||(a=t),s||"ordinal"===t.type||"time"===t.type||d&&d!==t.coordDim||(s=t))}),!s||p||a||(p=!0),s&&(l="__\0ecstackresult_"+t.id,u="__\0ecstackedover_"+t.id,a&&(a.createInvertedIndices=!0),h=s.coordDim,n=s.type,c=0,O(i,function(t){t.coordDim===h&&c++}),e={name:l,coordDim:h,coordDimIndex:c,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},t={name:u,coordDim:u,coordDimIndex:c+1,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1},r?(o&&(e.storeDimIndex=o.ensureCalculationDimension(u,n),t.storeDimIndex=o.ensureCalculationDimension(l,n)),r.appendCalculationDimension(e),r.appendCalculationDimension(t)):(i.push(e),i.push(t))),{stackedDimension:s&&s.name,stackedByDimension:a&&a.name,isStackedByIndex:p,stackedOverDimension:u,stackResultDimension:l}}function Sv(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function Mv(t,e){return Sv(t,e)?t.getCalculationInfo("stackResultDimension"):e}function Tv(t,e,n){n=n||{};var i,r,o,a,s,l,u=e.getSourceManager(),h=!1,t=(t?(h=!0,i=Jd(t)):h=(i=u.getSource()).sourceFormat===qp,function(t){var e=t.get("coordinateSystem"),n=new _v(e);if(e=xv[e])return e(t,n,n.axisMap,n.categoryAxisMap),n}(e)),c=(r=t,c=(c=e).get("coordinateSystem"),c=Sd.get(c),p=(p=r&&r.coordSysDims?B(r.coordSysDims,function(t){var e={name:t},t=r.axisMap.get(t);return t&&(t=t.get("type"),e.type="category"===(t=t)?"ordinal":"time"===t?"time":"float"),e}):p)||c&&(c.getDimensionsInfo?c.getDimensionsInfo():c.dimensions.slice())||["x","y"]),p=n.useEncodeDefaulter,p=k(p)?p:p?pt(id,c,e):null,c={coordDimensions:c,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:p,canOmitUnusedDimensions:!h},p=vv(i,c),d=(c=p.dimensions,o=n.createInvertedIndices,(a=t)&&O(c,function(t,e){var n=t.coordDim,n=a.categoryAxisMap.get(n);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta(),o)&&(t.createInvertedIndices=!0),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(c[s].otherDims.itemName=0),s),n=h?null:u.getSharedDataStore(p),t=bv(e,{schema:p,store:n}),c=new mv(p,e),p=(c.setCalculationInfo(t),null==d||(u=i).sourceFormat!==qp||F(So(function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(u.data||[])))?null:function(t,e,n,i){return i===d?n:this.defaultDimValueGetter(t,e,n,i)});return c.hasItemOption=!1,c.initData(h?i:n,null,p),c}Iv.prototype.getSetting=function(t){return this._setting[t]},Iv.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},Iv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Iv.prototype.getExtent=function(){return this._extent.slice()},Iv.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},Iv.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},Iv.prototype.isBlank=function(){return this._isBlank},Iv.prototype.setBlank=function(t){this._isBlank=t};var Cv=Iv;function Iv(t){this._setting=t||{},this._extent=[1/0,-1/0]}jo(Cv);var kv=0,Dv=(Av.createByAxisModel=function(t){var t=t.option,e=t.data,e=e&&B(e,Pv);return new Av({categories:e,needCollect:!e,deduplication:!1!==t.dedplication})},Av.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},Av.prototype.parseAndCollect=function(t){var e,n,i=this._needCollect;return V(t)||i?(i&&!this._deduplication?(n=this.categories.length,this.categories[n]=t):null==(n=(e=this._getOrCreateMap()).get(t))&&(i?(n=this.categories.length,this.categories[n]=t,e.set(t,n)):n=NaN),n):t},Av.prototype._getOrCreateMap=function(){return this._map||(this._map=E(this.categories))},Av);function Av(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++kv}function Pv(t){return R(t)&&null!=t.value?t.value:t+""}function Lv(t){return"interval"===t.type||"log"===t.type}function Ov(t,e,n,i){var r={},o=t[1]-t[0],o=r.interval=po(o/e,!0),e=(null!=n&&o<n&&(o=r.interval=n),null!=i&&i<o&&(o=r.interval=i),r.intervalPrecision=Nv(o)),n=r.niceTickExtent=[no(Math.ceil(t[0]/o)*o,e),no(Math.floor(t[1]/o)*o,e)];return i=n,o=t,isFinite(i[0])||(i[0]=o[0]),isFinite(i[1])||(i[1]=o[1]),Ev(i,0,o),Ev(i,1,o),i[0]>i[1]&&(i[0]=i[1]),r}function Rv(t){var e=Math.pow(10,co(t)),t=t/e;return t?2===t?t=3:3===t?t=5:t*=2:t=1,no(t*e)}function Nv(t){return io(t)+2}function Ev(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function zv(t,e){return t>=e[0]&&t<=e[1]}function Bv(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function Fv(t,e){return t*(e[1]-e[0])+e[0]}u(Gv,Vv=Cv),Gv.prototype.parse=function(t){return null==t?NaN:V(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},Gv.prototype.contain=function(t){return zv(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},Gv.prototype.normalize=function(t){return Bv(t=this._getTickNumber(this.parse(t)),this._extent)},Gv.prototype.scale=function(t){return t=Math.round(Fv(t,this._extent)),this.getRawOrdinalNumber(t)},Gv.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},Gv.prototype.getMinorTicks=function(t){},Gv.prototype.setSortInfo=function(t){if(null==t)this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;else{for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];i[n[r]=s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}},Gv.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&0<=t&&t<e.length?e[t]:t},Gv.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&0<=t&&t<e.length?e[t]:t},Gv.prototype.getLabel=function(t){if(!this.isBlank())return t=this.getRawOrdinalNumber(t.value),null==(t=this._ordinalMeta.categories[t])?"":t+""},Gv.prototype.count=function(){return this._extent[1]-this._extent[0]+1},Gv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Gv.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},Gv.prototype.getOrdinalMeta=function(){return this._ordinalMeta},Gv.prototype.calcNiceTicks=function(){},Gv.prototype.calcNiceExtent=function(){},Gv.type="ordinal";var Vv,Hv=Gv;function Gv(t){var t=Vv.call(this,t)||this,e=(t.type="ordinal",t.getSetting("ordinalMeta"));return F(e=e||new Dv({}))&&(e=new Dv({categories:B(e,function(t){return R(t)?t.value:t})})),t._ordinalMeta=e,t._extent=t.getSetting("extent")||[0,e.categories.length-1],t}Cv.registerClass(Hv);var Wv,Uv=no,Xv=(u(Yv,Wv=Cv),Yv.prototype.parse=function(t){return t},Yv.prototype.contain=function(t){return zv(t,this._extent)},Yv.prototype.normalize=function(t){return Bv(t,this._extent)},Yv.prototype.scale=function(t){return Fv(t,this._extent)},Yv.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},Yv.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},Yv.prototype.getInterval=function(){return this._interval},Yv.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Nv(t)},Yv.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(e){n[0]<i[0]&&o.push(t?{value:Uv(i[0]-e,r)}:{value:n[0]});for(var a=i[0];a<=i[1]&&(o.push({value:a}),(a=Uv(a+e,r))!==o[o.length-1].value);)if(1e4<o.length)return[];var s=o.length?o[o.length-1].value:i[1];n[1]>s&&o.push(t?{value:Uv(s+e,r)}:{value:n[1]})}return o},Yv.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o.value-a.value)/t;s<t-1;){var h=Uv(a.value+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},Yv.prototype.getLabel=function(t,e){return null==t?"":(null==(e=e&&e.precision)?e=io(t.value)||0:"auto"===e&&(e=this._intervalPrecision),_p(Uv(t.value,e,!0)))},Yv.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];isFinite(r)&&(r<0&&i.reverse(),r=Ov(i,t,e,n),this._intervalPrecision=r.intervalPrecision,this._interval=r.interval,this._niceExtent=r.niceTickExtent)},Yv.prototype.calcNiceExtent=function(t){var e=this._extent,n=(e[0]===e[1]&&(0!==e[0]?(n=Math.abs(e[0]),t.fixMax||(e[1]+=n/2),e[0]-=n/2):e[1]=1),e[1]-e[0]),n=(isFinite(n)||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval),this._interval);t.fixMin||(e[0]=Uv(Math.floor(e[0]/n)*n)),t.fixMax||(e[1]=Uv(Math.ceil(e[1]/n)*n))},Yv.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},Yv.type="interval",Yv);function Yv(){var t=null!==Wv&&Wv.apply(this,arguments)||this;return t.type="interval",t._interval=0,t._intervalPrecision=2,t}Cv.registerClass(Xv);var qv="undefined"!=typeof Float32Array,Zv=qv?Float32Array:Array;function jv(t){return F(t)?qv?new Float32Array(t):t:new Zv(t)}var Kv="__ec_stack_";function $v(t){return t.get("stack")||Kv+t.seriesIndex}function Qv(t){return t.dim+t.index}function Jv(t,e){var n=[];return e.eachSeriesByType(t,function(t){i_(t)&&n.push(t)}),n}function t_(t){var a,d,l=function(t){var e,l={},n=(O(t,function(t){var e=t.coordinateSystem.getBaseAxis();if("time"===e.type||"value"===e.type)for(var t=t.getData(),n=e.dim+"_"+e.index,i=t.getDimensionIndex(t.mapDimension(e.dim)),r=t.getStore(),o=0,a=r.count();o<a;++o){var s=r.get(i,o);l[n]?l[n].push(s):l[n]=[s]}}),{});for(e in l)if(l.hasOwnProperty(e)){var i=l[e];if(i){i.sort(function(t,e){return t-e});for(var r=null,o=1;o<i.length;++o){var a=i[o]-i[o-1];0<a&&(r=null===r?a:Math.min(r,a))}n[e]=r}}return n}(t),u=[];return O(t,function(t){var e,n=t.coordinateSystem.getBaseAxis(),i=n.getExtent(),r=(e="category"===n.type?n.getBandWidth():"value"===n.type||"time"===n.type?(e=n.dim+"_"+n.index,e=l[e],r=Math.abs(i[1]-i[0]),o=n.scale.getExtent(),o=Math.abs(o[1]-o[0]),e?r/o*e:r):(o=t.getData(),Math.abs(i[1]-i[0])/o.count()),eo(t.get("barWidth"),e)),i=eo(t.get("barMaxWidth"),e),o=eo(t.get("barMinWidth")||(r_(t)?.5:1),e),a=t.get("barGap"),s=t.get("barCategoryGap");u.push({bandWidth:e,barWidth:r,barMaxWidth:i,barMinWidth:o,barGap:a,barCategoryGap:s,axisKey:Qv(n),stackId:$v(t)})}),a={},O(u,function(t,e){var n=t.axisKey,i=t.bandWidth,i=a[n]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},r=i.stacks,n=(a[n]=i,t.stackId),o=(r[n]||i.autoWidthCount++,r[n]=r[n]||{width:0,maxWidth:0},t.barWidth),o=(o&&!r[n].width&&(r[n].width=o,o=Math.min(i.remainedWidth,o),i.remainedWidth-=o),t.barMaxWidth),o=(o&&(r[n].maxWidth=o),t.barMinWidth),r=(o&&(r[n].minWidth=o),t.barGap),n=(null!=r&&(i.gap=r),t.barCategoryGap);null!=n&&(i.categoryGap=n)}),d={},O(a,function(t,n){d[n]={};var i,e=t.stacks,r=t.bandWidth,o=t.categoryGap,a=(null==o&&(a=ht(e).length,o=Math.max(35-4*a,15)+"%"),eo(o,r)),s=eo(t.gap,1),l=t.remainedWidth,u=t.autoWidthCount,h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),c=(O(e,function(t){var e,n=t.maxWidth,i=t.minWidth;t.width?(e=t.width,n&&(e=Math.min(e,n)),i&&(e=Math.max(e,i)),t.width=e,l-=e+s*e,u--):(e=h,n&&n<e&&(e=Math.min(n,l)),(e=i&&e<i?i:e)!==h&&(t.width=e,l-=e+s*e,u--))}),h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),0),p=(O(e,function(t,e){t.width||(t.width=h),c+=(i=t).width*(1+s)}),i&&(c-=i.width*s),-c/2);O(e,function(t,e){d[n][e]=d[n][e]||{bandWidth:r,offset:p,width:t.width},p+=t.width*(1+s)})}),d}function e_(t,e){var t=Jv(t,e),r=t_(t);O(t,function(t){var e=t.getData(),n=t.coordinateSystem.getBaseAxis(),t=$v(t),n=r[Qv(n)][t],t=n.offset,i=n.width;e.setLayout({bandWidth:n.bandWidth,offset:t,size:i})})}function n_(t){return{seriesType:t,plan:qg(),reset:function(t){var e,x,n,w,b,S,i,r,M,T,C,I,k,D,A,P;if(i_(t))return e=t.getData(),i=(x=t.coordinateSystem).getBaseAxis(),n=x.getOtherAxis(i),w=e.getDimensionIndex(e.mapDimension(n.dim)),b=e.getDimensionIndex(e.mapDimension(i.dim)),S=t.get("showBackground",!0),i=e.mapDimension(n.dim),r=e.getCalculationInfo("stackResultDimension"),M=Sv(e,i)&&!!e.getCalculationInfo("stackedOnSeries"),T=n.isHorizontal(),C=function(t){var e=t.model.get("startValue");e=e||0;return t.toGlobalCoord(t.dataToCoord("log"!==t.type||0<e?e:1))}(n),I=r_(t),k=t.get("barMinHeight")||0,D=r&&e.getDimensionIndex(r),A=e.getLayout("size"),P=e.getLayout("offset"),{progress:function(t,e){for(var n,i=t.count,r=I&&jv(3*i),o=I&&S&&jv(3*i),a=I&&jv(i),s=x.master.getRect(),l=T?s.width:s.height,u=e.getStore(),h=0;null!=(n=t.next());){var c,p=u.get(M?D:w,n),d=u.get(b,n),f=C,g=void 0,y=(M&&(g=+p-u.get(w,n)),void 0),m=void 0,v=void 0,_=void 0;T?(c=x.dataToPoint([p,d]),y=f=M?x.dataToPoint([g,d])[0]:f,m=c[1]+P,v=c[0]-f,_=A,Math.abs(v)<k&&(v=(v<0?-1:1)*k)):(c=x.dataToPoint([d,p]),M&&(f=x.dataToPoint([d,g])[1]),y=c[0]+P,m=f,v=A,_=c[1]-f,Math.abs(_)<k&&(_=(_<=0?-1:1)*k)),I?(r[h]=y,r[h+1]=m,r[h+2]=T?v:_,o&&(o[h]=T?s.x:y,o[h+1]=T?m:s.y,o[h+2]=l),a[n]=n):e.setItemLayout(n,{x:y,y:m,width:v,height:_}),h+=3}I&&e.setLayout({largePoints:r,largeDataIndices:a,largeBackgroundPoints:o,valueAxisHorizontal:T})}}}}}function i_(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function r_(t){return t.pipelineContext&&t.pipelineContext.large}u(s_,o_=Xv),s_.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return ip(t.value,Qc[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(np(this._minLevelUnit))]||Qc.second,e,this.getSetting("locale"))},s_.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC"),r=this.getSetting("locale"),o=null;if(V(n))o=n;else if(k(n))o=n(t.value,e,{level:t.level});else{var a=L({},Kc);if(0<t.level)for(var s=0;s<Jc.length;++s)a[Jc[s]]="{primary|"+a[Jc[s]]+"}";var l=n?!1===n.inherit?n:z(n,a):a,u=rp(t.value,i);if(l[u])o=l[u];else if(l.inherit){for(s=tp.indexOf(u)-1;0<=s;--s)if(l[u]){o=l[u];break}o=o||a.none}F(o)&&(e=null==t.level?0:0<=t.level?t.level:o.length+t.level,o=o[e=Math.min(e,o.length-1)])}return ip(new Date(t.value),o,i,r)},s_.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];return t&&(n.push({value:e[0],level:0}),t=this.getSetting("useUTC"),t=function(t,b,S,M){var e=tp,n=0;function i(t,e,n){var i=[],r=!e.length;if(!function(t,e,n,i){function r(t){return op(c,t,i)===op(p,t,i)}function o(){return r("year")}function a(){return o()&&r("month")}function s(){return a()&&r("day")}function l(){return s()&&r("hour")}function u(){return l()&&r("minute")}function h(){return u()&&r("second")}var c=uo(e),p=uo(n);switch(t){case"year":return o();case"month":return a();case"day":return s();case"hour":return l();case"minute":return u();case"second":return h();case"millisecond":return h()&&r("millisecond")}}(np(t),M[0],M[1],S)){r&&(e=[{value:function(t,e,n){var i=new Date(t);switch(np(e)){case"year":case"month":i[dp(n)](0);case"day":i[fp(n)](1);case"hour":i[gp(n)](0);case"minute":i[yp(n)](0);case"second":i[mp(n)](0),i[vp(n)](0)}return i.getTime()}(new Date(M[0]),t,S)},{value:M[1]}]);for(var o,a,s=0;s<e.length-1;s++){var l=e[s].value,u=e[s+1].value;if(l!==u){var h=void 0,c=void 0,p=void 0;switch(t){case"year":h=Math.max(1,Math.round(b/jc/365)),c=ap(S),p=S?"setUTCFullYear":"setFullYear";break;case"half-year":case"quarter":case"month":a=b,h=6<(a/=30*jc)?6:3<a?3:2<a?2:1,c=sp(S),p=dp(S);break;case"week":case"half-week":case"day":a=b,h=16<(a/=jc)?16:7.5<a?7:3.5<a?4:1.5<a?2:1,c=lp(S),p=fp(S),0;break;case"half-day":case"quarter-day":case"hour":o=b,h=12<(o/=Zc)?12:6<o?6:3.5<o?4:2<o?2:1,c=up(S),p=gp(S);break;case"minute":h=u_(b,!0),c=hp(S),p=yp(S);break;case"second":h=u_(b,!1),c=cp(S),p=mp(S);break;case"millisecond":h=po(b,!0),c=pp(S),p=vp(S)}w=x=_=v=m=y=g=f=d=void 0;for(var d=h,f=l,g=u,y=c,m=p,v=i,_=new Date(f),x=f,w=_[y]();x<g&&x<=M[1];)v.push({value:x}),_[m](w+=d),x=_.getTime();v.push({value:x,notAdd:!0}),"year"===t&&1<n.length&&0===s&&n.unshift({value:n[0].value-h})}}for(s=0;s<i.length;s++)n.push(i[s])}}for(var r=[],o=[],a=0,s=0,l=0;l<e.length&&n++<1e4;++l){var u=np(e[l]);if(function(t){return t===np(t)}(e[l])){i(e[l],r[r.length-1]||[],o);var h=e[l+1]?np(e[l+1]):null;if(u!==h){if(o.length){s=a,o.sort(function(t,e){return t.value-e.value});for(var c=[],p=0;p<o.length;++p){var d=o[p].value;0!==p&&o[p-1].value===d||(c.push(o[p]),d>=M[0]&&d<=M[1]&&a++)}u=(M[1]-M[0])/b;if(1.5*u<a&&u/1.5<s)break;if(r.push(c),u<a||t===e[l])break}o=[]}}}for(var f=ut(B(r,function(t){return ut(t,function(t){return t.value>=M[0]&&t.value<=M[1]&&!t.notAdd})}),function(t){return 0<t.length}),g=[],y=f.length-1,l=0;l<f.length;++l)for(var m=f[l],v=0;v<m.length;++v)g.push({value:m[v].value,level:y-l});g.sort(function(t,e){return t.value-e.value});for(var _=[],l=0;l<g.length;++l)0!==l&&g[l].value===g[l-1].value||_.push(g[l]);return _}(this._minLevelUnit,this._approxInterval,t,e),(n=n.concat(t)).push({value:e[1],level:0})),n},s_.prototype.calcNiceExtent=function(t){var e,n=this._extent;n[0]===n[1]&&(n[0]-=jc,n[1]+=jc),n[1]===-1/0&&n[0]===1/0&&(e=new Date,n[1]=+new Date(e.getFullYear(),e.getMonth(),e.getDate()),n[0]=n[1]-jc),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},s_.prototype.calcNiceTicks=function(t,e,n){var i=this._extent,i=i[1]-i[0],i=(this._approxInterval=i/(t=t||10),null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n),l_.length),t=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=1+r:i=r}return n}(l_,this._approxInterval,0,i),i-1);this._interval=l_[t][1],this._minLevelUnit=l_[Math.max(t-1,0)][0]},s_.prototype.parse=function(t){return H(t)?t:+uo(t)},s_.prototype.contain=function(t){return zv(this.parse(t),this._extent)},s_.prototype.normalize=function(t){return Bv(this.parse(t),this._extent)},s_.prototype.scale=function(t){return Fv(t,this._extent)},s_.type="time";var o_,a_=s_;function s_(t){t=o_.call(this,t)||this;return t.type="time",t}var l_=[["second",Yc],["minute",qc],["hour",Zc],["quarter-day",6*Zc],["half-day",12*Zc],["day",1.2*jc],["half-week",3.5*jc],["week",7*jc],["month",31*jc],["quarter",95*jc],["half-year",Yo/2],["year",Yo]];function u_(t,e){return 30<(t/=e?qc:Yc)?30:20<t?20:15<t?15:10<t?10:5<t?5:2<t?2:1}Cv.registerClass(a_);var h_,c_=Cv.prototype,p_=Xv.prototype,d_=no,f_=Math.floor,g_=Math.ceil,y_=Math.pow,m_=Math.log,v_=(u(__,h_=Cv),__.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return B(p_.getTicks.call(this,t),function(t){var t=t.value,e=no(y_(this.base,t)),e=t===n[0]&&this._fixMin?x_(e,i[0]):e;return{value:t===n[1]&&this._fixMax?x_(e,i[1]):e}},this)},__.prototype.setExtent=function(t,e){var n=m_(this.base);t=m_(Math.max(0,t))/n,e=m_(Math.max(0,e))/n,p_.setExtent.call(this,t,e)},__.prototype.getExtent=function(){var t=this.base,e=c_.getExtent.call(this);e[0]=y_(t,e[0]),e[1]=y_(t,e[1]);t=this._originalScale.getExtent();return this._fixMin&&(e[0]=x_(e[0],t[0])),this._fixMax&&(e[1]=x_(e[1],t[1])),e},__.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=m_(t[0])/m_(e),t[1]=m_(t[1])/m_(e),c_.unionExtent.call(this,t)},__.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},__.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n==1/0||n<=0)){var i=ho(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&0<Math.abs(i);)i*=10;t=[no(g_(e[0]/i)*i),no(f_(e[1]/i)*i)];this._interval=i,this._niceExtent=t}},__.prototype.calcNiceExtent=function(t){p_.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},__.prototype.parse=function(t){return t},__.prototype.contain=function(t){return zv(t=m_(t)/m_(this.base),this._extent)},__.prototype.normalize=function(t){return Bv(t=m_(t)/m_(this.base),this._extent)},__.prototype.scale=function(t){return t=Fv(t,this._extent),y_(this.base,t)},__.type="log",__);function __(){var t=null!==h_&&h_.apply(this,arguments)||this;return t.type="log",t.base=10,t._originalScale=new Xv,t._interval=0,t}Hy=v_.prototype;function x_(t,e){return d_(t,io(e))}Hy.getMinorTicks=p_.getMinorTicks,Hy.getLabel=p_.getLabel,Cv.registerClass(v_);b_.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type,r=(this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero(),e.get("min",!0)),r=(null==r&&(r=e.get("startValue",!0)),this._modelMinRaw=r),r=(k(r)?this._modelMinNum=T_(t,r({min:n[0],max:n[1]})):"dataMin"!==r&&(this._modelMinNum=T_(t,r)),this._modelMaxRaw=e.get("max",!0));k(r)?this._modelMaxNum=T_(t,r({min:n[0],max:n[1]})):"dataMax"!==r&&(this._modelMaxNum=T_(t,r)),i?this._axisDataLen=e.getCategories().length:"boolean"==typeof(t=F(n=e.get("boundaryGap"))?n:[n||0,n||0])[0]||"boolean"==typeof t[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[kr(t[0],1),kr(t[1],1)]},b_.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s,e=(null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),null!=a&&isFinite(a)||(a=NaN),null!=s&&isFinite(s)||(s=NaN),xt(a)||xt(s)||t&&!i),n=(this._needCrossZero&&(a=0<a&&0<s&&!l?0:a)<0&&s<0&&!u&&(s=0),this._determinedMin),r=this._determinedMax;return null!=n&&(a=n,l=!0),null!=r&&(s=r,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:e}},b_.prototype.modifyDataMinMax=function(t,e){this[M_[t]]=e},b_.prototype.setDeterminedMinMax=function(t,e){this[S_[t]]=e},b_.prototype.freeze=function(){this.frozen=!0};var w_=b_;function b_(t,e,n){this._prepareParams(t,e,n)}var S_={min:"_determinedMin",max:"_determinedMax"},M_={min:"_dataMin",max:"_dataMax"};function T_(t,e){return null==e?null:xt(e)?NaN:t.parse(e)}function C_(t,e){var n,i,r,o,a,s=t.type,l=(l=e,u=(h=t).getExtent(),(c=h.rawExtentInfo)||(c=new w_(h,l,u),h.rawExtentInfo=c),c.calculate()),u=(t.setBlank(l.isBlank),l.min),h=l.max,c=e.ecModel;return c&&"time"===s&&(t=Jv("bar",c),n=!1,O(t,function(t){n=n||t.getBaseAxis()===e.axis}),n)&&(s=t_(t),c=u,t=h,s=s,a=(i=e).axis.getExtent(),a=Math.abs(a[1]-a[0]),void 0!==(s=function(t,e,n){if(t&&e)return null!=(t=t[Qv(e)])&&null!=n?t[$v(n)]:t}(s,i.axis))&&(r=1/0,O(s,function(t){r=Math.min(t.offset,r)}),o=-1/0,O(s,function(t){o=Math.max(t.offset+t.width,o)}),r=Math.abs(r),o=Math.abs(o),t+=o/(i=r+o)*(a=(s=t-c)/(1-(r+o)/a)-s),c-=r/i*a),u=(s={min:c,max:t}).min,h=s.max),{extent:[u,h],fixMin:l.minFixed,fixMax:l.maxFixed}}function I_(t,e){var n=C_(t,e),i=n.extent,r=e.get("splitNumber"),o=(t instanceof v_&&(t.base=e.get("logBase")),t.type),a=e.get("interval"),o="interval"===o||"time"===o;t.setExtent(i[0],i[1]),t.calcNiceExtent({splitNumber:r,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:o?e.get("minInterval"):null,maxInterval:o?e.get("maxInterval"):null}),null!=a&&t.setInterval&&t.setInterval(a)}function k_(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Hv({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new a_({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(Cv.getClass(e)||Xv)}}function D_(n){var i,e,r,t=n.getLabelModel().get("formatter"),o="category"===n.type?n.scale.getExtent()[0]:null;return"time"===n.scale.type?(r=t,function(t,e){return n.scale.getFormattedLabel(t,e,r)}):V(t)?(e=t,function(t){t=n.scale.getLabel(t);return e.replace("{value}",null!=t?t:"")}):k(t)?(i=t,function(t,e){return null!=o&&(e=t.value-o),i(A_(n,t),e,null!=t.level?{level:t.level}:null)}):function(t){return n.scale.getLabel(t)}}function A_(t,e){return"category"===t.type?t.scale.getLabel(e):e.value}function P_(t){var e=t.model,n=t.scale;if(e.get(["axisLabel","show"])&&!n.isBlank()){var i,r,o=n.getExtent(),a=n instanceof Hv?n.count():(i=n.getTicks()).length,s=t.getLabelModel(),l=D_(t),u=1;40<a&&(u=Math.ceil(a/40));for(var h,c,p,d=0;d<a;d+=u){var f=l(i?i[d]:{value:o[0]+d},d),f=s.getTextRect(f),g=(f=f,h=s.get("rotate")||0,c=p=g=c=void 0,h=h*Math.PI/180,c=f.width,g=f.height,p=c*Math.abs(Math.cos(h))+Math.abs(g*Math.sin(h)),c=c*Math.abs(Math.sin(h))+Math.abs(g*Math.cos(h)),new X(f.x,f.y,p,c));r?r.union(g):r=g}return r}}function L_(t){t=t.get("interval");return null==t?"auto":t}function O_(t){return"category"===t.type&&0===L_(t.getLabelModel())}N_.prototype.getNeedCrossZero=function(){return!this.option.scale},N_.prototype.getCoordSysModel=function(){};var R_=N_;function N_(){}var Xy=Object.freeze({__proto__:null,createDimensions:function(t,e){return vv(t,e).dimensions},createList:function(t){return Tv(null,t)},createScale:function(t,e){var n=e;return(e=k_(n=e instanceof Rc?n:new Rc(e))).setExtent(t[0],t[1]),I_(e,n),e},createSymbol:$y,createTextStyle:function(t,e){return dc(t,null,null,"normal"!==(e=e||{}).state)},dataStack:{isDimensionStacked:Sv,enableDataStack:bv,getStackedDimension:Mv},enableHoverEmphasis:Rl,getECData:D,getLayoutRect:Rp,mixinAxisModelCommonMethods:function(t){at(t,R_)}}),E_=[],z_={registerPreprocessor:D0,registerProcessor:A0,registerPostInit:P0,registerPostUpdate:L0,registerUpdateLifecycle:O0,registerAction:R0,registerCoordinateSystem:N0,registerLayout:E0,registerVisual:z0,registerTransform:G0,registerLoading:V0,registerMap:H0,registerImpl:function(t,e){Am[t]=e},PRIORITY:Uy,ComponentModel:g,ComponentView:Xg,SeriesModel:zg,ChartView:Kg,registerComponentModel:function(t){g.registerClass(t)},registerComponentView:function(t){Xg.registerClass(t)},registerSeriesModel:function(t){zg.registerClass(t)},registerChartView:function(t){Kg.registerClass(t)},registerSubTypeDefaulter:function(t,e){g.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){Kr(t,e)}};function B_(t){F(t)?O(t,function(t){B_(t)}):0<=I(E_,t)||(E_.push(t),(t=k(t)?{install:t}:t).install(z_))}var F_=1e-8;function V_(t,e){return Math.abs(t-e)<F_}function H_(t,e,n){var i=0,r=t[0];if(r){for(var o=1;o<t.length;o++){var a=t[o];i+=es(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return V_(r[0],s[0])&&V_(r[1],s[1])||(i+=es(r[0],r[1],s[0],s[1],e,n)),0!==i}}var G_=[];function W_(t,e){for(var n=0;n<t.length;n++)ee(t[n],t[n],e)}function U_(t,e,n,i){for(var r=0;r<t.length;r++){var o=t[r];(o=i?i.project(o):o)&&isFinite(o[0])&&isFinite(o[1])&&(ne(e,e,o),ie(n,n,o))}}X_.prototype.setCenter=function(t){this._center=t},X_.prototype.getCenter=function(){return this._center||(this._center=this.calcCenter())};Yy=X_;function X_(t){this.name=t}var Y_,q_,Z_=function(t,e){this.type="polygon",this.exterior=t,this.interiors=e},j_=function(t){this.type="linestring",this.points=t},K_=(u($_,Y_=Yy),$_.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,i=0;i<e.length;i++){var r=e[i],o=r.exterior,o=o&&o.length;n<o&&(t=r,n=o)}if(t){for(var a=t.exterior,s=0,l=0,u=0,h=a.length,c=a[h-1][0],p=a[h-1][1],d=0;d<h;d++){var f=a[d][0],g=a[d][1],y=c*g-f*p;s+=y,l+=(c+f)*y,u+=(p+g)*y,c=f,p=g}return s?[l/s/3,u/s/3,s]:[a[0][0]||0,a[0][1]||0]}var m=this.getBoundingRect();return[m.x+m.width/2,m.y+m.height/2]},$_.prototype.getBoundingRect=function(e){var n,i,t=this._rect;return t&&!e||(n=[1/0,1/0],i=[-1/0,-1/0],O(this.geometries,function(t){"polygon"===t.type?U_(t.exterior,n,i,e):O(t.points,function(t){U_(t,n,i,e)})}),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),t=new X(n[0],n[1],i[0]-n[0],i[1]-n[1]),e)||(this._rect=t),t},$_.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(e.contain(t[0],t[1]))t:for(var i=0,r=n.length;i<r;i++){var o=n[i];if("polygon"===o.type){var a=o.exterior,s=o.interiors;if(H_(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(H_(s[l],t[0],t[1]))continue t;return!0}}}return!1},$_.prototype.transformTo=function(t,e,n,i){for(var r=this.getBoundingRect(),o=r.width/r.height,o=(n?i=i||n/o:n=o*i,new X(t,e,n,i)),a=r.calculateTransform(o),s=this.geometries,l=0;l<s.length;l++){var u=s[l];"polygon"===u.type?(W_(u.exterior,a),O(u.interiors,function(t){W_(t,a)})):O(u.points,function(t){W_(t,a)})}(r=this._rect).copy(o),this._center=[r.x+r.width/2,r.y+r.height/2]},$_.prototype.cloneShallow=function(t){t=new $_(t=null==t?this.name:t,this.geometries,this._center);return t._rect=this._rect,t.transformTo=null,t},$_);function $_(t,e,n){t=Y_.call(this,t)||this;return t.type="geoJSON",t.geometries=e,t._center=n&&[n[0],n[1]],t}function Q_(t,e){t=q_.call(this,t)||this;return t.type="geoSVG",t._elOnlyForCalculate=e,t}function J_(t,e,n){for(var i=0;i<t.length;i++)t[i]=t1(t[i],e[i],n)}function t1(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=(s=t.charCodeAt(a)-64)>>1^-(1&s),l=(l=t.charCodeAt(a+1)-64)>>1^-(1&l);i.push([(r=s+=r)/n,(o=l+=o)/n])}return i}function e1(t,o){var e,n,r;return B(ut((t=(e=t).UTF8Encoding?(null==(r=(n=e).UTF8Scale)&&(r=1024),O(n.features,function(t){var e=t.geometry,n=e.encodeOffsets,i=e.coordinates;if(n)switch(e.type){case"LineString":e.coordinates=t1(i,n,r);break;case"Polygon":case"MultiLineString":J_(i,n,r);break;case"MultiPolygon":O(i,function(t,e){return J_(t,n[e],r)})}}),n.UTF8Encoding=!1,n):e).features,function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length}),function(t){var e=t.properties,n=t.geometry,i=[];switch(n.type){case"Polygon":var r=n.coordinates;i.push(new Z_(r[0],r.slice(1)));break;case"MultiPolygon":O(n.coordinates,function(t){t[0]&&i.push(new Z_(t[0],t.slice(1)))});break;case"LineString":i.push(new j_([n.coordinates]));break;case"MultiLineString":i.push(new j_(n.coordinates))}t=new K_(e[o||"name"],i,e.cp);return t.properties=e,t})}u(Q_,q_=Yy),Q_.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),e=[e.x+e.width/2,e.y+e.height/2],n=Oe(G_),i=t;i&&!i.isGeoSVGGraphicRoot;)Ne(n,i.getLocalTransform(),n),i=i.parent;return Fe(n,n),ee(e,e,n),e};var Qo=Object.freeze({__proto__:null,MAX_SAFE_INTEGER:9007199254740991,asc:function(t){return t.sort(function(t,e){return t-e}),t},getPercentWithPrecision:function(t,e,n){return t[e]&&function(t,e){var n=lt(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return[];var i=Math.pow(10,e),e=B(t,function(t){return(isNaN(t)?0:t)/n*i*100}),r=100*i,o=B(e,function(t){return Math.floor(t)}),a=lt(o,function(t,e){return t+e},0),s=B(e,function(t,e){return t-o[e]});for(;a<r;){for(var l=Number.NEGATIVE_INFINITY,u=null,h=0,c=s.length;h<c;++h)s[h]>l&&(l=s[h],u=h);++o[u],s[u]=0,++a}return B(o,function(t){return t/i})}(t,n)[e]||0},getPixelPrecision:oo,getPrecision:io,getPrecisionSafe:ro,isNumeric:go,isRadianAroundZero:so,linearMap:to,nice:po,numericToNumber:fo,parseDate:uo,quantile:function(t,e){var e=(t.length-1)*e+1,n=Math.floor(e),i=+t[n-1];return(e=e-n)?i+e*(t[n]-i):i},quantity:ho,quantityExponent:co,reformIntervals:function(t){t.sort(function(t,e){return function t(e,n,i){return e.interval[i]<n.interval[i]||e.interval[i]===n.interval[i]&&(e.close[i]-n.close[i]==(i?-1:1)||!i&&t(e,n,1))}(t,e,0)?-1:1});for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t},remRadian:ao,round:no}),jh=Object.freeze({__proto__:null,format:ip,parse:uo}),$c=Object.freeze({__proto__:null,Arc:oh,BezierCurve:eh,BoundingRect:X,Circle:hu,CompoundPath:lh,Ellipse:fu,Group:Gr,Image:ws,IncrementalDisplayable:n,Line:ju,LinearGradient:dh,Polygon:Vu,Polyline:Uu,RadialGradient:hh,Rect:As,Ring:Nu,Sector:Pu,Text:Ns,clipPointsByRect:tc,clipRectByRect:ec,createIcon:nc,extendPath:Vh,extendShape:Bh,getShapeClass:Gh,getTransform:Kh,initProps:Dh,makeImage:Uh,makePath:Wh,mergePath:Yh,registerShape:Hh,resizePath:qh,updateProps:kh}),Dc=Object.freeze({__proto__:null,addCommas:_p,capitalFirst:function(t){return t&&t.charAt(0).toUpperCase()+t.substr(1)},encodeHTML:_e,formatTime:function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=(e=uo(e))[(n=n?"getUTC":"get")+"FullYear"](),r=e[n+"Month"]()+1,o=e[n+"Date"](),a=e[n+"Hours"](),s=e[n+"Minutes"](),l=e[n+"Seconds"](),e=e[n+"Milliseconds"]();return t=t.replace("MM",ep(r,2)).replace("M",r).replace("yyyy",i).replace("yy",ep(i%100+"",2)).replace("dd",ep(o,2)).replace("d",o).replace("hh",ep(a,2)).replace("h",a).replace("mm",ep(s,2)).replace("m",s).replace("ss",ep(l,2)).replace("s",l).replace("SSS",ep(e,3))},formatTpl:Tp,getTextRect:function(t,e,n,i,r,o,a,s){return new Ns({style:{text:t,font:e,align:n,verticalAlign:i,padding:r,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()},getTooltipMarker:Cp,normalizeCssArray:wp,toCamelCase:xp,truncateText:function(t,e,n,i,r){var o={};return oa(o,t,e,n,i,r),o.text}}),Tc=Object.freeze({__proto__:null,bind:ct,clone:y,curry:pt,defaults:z,each:O,extend:L,filter:ut,indexOf:I,inherits:ot,isArray:F,isFunction:k,isObject:R,isString:V,map:B,merge:d,reduce:lt}),n1=Lo();function i1(e,t){t=B(t,function(t){return e.scale.parse(t)});return"time"===e.type&&0<t.length&&(t.sort(),t.unshift(t[0]),t.push(t[t.length-1])),t}function r1(n){var i,e,r,o,t,a,s=n.getLabelModel().get("customValues");return s?(i=D_(n),e=n.scale.getExtent(),{labels:B(ut(i1(n,s),function(t){return t>=e[0]&&t<=e[1]}),function(t){var e={value:t};return{formattedLabel:i(e),rawLabel:n.scale.getLabel(e),tickValue:t}})}):"category"===n.type?(t=(s=n).getLabelModel(),a=a1(s,t),!t.get("show")||s.scale.isBlank()?{labels:[],labelCategoryInterval:a.labelCategoryInterval}:a):(t=(r=n).scale.getTicks(),o=D_(r),{labels:B(t,function(t,e){return{level:t.level,formattedLabel:o(t,e),rawLabel:r.scale.getLabel(t),tickValue:t.value}})})}function o1(t,e){var n,i,r,o,a,s,l=t.getTickModel().get("customValues");return l?(n=t.scale.getExtent(),{ticks:ut(i1(t,l),function(t){return t>=n[0]&&t<=n[1]})}):"category"===t.type?(l=e,o=s1(e=t,"ticks"),a=L_(l),(s=l1(o,a))||(l.get("show")&&!e.scale.isBlank()||(i=[]),i=k(a)?c1(e,a,!0):"auto"===a?(s=a1(e,e.getLabelModel()),r=s.labelCategoryInterval,B(s.labels,function(t){return t.tickValue})):h1(e,r=a,!0),u1(o,a,{ticks:i,tickCategoryInterval:r}))):{ticks:B(t.scale.getTicks(),function(t){return t.value})}}function a1(t,e){var n,i=s1(t,"labels"),e=L_(e),r=l1(i,e);return r||u1(i,e,{labels:k(e)?c1(t,e):h1(t,n="auto"===e?null!=(i=n1(r=t).autoInterval)?i:n1(r).autoInterval=r.calculateCategoryInterval():e),labelCategoryInterval:n})}function s1(t,e){return n1(t)[e]||(n1(t)[e]=[])}function l1(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function u1(t,e,n){return t.push({key:e,value:n}),n}function h1(t,e,n){for(var i=D_(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),e=o[0],u=r.count(),u=(0!==e&&1<l&&2<u/l&&(e=Math.round(Math.ceil(e/l)*l)),O_(t)),t=a.get("showMinLabel")||u,a=a.get("showMaxLabel")||u,h=(t&&e!==o[0]&&c(o[0]),e);h<=o[1];h+=l)c(h);function c(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t})}return a&&h-l!==o[1]&&c(o[1]),s}function c1(t,i,r){var o=t.scale,a=D_(t),s=[];return O(o.getTicks(),function(t){var e=o.getLabel(t),n=t.value;i(t.value,e)&&s.push(r?n:{formattedLabel:a(t),rawLabel:e,tickValue:n})}),s}var p1=[0,1],Pc=(d1.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),e=Math.max(e[0],e[1]);return n<=t&&t<=e},d1.prototype.containData=function(t){return this.scale.contain(t)},d1.prototype.getExtent=function(){return this._extent.slice()},d1.prototype.getPixelPrecision=function(t){return oo(t||this.scale.getExtent(),this._extent)},d1.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},d1.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&f1(n=n.slice(),i.count()),to(t,p1,n,e)},d1.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale,i=(this.onBand&&"ordinal"===i.type&&f1(n=n.slice(),i.count()),to(t,n,p1,e));return this.scale.scale(i)},d1.prototype.pointToData=function(t,e){},d1.prototype.getTicksCoords=function(t){var e,n,i,r,o,a,s,l=(t=t||{}).tickModel||this.getTickModel(),u=B(o1(this,l).ticks,function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}},this),l=l.get("alignWithLabel");function h(t,e){return t=no(t),e=no(e),a?e<t:t<e}return e=this,n=u,l=l,t=t.clamp,s=n.length,e.onBand&&!l&&s&&(l=e.getExtent(),1===s?(n[0].coord=l[0],i=n[1]={coord:l[1],tickValue:n[0].tickValue}):(o=n[s-1].tickValue-n[0].tickValue,r=(n[s-1].coord-n[0].coord)/o,O(n,function(t){t.coord-=r/2}),e=1+(o=e.scale.getExtent())[1]-n[s-1].tickValue,i={coord:n[s-1].coord+r*e,tickValue:o[1]+1},n.push(i)),a=l[0]>l[1],h(n[0].coord,l[0])&&(t?n[0].coord=l[0]:n.shift()),t&&h(l[0],n[0].coord)&&n.unshift({coord:l[0]}),h(l[1],i.coord)&&(t?i.coord=l[1]:n.pop()),t)&&h(i.coord,l[1])&&n.push({coord:l[1]}),u},d1.prototype.getMinorTicksCoords=function(){var t;return"ordinal"===this.scale.type?[]:(t=this.model.getModel("minorTick").get("splitNumber"),B(this.scale.getMinorTicks(t=0<t&&t<100?t:5),function(t){return B(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this))},d1.prototype.getViewLabels=function(){return r1(this).labels},d1.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},d1.prototype.getTickModel=function(){return this.model.getModel("axisTick")},d1.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),e=e[1]-e[0]+(this.onBand?1:0),t=(0===e&&(e=1),Math.abs(t[1]-t[0]));return Math.abs(t)/e},d1.prototype.calculateCategoryInterval=function(){r=(n=d=this).getLabelModel();var t={axisRotate:n.getRotate?n.getRotate():n.isHorizontal&&!n.isHorizontal()?90:0,labelRotate:r.get("rotate")||0,font:r.getFont()},e=D_(d),n=(t.axisRotate-t.labelRotate)/180*Math.PI,i=(r=d.scale).getExtent(),r=r.count();if(i[1]-i[0]<1)return 0;for(var o=1,a=(40<r&&(o=Math.max(1,Math.floor(r/40))),i[0]),s=d.dataToCoord(a+1)-d.dataToCoord(a),l=Math.abs(s*Math.cos(n)),s=Math.abs(s*Math.sin(n)),u=0,h=0;a<=i[1];a+=o)var c=1.3*(p=Mr(e({value:a}),t.font,"center","top")).width,p=1.3*p.height,u=Math.max(u,c,7),h=Math.max(h,p,7);var n=u/l,l=h/s,s=(isNaN(n)&&(n=1/0),isNaN(l)&&(l=1/0),Math.max(0,Math.floor(Math.min(n,l)))),n=n1(d.model),l=d.getExtent(),d=n.lastAutoInterval,f=n.lastTickCount;return null!=d&&null!=f&&Math.abs(d-s)<=1&&Math.abs(f-r)<=1&&s<d&&n.axisExtent0===l[0]&&n.axisExtent1===l[1]?s=d:(n.lastTickCount=r,n.lastAutoInterval=s,n.axisExtent0=l[0],n.axisExtent1=l[1]),s},d1);function d1(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}function f1(t,e){e=(t[1]-t[0])/e/2;t[0]+=e,t[1]-=e}var g1=2*Math.PI,y1=Ka.CMD,m1=["top","right","bottom","left"];function v1(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,n=n-t,i=i-e,h=Math.sqrt(n*n+i*i),l=(l*(n/=h)+u*(i/=h))/h,u=(s&&(l=Math.min(Math.max(l,0),1)),a[0]=t+(l*=h)*n),s=a[1]=e+l*i;return Math.sqrt((u-r)*(u-r)+(s-o)*(s-o))}function _1(t,e,n,i,r,o,a){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i);n=t+n,i=e+i,t=a[0]=Math.min(Math.max(r,t),n),n=a[1]=Math.min(Math.max(o,e),i);return Math.sqrt((t-r)*(t-r)+(n-o)*(n-o))}var x1=[];function w1(t,e,n){for(var i,r,o,a,s,l,u,h,c,p=0,d=0,f=0,g=0,y=1/0,m=e.data,v=t.x,_=t.y,x=0;x<m.length;){var w=m[x++],b=(1===x&&(f=p=m[x],g=d=m[x+1]),y);switch(w){case y1.M:p=f=m[x++],d=g=m[x++];break;case y1.L:b=v1(p,d,m[x],m[x+1],v,_,x1,!0),p=m[x++],d=m[x++];break;case y1.C:b=Gn(p,d,m[x++],m[x++],m[x++],m[x++],m[x],m[x+1],v,_,x1),p=m[x++],d=m[x++];break;case y1.Q:b=qn(p,d,m[x++],m[x++],m[x],m[x+1],v,_,x1),p=m[x++],d=m[x++];break;case y1.A:var S=m[x++],M=m[x++],T=m[x++],C=m[x++],I=m[x++],k=m[x++],D=(x+=1,!!(1-m[x++])),A=Math.cos(I)*T+S,P=Math.sin(I)*C+M;x<=1&&(f=A,g=P),P=(A=I)+k,D=D,a=(v-S)*(o=C)/T+S,s=_,l=x1,c=h=u=void 0,a-=i=S,s-=r=M,u=Math.sqrt(a*a+s*s),h=(a/=u)*o+i,c=(s/=u)*o+r,b=Math.abs(A-P)%g1<1e-4||((P=D?(D=A,A=Ja(P),Ja(D)):(A=Ja(A),Ja(P)))<A&&(P+=g1),(D=Math.atan2(s,a))<0&&(D+=g1),A<=D&&D<=P)||A<=D+g1&&D+g1<=P?(l[0]=h,l[1]=c,u-o):(c=((D=o*Math.cos(A)+i)-a)*(D-a)+((h=o*Math.sin(A)+r)-s)*(h-s))<(i=((u=o*Math.cos(P)+i)-a)*(u-a)+((A=o*Math.sin(P)+r)-s)*(A-s))?(l[0]=D,l[1]=h,Math.sqrt(c)):(l[0]=u,l[1]=A,Math.sqrt(i)),p=Math.cos(I+k)*T+S,d=Math.sin(I+k)*C+M;break;case y1.R:b=_1(f=p=m[x++],g=d=m[x++],m[x++],m[x++],v,_,x1);break;case y1.Z:b=v1(p,d,f,g,v,_,x1,!0),p=f,d=g}b<y&&(y=b,n.set(x1[0],x1[1]))}return y}var b1=new M,S1=new M,M1=new M,T1=new M,C1=new M;function I1(t,e){if(t){var n=t.getTextGuideLine(),i=t.getTextContent();if(i&&n){var r=t.textGuideLineConfig||{},o=[[0,0],[0,0],[0,0]],a=r.candidates||m1,s=i.getBoundingRect().clone(),l=(s.applyTransform(i.getComputedTransform()),1/0),u=r.anchor,h=t.getComputedTransform(),c=h&&Fe([],h),p=e.get("length2")||0;u&&M1.copy(u);for(var d,f,g=0;g<a.length;g++){var y=a[g],m=(S=b=w=x=_=v=m=void 0,y),v=0,_=s,x=b1,w=T1,b=_.width,S=_.height;switch(m){case"top":x.set(_.x+b/2,_.y-v),w.set(0,-1);break;case"bottom":x.set(_.x+b/2,_.y+S+v),w.set(0,1);break;case"left":x.set(_.x-v,_.y+S/2),w.set(-1,0);break;case"right":x.set(_.x+b+v,_.y+S/2),w.set(1,0)}M.scaleAndAdd(S1,b1,T1,p),S1.transform(c);y=t.getBoundingRect(),y=u?u.distance(S1):t instanceof j?w1(S1,t.path,M1):(m=M1,d=_1((d=y).x,y.y,y.width,y.height,S1.x,S1.y,x1),m.set(x1[0],x1[1]),d);y<l&&(l=y,S1.transform(h),M1.transform(h),M1.toArray(o[0]),S1.toArray(o[1]),b1.toArray(o[2]))}i=o,(r=e.get("minTurnAngle"))<=180&&0<r&&(r=r/180*Math.PI,b1.fromArray(i[0]),S1.fromArray(i[1]),M1.fromArray(i[2]),M.sub(T1,b1,S1),M.sub(C1,M1,S1),e=T1.len(),f=C1.len(),e<.001||f<.001||(T1.scale(1/e),C1.scale(1/f),e=T1.dot(C1),Math.cos(r)<e&&(f=v1(S1.x,S1.y,M1.x,M1.y,b1.x,b1.y,k1,!1),D1.fromArray(k1),D1.scaleAndAdd(C1,f/Math.tan(Math.PI-r)),e=M1.x!==S1.x?(D1.x-S1.x)/(M1.x-S1.x):(D1.y-S1.y)/(M1.y-S1.y),isNaN(e)||(e<0?M.copy(D1,S1):1<e&&M.copy(D1,M1),D1.toArray(i[1]))))),n.setShape({points:o})}}}var k1=[],D1=new M;function A1(t,e,n,i){var r="normal"===n,n=r?t:t.ensureState(n),e=(n.ignore=e,i.get("smooth")),e=(e&&!0===e&&(e=.3),n.shape=n.shape||{},0<e&&(n.shape.smooth=e),i.getModel("lineStyle").getLineStyle());r?t.useStyle(e):n.style=e}function P1(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),0<n&&3<=i.length){var e=$t(i[0],i[1]),r=$t(i[1],i[2]);e&&r?(n=Math.min(e,r)*n,e=te([],i[1],i[0],n/e),n=te([],i[1],i[2],n/r),r=te([],e,n,.5),t.bezierCurveTo(e[0],e[1],e[0],e[1],r[0],r[1]),t.bezierCurveTo(n[0],n[1],n[0],n[1],i[2][0],i[2][1])):(t.lineTo(i[1][0],i[1][1]),t.lineTo(i[2][0],i[2][1]))}else for(var o=1;o<i.length;o++)t.lineTo(i[o][0],i[o][1])}function L1(t){for(var e=[],n=0;n<t.length;n++){var i,r,o,a,s,l,u=t[n];u.defaultAttr.ignore||(r=(i=u.label).getComputedTransform(),o=i.getBoundingRect(),a=!r||r[1]<1e-5&&r[2]<1e-5,l=i.style.margin||0,(s=o.clone()).applyTransform(r),s.x-=l/2,s.y-=l/2,s.width+=l,s.height+=l,l=a?new wh(o,r):null,e.push({label:i,labelLine:u.labelLine,rect:s,localRect:o,obb:l,priority:u.priority,defaultAttr:u.defaultAttr,layoutOption:u.computedLayoutOption,axisAligned:a,transform:r}))}return e}function O1(s,l,u,t,e,n){var h=s.length;if(!(h<2)){s.sort(function(t,e){return t.rect[l]-e.rect[l]});for(var i=0,o=!1,r=0,a=0;a<h;a++){var c,p=s[a],d=p.rect;(c=d[l]-i)<0&&(d[l]-=c,p.label[l]-=c,o=!0),r+=Math.max(-c,0),i=d[l]+d[u]}0<r&&n&&x(-r/h,0,h);var f,g,y=s[0],m=s[h-1];return v(),f<0&&w(-f,.8),g<0&&w(g,.8),v(),_(f,g,1),_(g,f,-1),v(),f<0&&b(-f),g<0&&b(g),o}function v(){f=y.rect[l]-t,g=e-m.rect[l]-m.rect[u]}function _(t,e,n){t<0&&(0<(e=Math.min(e,-t))?(x(e*n,0,h),(e=e+t)<0&&w(-e*n,1)):w(-t*n,1))}function x(t,e,n){0!==t&&(o=!0);for(var i=e;i<n;i++){var r=s[i];r.rect[l]+=t,r.label[l]+=t}}function w(t,e){for(var n=[],i=0,r=1;r<h;r++){var o=s[r-1].rect,o=Math.max(s[r].rect[l]-o[l]-o[u],0);n.push(o),i+=o}if(i){var a=Math.min(Math.abs(t)/i,e);if(0<t)for(r=0;r<h-1;r++)x(n[r]*a,0,r+1);else for(r=h-1;0<r;r--)x(-(n[r-1]*a),r,h)}}function b(t){for(var e=t<0?-1:1,n=(t=Math.abs(t),Math.ceil(t/(h-1))),i=0;i<h-1;i++)if(0<e?x(n,0,i+1):x(-n,h-i-1,h),(t-=n)<=0)return}}function R1(t){var e=[],n=(t.sort(function(t,e){return e.priority-t.priority}),new X(0,0,0,0));function i(t){var e;t.ignore||null==(e=t.ensureState("emphasis")).ignore&&(e.ignore=!1),t.ignore=!0}for(var r=0;r<t.length;r++){for(var o=t[r],a=o.axisAligned,s=o.localRect,l=o.transform,u=o.label,h=o.labelLine,c=(n.copy(o.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05,o.obb),p=!1,d=0;d<e.length;d++){var f=e[d];if(n.intersect(f.rect)){if(a&&f.axisAligned){p=!0;break}if(f.obb||(f.obb=new wh(f.localRect,f.transform)),(c=c||new wh(s,l)).intersect(f.obb)){p=!0;break}}}p?(i(u),h&&i(h)):(u.attr("ignore",o.defaultAttr.ignore),h&&h.attr("ignore",o.defaultAttr.labelGuideIgnore),e.push(o))}}function N1(t,e){var n=t.label,e=e&&e.getTextGuideLine();return{dataIndex:t.dataIndex,dataType:t.dataType,seriesIndex:t.seriesModel.seriesIndex,text:t.label.style.text,rect:t.hostRect,labelRect:t.rect,align:n.style.align,verticalAlign:n.style.verticalAlign,labelLinePoints:function(t){if(t){for(var e=[],n=0;n<t.length;n++)e.push(t[n].slice());return e}}(e&&e.shape.points)}}var E1=["align","verticalAlign","width","height","fontSize"],z1=new vr,B1=Lo(),F1=Lo();function V1(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null!=e[r]&&(t[r]=e[r])}}var H1=["x","y","rotation"],G1=(W1.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},W1.prototype._addLabel=function(t,e,n,i,r){var o,a=i.style,s=i.__hostTarget.textConfig||{},l=i.getComputedTransform(),u=i.getBoundingRect().plain(),l=(X.applyTransform(u,u,l),l?z1.setLocalTransform(l):(z1.x=z1.y=z1.rotation=z1.originX=z1.originY=0,z1.scaleX=z1.scaleY=1),z1.rotation=Ja(z1.rotation),i.__hostTarget),h=(l&&(o=l.getBoundingRect().plain(),h=l.getComputedTransform(),X.applyTransform(o,o,h)),o&&l.getTextGuideLine());this._labelList.push({label:i,labelLine:h,seriesModel:n,dataIndex:t,dataType:e,layoutOption:r,computedLayoutOption:null,rect:u,hostRect:o,priority:o?o.width*o.height:0,defaultAttr:{ignore:i.ignore,labelGuideIgnore:h&&h.ignore,x:z1.x,y:z1.y,scaleX:z1.scaleX,scaleY:z1.scaleY,rotation:z1.rotation,style:{x:a.x,y:a.y,align:a.align,verticalAlign:a.verticalAlign,width:a.width,height:a.height,fontSize:a.fontSize},cursor:i.cursor,attachedPos:s.position,attachedRot:s.rotation}})},W1.prototype.addLabelsOfSeries=function(t){var n=this,i=(this._chartViewList.push(t),t.__model),r=i.get("labelLayout");(k(r)||ht(r).length)&&t.group.traverse(function(t){if(t.ignore)return!0;var e=t.getTextContent(),t=D(t);e&&!e.disableLabelLayout&&n._addLabel(t.dataIndex,t.dataType,i,e,r)})},W1.prototype.updateLayoutConfig=function(t){var e=t.getWidth(),n=t.getHeight();for(var i=0;i<this._labelList.length;i++){var r=this._labelList[i],o=r.label,a=o.__hostTarget,s=r.defaultAttr,l=void 0,l=k(r.layoutOption)?r.layoutOption(N1(r,a)):r.layoutOption,u=(r.computedLayoutOption=l=l||{},Math.PI/180),h=(a&&a.setTextConfig({local:!1,position:null!=l.x||null!=l.y?null:s.attachedPos,rotation:null!=l.rotate?l.rotate*u:s.attachedRot,offset:[l.dx||0,l.dy||0]}),!1);null!=l.x?(o.x=eo(l.x,e),o.setStyle("x",0),h=!0):(o.x=s.x,o.setStyle("x",s.style.x)),null!=l.y?(o.y=eo(l.y,n),o.setStyle("y",0),h=!0):(o.y=s.y,o.setStyle("y",s.style.y)),l.labelLinePoints&&(c=a.getTextGuideLine())&&(c.setShape({points:l.labelLinePoints}),h=!1),B1(o).needsUpdateLabelLine=h,o.rotation=null!=l.rotate?l.rotate*u:s.rotation,o.scaleX=s.scaleX,o.scaleY=s.scaleY;for(var c,p=0;p<E1.length;p++){var d=E1[p];o.setStyle(d,(null!=l[d]?l:s.style)[d])}l.draggable?(o.draggable=!0,o.cursor="move",a&&(c=r.seriesModel,null!=r.dataIndex&&(c=r.seriesModel.getData(r.dataType).getItemModel(r.dataIndex)),o.on("drag",function(t,e){return function(){I1(t,e)}}(a,c.getModel("labelLine"))))):(o.off("drag"),o.cursor=s.cursor)}},W1.prototype.layout=function(t){var e,n,i=t.getWidth(),t=t.getHeight(),r=L1(this._labelList),o=ut(r,function(t){return"shiftX"===t.layoutOption.moveOverlap}),a=ut(r,function(t){return"shiftY"===t.layoutOption.moveOverlap});O1(o,"x","width",0,i,e),O1(a,"y","height",0,t,n),R1(ut(r,function(t){return t.layoutOption.hideOverlap}))},W1.prototype.processLabelsOverall=function(){var a=this;O(this._chartViewList,function(t){var i=t.__model,r=t.ignoreLabelLineUpdate,o=i.isAnimationEnabled();t.group.traverse(function(t){if(t.ignore&&!t.forceLabelAnimation)return!0;var e=!r,n=t.getTextContent();(e=!e&&n?B1(n).needsUpdateLabelLine:e)&&a._updateLabelLine(t,i),o&&a._animateLabels(t,i)})})},W1.prototype._updateLabelLine=function(t,e){var n=t.getTextContent(),i=D(t),r=i.dataIndex;if(n&&null!=r){var n=e.getData(i.dataType),e=n.getItemModel(r),i={},r=n.getItemVisual(r,"style"),r=(r&&(n=n.getVisual("drawType"),i.stroke=r[n]),e.getModel("labelLine")),o=t,a=function(t,e){for(var n={normal:t.getModel(e=e||"labelLine")},i=0;i<tl.length;i++){var r=tl[i];n[r]=t.getModel([r,e])}return n}(e),n=i,s=o.getTextGuideLine(),l=o.getTextContent();if(l){for(var e=a.normal,u=e.get("show"),h=l.ignore,c=0;c<el.length;c++){var p,d=el[c],f=a[d],g="normal"===d;f&&(p=f.get("show"),(g?h:N(l.states[d]&&l.states[d].ignore,h))||!N(p,u)?((p=g?s:s&&s.states[d])&&(p.ignore=!0),s&&A1(s,!0,d,f)):(s||(s=new Uu,o.setTextGuideLine(s),g||!h&&u||A1(s,!0,"normal",a.normal),o.stateProxy&&(s.stateProxy=o.stateProxy)),A1(s,!1,d,f)))}s&&(z(s.style,n),s.style.fill=null,n=e.get("showAbove"),(o.textGuideLineConfig=o.textGuideLineConfig||{}).showAbove=n||!1,s.buildPath=P1)}else s&&o.removeTextGuideLine();I1(t,r)}},W1.prototype._animateLabels=function(t,e){var n,i,r,o,a,s=t.getTextContent(),l=t.getTextGuideLine();!s||!t.forceLabelAnimation&&(s.ignore||s.invisible||t.disableLabelAnimation||Ah(t))||(o=(r=B1(s)).oldLayout,n=(i=D(t)).dataIndex,a={x:s.x,y:s.y,rotation:s.rotation},i=e.getData(i.dataType),o?(s.attr(o),(t=t.prevStates)&&(0<=I(t,"select")&&s.attr(r.oldLayoutSelect),0<=I(t,"emphasis"))&&s.attr(r.oldLayoutEmphasis),kh(s,a,e,n)):(s.attr(a),_c(s).valueAnimation||(t=N(s.style.opacity,1),s.style.opacity=0,Dh(s,{style:{opacity:t}},e,n))),r.oldLayout=a,s.states.select&&(V1(t=r.oldLayoutSelect={},a,H1),V1(t,s.states.select,H1)),s.states.emphasis&&(V1(t=r.oldLayoutEmphasis={},a,H1),V1(t,s.states.emphasis,H1)),xc(s,n,i,e,e)),!l||l.ignore||l.invisible||(o=(r=F1(l)).oldLayout,a={points:l.shape.points},o?(l.attr({shape:o}),kh(l,{shape:a},e)):(l.setShape(a),l.style.strokePercent=0,Dh(l,{style:{strokePercent:1}},e)),r.oldLayout=a)},W1);function W1(){this._labelList=[],this._chartViewList=[]}var U1=Lo();function X1(t){t.registerUpdateLifecycle("series:beforeupdate",function(t,e,n){(U1(e).labelManager||(U1(e).labelManager=new G1)).clearLabels()}),t.registerUpdateLifecycle("series:layoutlabels",function(t,e,n){var i=U1(e).labelManager;n.updatedSeries.forEach(function(t){i.addLabelsOfSeries(e.getViewOfSeriesModel(t))}),i.updateLayoutConfig(e),i.layout(e),i.processLabelsOverall()})}function Y1(t,e,n){var i=G.createCanvas(),r=e.getWidth(),e=e.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=r+"px",o.height=e+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=e*n,i}B_(X1);u(j1,q1=le),j1.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},j1.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},j1.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},j1.prototype.setUnpainted=function(){this.__firstTimePaint=!0},j1.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=Y1("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},j1.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var l=[],u=this.maxRepaintRectCount,h=!1,c=new X(0,0,0,0);function r(t){if(t.isFinite()&&!t.isZero())if(0===l.length)(e=new X(0,0,0,0)).copy(t),l.push(e);else{for(var e,n=!1,i=1/0,r=0,o=0;o<l.length;++o){var a=l[o];if(a.intersect(t)){var s=new X(0,0,0,0);s.copy(a),s.union(t),l[o]=s,n=!0;break}h&&(c.copy(t),c.union(a),s=t.width*t.height,a=a.width*a.height,(a=c.width*c.height-s-a)<i)&&(i=a,r=o)}h&&(l[r].union(t),n=!0),n||((e=new X(0,0,0,0)).copy(t),l.push(e)),h=h||l.length>=u}}for(var o,a=this.__startIndex;a<this.__endIndex;++a)(s=t[a])&&(d=s.shouldBePainted(n,i,!0,!0),(p=s.__isRendered&&(s.__dirty&vn||!d)?s.getPrevPaintRect():null)&&r(p),o=d&&(s.__dirty&vn||!s.__isRendered)?s.getPaintRect():null)&&r(o);for(a=this.__prevStartIndex;a<this.__prevEndIndex;++a){var s,p,d=(s=e[a])&&s.shouldBePainted(n,i,!0,!0);!s||d&&s.__zr||!s.__isRendered||(p=s.getPrevPaintRect())&&r(p)}do{for(var f=!1,a=0;a<l.length;)if(l[a].isZero())l.splice(a,1);else{for(var g=a+1;g<l.length;)l[a].intersect(l[g])?(f=!0,l[a].union(l[g]),l.splice(g,1)):g++;a++}}while(f);return this._paintRects=l},j1.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},j1.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n)&&this.ctxBack.scale(n,n)},j1.prototype.clear=function(t,o,e){var n=this.dom,a=this.ctx,i=n.width,r=n.height,s=(o=o||this.clearColor,this.motionBlur&&!t),l=this.lastFrameAlpha,u=this.dpr,h=this,c=(s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,i/u,r/u)),this.domBack);function p(t,e,n,i){var r;a.clearRect(t,e,n,i),o&&"transparent"!==o&&(r=void 0,mt(o)?(r=(o.global||o.__width===n&&o.__height===i)&&o.__canvasGradient||tm(a,o,{x:0,y:0,width:n,height:i}),o.__canvasGradient=r,o.__width=n,o.__height=i):vt(o)&&(o.scaleX=o.scaleX||u,o.scaleY=o.scaleY||u,r=hm(a,o,{dirty:function(){h.setUnpainted(),h.painter.refresh()}})),a.save(),a.fillStyle=r||o,a.fillRect(t,e,n,i),a.restore()),s&&(a.save(),a.globalAlpha=l,a.drawImage(c,t,e,n,i),a.restore())}!e||s?p(0,0,i,r):e.length&&O(e,function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)})};var q1,Z1=j1;function j1(t,e,n){var i,r=q1.call(this)||this,t=(r.motionBlur=!1,r.lastFrameAlpha=.7,r.dpr=1,r.virtual=!1,r.config={},r.incremental=!1,r.zlevel=0,r.maxRepaintRectCount=5,r.__dirty=!0,r.__firstTimePaint=!0,r.__used=!1,r.__drawIndex=0,r.__startIndex=0,r.__endIndex=0,r.__prevStartIndex=null,r.__prevEndIndex=null,n=n||ur,"string"==typeof t?i=Y1(t,e,n):R(t)&&(t=(i=t).id),r.id=t,(r.dom=i).style);return t&&(zt(i),i.onselectstart=function(){return!1},t.padding="0",t.margin="0",t.borderWidth="0"),r.painter=e,r.dpr=n,r}var K1=314159;m.prototype.getType=function(){return"canvas"},m.prototype.isSingleCanvas=function(){return this._singleCanvas},m.prototype.getViewportRoot=function(){return this._domRoot},m.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},m.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o,a=i[r],a=this._layers[a];!a.__builtin__&&a.refresh&&(o=0===r?this._backgroundColor:null,a.refresh(o))}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},m.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},m.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n=n||(this._hoverlayer=this.getLayer(1e5)),i||(i=n.ctx).save(),Sm(i,a,r,o===e-1))}i&&i.restore()}},m.prototype.getHoverLayer=function(){return this.getLayer(1e5)},m.prototype.paintOne=function(t,e){bm(t,e)},m.prototype._paintList=function(t,e,n,i){var r,o,a;this._redrawId===i&&(n=n||!1,this._updateLayerStatus(t),r=(o=this._doPaintList(t,e,n)).finished,o=o.needsRefreshHover,this._needsManuallyCompositing&&this._compositeManually(),o&&this._paintHoverList(t),r?this.eachLayer(function(t){t.afterBrush&&t.afterBrush()}):(a=this,Tn(function(){a._paintList(t,e,n,i)})))},m.prototype._compositeManually=function(){var e=this.getLayer(K1).ctx,n=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,n,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,n,i)})},m.prototype._doPaintList=function(d,f,g){for(var y=this,m=[],v=this._opts.useDirtyRect,t=0;t<this._zlevelList.length;t++){var e=this._zlevelList[t],e=this._layers[e];e.__builtin__&&e!==this._hoverlayer&&(e.__dirty||g)&&m.push(e)}for(var _=!0,x=!1,n=function(t){function e(t){var e={inHover:!1,allClipped:!1,prevEl:null,viewWidth:y._width,viewHeight:y._height};for(i=s;i<r.__endIndex;i++){var n=d[i];if(n.__inHover&&(x=!0),y._doPaintEl(n,r,v,t,e,i===r.__endIndex-1),l)if(15<Date.now()-u)break}e.prevElClipPaths&&o.restore()}var n,i,r=m[t],o=r.ctx,a=v&&r.createRepaintRects(d,f,w._width,w._height),s=g?r.__startIndex:r.__drawIndex,l=!g&&r.incremental&&Date.now,u=l&&Date.now(),t=r.zlevel===w._zlevelList[0]?w._backgroundColor:null;r.__startIndex!==r.__endIndex&&(s!==r.__startIndex||(n=d[s]).incremental&&n.notClear&&!g)||r.clear(!1,t,a),-1===s&&(console.error("For some unknown reason. drawIndex is -1"),s=r.__startIndex);if(a)if(0===a.length)i=r.__endIndex;else for(var h=w.dpr,c=0;c<a.length;++c){var p=a[c];o.save(),o.beginPath(),o.rect(p.x*h,p.y*h,p.width*h,p.height*h),o.clip(),e(p),o.restore()}else o.save(),e(),o.restore();r.__drawIndex=i,r.__drawIndex<r.__endIndex&&(_=!1)},w=this,i=0;i<m.length;i++)n(i);return b.wxa&&O(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),{finished:_,needsRefreshHover:x}},m.prototype._doPaintEl=function(t,e,n,i,r,o){e=e.ctx;n?(n=t.getPaintRect(),(!i||n&&n.intersect(i))&&(Sm(e,t,r,o),t.setPrevPaintRect(n))):Sm(e,t,r,o)},m.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=K1);var n=this._layers[t];return n||((n=new Z1("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?d(n,this._layerConfig[t],!0):this._layerConfig[t-.01]&&d(n,this._layerConfig[t-.01],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},m.prototype.insertLayer=function(t,e){var n,i=this._layers,r=this._zlevelList,o=r.length,a=this._domRoot,s=null,l=-1;if(!i[t]&&(n=e)&&(n.__builtin__||"function"==typeof n.resize&&"function"==typeof n.refresh)){if(0<o&&t>r[0]){for(l=0;l<o-1&&!(r[l]<t&&r[l+1]>t);l++);s=i[r[l]]}r.splice(l+1,0,t),(i[t]=e).virtual||(s?(n=s.dom).nextSibling?a.insertBefore(e.dom,n.nextSibling):a.appendChild(e.dom):a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom)),e.painter||(e.painter=this)}},m.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},m.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},m.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},m.prototype.getLayers=function(){return this._layers},m.prototype._updateLayerStatus=function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++)if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}for(var i,r=null,o=0,a=0;a<t.length;a++){var s,l=(s=t[a]).zlevel,u=void 0;i!==l&&(i=l,o=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,o=1):u=this.getLayer(l+(0<o?.01:0),this._needsManuallyCompositing),u.__builtin__||it("ZLevel "+l+" has been used by unkown layer "+u.id),u!==r&&(u.__used=!0,u.__startIndex!==a&&(u.__dirty=!0),u.__startIndex=a,u.incremental?u.__drawIndex=-1:u.__drawIndex=a,e(a),r=u),s.__dirty&vn&&!s.__inHover&&(u.__dirty=!0,u.incremental)&&u.__drawIndex<0&&(u.__drawIndex=a)}e(a),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},m.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},m.prototype._clearLayer=function(t){t.clear()},m.prototype.setBackgroundColor=function(t){this._backgroundColor=t,O(this._layers,function(t){t.setUnpainted()})},m.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?d(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];r!==t&&r!==t+.01||d(this._layers[r],n[t],!0)}}},m.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(I(n,t),1))},m.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot,i=(n.style.display="none",this._opts),r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=nm(r,0,i),e=nm(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(K1).resize(t,e)}return this},m.prototype.clearLayer=function(t){t=this._layers[t];t&&t.clear()},m.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},m.prototype.getRenderedCanvas=function(t){if(this._singleCanvas&&!this._compositeManually)return this._layers[K1].dom;var e=new Z1("image",this,(t=t||{}).pixelRatio||this.dpr),n=(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),e.ctx);if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer(function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];Sm(n,u,o,s===l-1)}return e.dom},m.prototype.getWidth=function(){return this._width},m.prototype.getHeight=function(){return this._height};var $1=m;function m(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=L({},n||{}),this.dpr=n.devicePixelRatio||ur,this._singleCanvas=r;(this.root=t).style&&(zt(t),t.innerHTML=""),this.storage=e;var o,a,e=this._zlevelList,s=(this._prevDisplayList=[],this._layers);r?(o=(r=t).width,a=r.height,null!=n.width&&(o=n.width),null!=n.height&&(a=n.height),this.dpr=n.devicePixelRatio||1,r.width=o*this.dpr,r.height=a*this.dpr,this._width=o,this._height=a,(o=new Z1(r,this,this.dpr)).__builtin__=!0,o.initContext(),(s[K1]=o).zlevel=K1,e.push(K1),this._domRoot=t):(this._width=nm(t,0,n),this._height=nm(t,1,n),o=this._domRoot=(a=this._width,r=this._height,(s=document.createElement("div")).style.cssText=["position:relative","width:"+a+"px","height:"+r+"px","padding:0","margin:0","border-width:0"].join(";")+";",s),t.appendChild(o))}u(tx,Q1=g),tx.prototype.init=function(t,e,n){Q1.prototype.init.call(this,t,e,n),this._sourceManager=new pg(this),fg(this)},tx.prototype.mergeOption=function(t,e){Q1.prototype.mergeOption.call(this,t,e),fg(this)},tx.prototype.optionUpdated=function(){this._sourceManager.dirty()},tx.prototype.getSourceManager=function(){return this._sourceManager},tx.type="dataset",tx.defaultOption={seriesLayoutBy:Jp};var Q1,J1=tx;function tx(){var t=null!==Q1&&Q1.apply(this,arguments)||this;return t.type="dataset",t}u(ix,ex=Xg),ix.type="dataset";var ex,nx=ix;function ix(){var t=null!==ex&&ex.apply(this,arguments)||this;return t.type="dataset",t}function rx(t){t.registerComponentModel(J1),t.registerComponentView(nx)}B_([function(t){t.registerPainter("canvas",$1)},rx]),B_(X1);var ox={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}},ax=function(t){return Math.round(t.length/2)};function sx(t){return{seriesType:t,reset:function(t,e,n){var i,r=t.getData(),o=t.get("sampling"),a=t.coordinateSystem,s=r.count();10<s&&"cartesian2d"===a.type&&o&&(i=a.getBaseAxis(),a=a.getOtherAxis(i),i=i.getExtent(),n=n.getDevicePixelRatio(),i=Math.abs(i[1]-i[0])*(n||1),n=Math.round(s/i),isFinite(n))&&1<n&&("lttb"===o?t.setData(r.lttbDownSample(r.mapDimension(a.dim),1/n)):"minmax"===o&&t.setData(r.minmaxDownSample(r.mapDimension(a.dim),1/n)),s=void 0,V(o)?s=ox[o]:k(o)&&(s=o),s)&&t.setData(r.downSample(r.mapDimension(a.dim),1/n,s,ax))}}}u(ux,lx=zg),ux.prototype.getInitialData=function(t,e){return Tv(null,this,{useEncodeDefaulter:!0})},ux.prototype.getMarkerPosition=function(t,p,e){var d,f,n=this.coordinateSystem;return n&&n.clampData?(d=n.clampData(t),f=n.dataToPoint(d),e?O(n.getAxes(),function(t,e){if("category"===t.type&&null!=p){var n=t.getTicksCoords(),i=t.getTickModel().get("alignWithLabel"),r=d[e],o="x1"===p[e]||"y1"===p[e];if(o&&!i&&(r+=1),!(n.length<2))if(2===n.length)f[e]=t.toGlobalCoord(t.getExtent()[o?1:0]);else{for(var a=void 0,s=void 0,l=1,u=0;u<n.length;u++){var h=n[u].coord,c=u===n.length-1?n[u-1].tickValue+l:n[u].tickValue;if(c===r){s=h;break}if(c<r)a=h;else if(null!=a&&r<c){s=(h+a)/2;break}1===u&&(l=c-n[0].tickValue)}null==s&&(s=(a?n[n.length-1]:n[0]).coord),f[e]=t.toGlobalCoord(s)}}}):(e=(t=this.getData()).getLayout("offset"),t=t.getLayout("size"),n=n.getBaseAxis().isHorizontal()?0:1,f[n]+=e+t/2),f):[NaN,NaN]},ux.type="series.__base_bar__",ux.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"};var lx,Ic=ux;function ux(){var t=null!==lx&&lx.apply(this,arguments)||this;return t.type=ux.type,t}zg.registerClass(Ic);u(px,hx=Ic),px.prototype.getInitialData=function(){return Tv(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},px.prototype.getProgressive=function(){return!!this.get("large")&&this.get("progressive")},px.prototype.getProgressiveThreshold=function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return t=t<e?e:t},px.prototype.brushSelector=function(t,e,n){return n.rect(e.getItemLayout(t))},px.type="series.bar",px.dependencies=["grid","polar"],px.defaultOption=Bc(Ic.defaultOption,{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1});var hx,cx=px;function px(){var t=null!==hx&&hx.apply(this,arguments)||this;return t.type=px.type,t}function dx(t,e,n,i,r){var o=t.getArea(),a=o.x,s=o.y,l=o.width,o=o.height,u=n.get(["lineStyle","width"])||0,h=(a-=u/2,s-=u/2,l+=u,o+=u,l=Math.ceil(l),a!==Math.floor(a)&&(a=Math.floor(a),l++),new As({shape:{x:a,y:s,width:l,height:o}}));return e&&(e=(u=t.getBaseAxis()).isHorizontal(),t=u.inverse,e?(t&&(h.shape.x+=l),h.shape.width=0):(t||(h.shape.y+=o),h.shape.height=0),u=k(r)?function(t){r(t,h)}:null,Dh(h,{shape:{width:l,height:o,x:a,y:s}},n,null,i,u)),h}function fx(t,e,n){var i=t.getArea(),r=no(i.r0,1),o=no(i.r,1),a=new Pu({shape:{cx:no(t.cx,1),cy:no(t.cy,1),r0:r,r:o,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});return e&&("angle"===t.getBaseAxis().dim?a.shape.endAngle=i.startAngle:a.shape.r=r,Dh(a,{shape:{endAngle:i.endAngle,r:o}},n)),a}var gx,yx=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},mx=(u(vx,gx=j),vx.prototype.getDefaultShape=function(){return new yx},vx.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=.5*(o-r),s=r+a,l=e.startAngle,u=e.endAngle,e=e.clockwise,h=2*Math.PI,c=e?u-l<h:l-u<h,h=(c||(l=u-(e?h:-h)),Math.cos(l)),p=Math.sin(l),d=Math.cos(u),f=Math.sin(u);c?(t.moveTo(h*r+n,p*r+i),t.arc(h*s+n,p*s+i,a,-Math.PI+l,l,!e)):t.moveTo(h*o+n,p*o+i),t.arc(n,i,o,l,u,!e),t.arc(d*s+n,f*s+i,a,u-2*Math.PI,u-Math.PI,!e),0!==r&&t.arc(n,i,r,u,l,e)},vx);function vx(t){t=gx.call(this,t)||this;return t.type="sausage",t}function _x(t,e){return t.type===e}function xx(t,e){var n,i=t.mapDimensionsAll("defaultedLabel"),r=i.length;if(1===r)return null!=(n=wf(t,e,i[0]))?n+"":null;if(r){for(var o=[],a=0;a<i.length;a++)o.push(wf(t,e,i[a]));return o.join(" ")}}function bx(t,e){var n=t.mapDimensionsAll("defaultedLabel");if(!F(e))return e+"";for(var i=[],r=0;r<n.length;r++){var o=t.getDimensionIndex(n[r]);0<=o&&i.push(e[o])}return i.join(" ")}function Sx(t,e,n){return e*Math.sin(t)*(n?-1:1)}function Mx(t,e,n){return e*Math.cos(t)*(n?1:-1)}var Tx=Math.max,Cx=Math.min;u(Dx,Ix=Kg),Dx.prototype.render=function(t,e,n,i){this._model=t,this._removeOnRenderedListener(n),this._updateDrawMode(t);var r=t.get("coordinateSystem");"cartesian2d"!==r&&"polar"!==r||(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(t,e,n):this._renderNormal(t,e,n,i))},Dx.prototype.incrementalPrepareRender=function(t){this._clear(),this._updateDrawMode(t),this._updateLargeClip(t)},Dx.prototype.incrementalRender=function(t,e){this._progressiveEls=[],this._incrementalRenderLarge(t,e)},Dx.prototype.eachRendered=function(t){ac(this._progressiveEls||this.group,t)},Dx.prototype._updateDrawMode=function(t){t=t.pipelineContext.large;null!=this._isLargeDraw&&t===this._isLargeDraw||(this._isLargeDraw=t,this._clear())},Dx.prototype._renderNormal=function(a,t,e,n){var s,i,r,l=this.group,u=a.getData(),h=this._data,c=a.coordinateSystem,p=c.getBaseAxis(),d=("cartesian2d"===c.type?s=p.isHorizontal():"polar"===c.type&&(s="angle"===p.dim),a.isAnimationEnabled()?a:null),f=function(t,e){var t=t.get("realtimeSort",!0),n=e.getBaseAxis();if(t&&"category"===n.type&&"cartesian2d"===e.type)return{baseAxis:n,otherAxis:e.getOtherAxis(n)}}(a,c),g=(f&&this._enableRealtimeSort(f,u,e),a.get("clip",!0)||f),y=(e=u,r=(i=c).getArea&&i.getArea(),!_x(i,"cartesian2d")||"category"===(i=i.getBaseAxis()).type&&i.onBand||(e=e.getLayout("bandWidth"),i.isHorizontal()?(r.x-=e,r.width+=2*e):(r.y-=e,r.height+=2*e)),r),m=(l.removeClipPath(),a.get("roundCap",!0)),v=a.get("showBackground",!0),_=a.getModel("backgroundStyle"),x=_.get("borderRadius")||0,w=[],b=this._backgroundEls,S=n&&n.isInitSort,M=n&&"changeAxisOrder"===n.type;function T(t){var e=zx[c.type](u,t),n=(n=s,new("polar"===c.type?Pu:As)({shape:Yx(n,e,c),silent:!0,z2:0}));return n.useStyle(_.getItemStyle()),"cartesian2d"===c.type?n.setShape("r",x):n.setShape("cornerRadius",x),w[t]=n}u.diff(h).add(function(t){var e,n,i=u.getItemModel(t),r=zx[c.type](u,t,i);v&&T(t),u.hasValue(t)&&Ex[c.type](r)&&(e=!1,g&&(e=Ax[c.type](y,r)),n=Px[c.type](a,u,t,r,s,d,p.model,!1,m),f&&(n.forceLabelAnimation=!0),Fx(n,u,t,i,r,a,s,"polar"===c.type),S?n.attr({shape:r}):f?Lx(f,d,n,r,t,s,!1,!1):Dh(n,{shape:r},a,t),u.setItemGraphicEl(t,n),l.add(n),n.ignore=e)}).update(function(t,e){var n,i=u.getItemModel(t),r=zx[c.type](u,t,i),o=(v&&(o=void 0,0===b.length?o=T(e):((o=b[e]).useStyle(_.getItemStyle()),"cartesian2d"===c.type?o.setShape("r",x):o.setShape("cornerRadius",x),w[t]=o),n=zx[c.type](u,t),kh(o,{shape:Yx(s,n,c)},d,t)),h.getItemGraphicEl(e));u.hasValue(t)&&Ex[c.type](r)?(n=!1,g&&(n=Ax[c.type](y,r))&&l.remove(o),o?Rh(o):o=Px[c.type](a,u,t,r,s,d,p.model,!!o,m),f&&(o.forceLabelAnimation=!0),M?(e=o.getTextContent())&&null!=(e=_c(e)).prevValue&&(e.prevValue=e.value):Fx(o,u,t,i,r,a,s,"polar"===c.type),S?o.attr({shape:r}):f?Lx(f,d,o,r,t,s,!0,M):kh(o,{shape:r},a,t,null),u.setItemGraphicEl(t,o),o.ignore=n,l.add(o)):l.remove(o)}).remove(function(t){var e=h.getItemGraphicEl(t);e&&Oh(e,a,t)}).execute();var o=this._backgroundGroup||(this._backgroundGroup=new Gr);o.removeAll();for(var C=0;C<w.length;++C)o.add(w[C]);l.add(o),this._backgroundEls=w,this._data=u},Dx.prototype._renderLarge=function(t,e,n){this._clear(),Ux(t,this.group),this._updateLargeClip(t)},Dx.prototype._incrementalRenderLarge=function(t,e){this._removeBackground(),Ux(e,this.group,this._progressiveEls,!0)},Dx.prototype._updateLargeClip=function(t){var e,n,i=t.get("clip",!0)&&(i=t.coordinateSystem,r=!1,t=t,i?"polar"===i.type?fx(i,r,t):"cartesian2d"===i.type?dx(i,r,t,e,n):null:null),r=this.group;i?r.setClipPath(i):r.removeClipPath()},Dx.prototype._enableRealtimeSort=function(t,e,n){var i,r,o=this;e.count()&&(i=t.baseAxis,this._isFirstFrame?(this._dispatchInitSort(e,t,n),this._isFirstFrame=!1):(r=function(t){t=e.getItemGraphicEl(t),t=t&&t.shape;return t&&Math.abs(i.isHorizontal()?t.height:t.width)||0},this._onRendered=function(){o._updateSortWithinSameData(e,r,i,n)},n.getZr().on("rendered",this._onRendered)))},Dx.prototype._dataSort=function(t,e,i){var r=[];return t.each(t.mapDimension(e.dim),function(t,e){var n=i(e);r.push({dataIndex:e,mappedValue:null==n?NaN:n,ordinalNumber:t})}),r.sort(function(t,e){return e.mappedValue-t.mappedValue}),{ordinalNumbers:B(r,function(t){return t.ordinalNumber})}},Dx.prototype._isOrderChangedWithinSameData=function(t,e,n){for(var i=n.scale,r=t.mapDimension(n.dim),o=Number.MAX_VALUE,a=0,s=i.getOrdinalMeta().categories.length;a<s;++a){var l=t.rawIndexOf(r,i.getRawOrdinalNumber(a)),l=l<0?Number.MIN_VALUE:e(t.indexOfRawIndex(l));if(o<l)return!0;o=l}return!1},Dx.prototype._isOrderDifferentInView=function(t,e){for(var n=e.scale,e=n.getExtent(),i=Math.max(0,e[0]),r=Math.min(e[1],n.getOrdinalMeta().categories.length-1);i<=r;++i)if(t.ordinalNumbers[i]!==n.getRawOrdinalNumber(i))return!0},Dx.prototype._updateSortWithinSameData=function(t,e,n,i){this._isOrderChangedWithinSameData(t,e,n)&&(t=this._dataSort(t,n,e),this._isOrderDifferentInView(t,n))&&(this._removeOnRenderedListener(i),i.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",axisId:n.index,sortInfo:t}))},Dx.prototype._dispatchInitSort=function(e,n,t){var i=n.baseAxis,r=this._dataSort(e,i,function(t){return e.get(e.mapDimension(n.otherAxis.dim),t)});t.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",isInitSort:!0,axisId:i.index,sortInfo:r})},Dx.prototype.remove=function(t,e){this._clear(this._model),this._removeOnRenderedListener(e)},Dx.prototype.dispose=function(t,e){this._removeOnRenderedListener(e)},Dx.prototype._removeOnRenderedListener=function(t){this._onRendered&&(t.getZr().off("rendered",this._onRendered),this._onRendered=null)},Dx.prototype._clear=function(e){var t=this.group,n=this._data;e&&e.isAnimationEnabled()&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl(function(t){Oh(t,e,D(t).dataIndex)})):t.removeAll(),this._data=null,this._isFirstFrame=!0},Dx.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},Dx.type="bar";var Ix,kx=Dx;function Dx(){var t=Ix.call(this)||this;return t.type=Dx.type,t._isFirstFrame=!0,t}var Ax={cartesian2d:function(t,e){var n=e.width<0?-1:1,i=e.height<0?-1:1,r=(n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height),t.x+t.width),o=t.y+t.height,a=Tx(e.x,t.x),s=Cx(e.x+e.width,r),t=Tx(e.y,t.y),l=Cx(e.y+e.height,o),u=s<a,h=l<t;return e.x=u&&r<a?s:a,e.y=h&&o<t?l:t,e.width=u?0:s-a,e.height=h?0:l-t,n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height),u||h},polar:function(t,e){var n,i=e.r0<=e.r?1:-1,r=(i<0&&(n=e.r,e.r=e.r0,e.r0=n),Cx(e.r,t.r)),t=Tx(e.r0,t.r0),r=(e.r=r)-(e.r0=t)<0;return i<0&&(n=e.r,e.r=e.r0,e.r0=n),r}},Px={cartesian2d:function(t,e,n,i,r,o,a,s,l){i=new As({shape:L({},i),z2:1});return i.__dataIndex=n,i.name="item",o&&(i.shape[r?"height":"width"]=0),i},polar:function(t,e,n,i,r,o,a,s,l){var w,b,l=!r&&l?mx:Pu,u=new l({shape:i,z2:1}),h=(u.name="item",Bx(r));return u.calculateTextPosition=(w=h,b=({isRoundCap:l===mx}||{}).isRoundCap,function(t,e,n){var i=e.position;if(!i||i instanceof Array)return Dr(t,e,n);var i=w(i),r=null!=e.distance?e.distance:5,o=this.shape,a=o.cx,s=o.cy,l=o.r,u=o.r0,h=(l+u)/2,c=o.startAngle,p=o.endAngle,d=(c+p)/2,f=b?Math.abs(l-u)/2:0,g=Math.cos,y=Math.sin,m=a+l*g(c),v=s+l*y(c),_="left",x="top";switch(i){case"startArc":m=a+(u-r)*g(d),v=s+(u-r)*y(d),_="center",x="top";break;case"insideStartArc":m=a+(u+r)*g(d),v=s+(u+r)*y(d),_="center",x="bottom";break;case"startAngle":m=a+h*g(c)+Sx(c,r+f,!1),v=s+h*y(c)+Mx(c,r+f,!1),_="right",x="middle";break;case"insideStartAngle":m=a+h*g(c)+Sx(c,f-r,!1),v=s+h*y(c)+Mx(c,f-r,!1),_="left",x="middle";break;case"middle":m=a+h*g(d),v=s+h*y(d),_="center",x="middle";break;case"endArc":m=a+(l+r)*g(d),v=s+(l+r)*y(d),_="center",x="bottom";break;case"insideEndArc":m=a+(l-r)*g(d),v=s+(l-r)*y(d),_="center",x="top";break;case"endAngle":m=a+h*g(p)+Sx(p,r+f,!0),v=s+h*y(p)+Mx(p,r+f,!0),_="left",x="middle";break;case"insideEndAngle":m=a+h*g(p)+Sx(p,f-r,!0),v=s+h*y(p)+Mx(p,f-r,!0),_="right",x="middle";break;default:return Dr(t,e,n)}return(t=t||{}).x=m,t.y=v,t.align=_,t.verticalAlign=x,t}),o&&(h={},u.shape[l=r?"r":"endAngle"]=r?i.r0:i.startAngle,h[l]=i[l],(s?kh:Dh)(u,{shape:h},o)),u}};function Lx(t,e,n,i,r,o,a,s){var l,o=o?(l={x:i.x,width:i.width},{y:i.y,height:i.height}):(l={y:i.y,height:i.height},{x:i.x,width:i.width});s||(a?kh:Dh)(n,{shape:o},e,r,null),(a?kh:Dh)(n,{shape:l},e?t.baseAxis.model:null,r)}function Ox(t,e){for(var n=0;n<e.length;n++)if(!isFinite(t[e[n]]))return 1}var Rx=["x","y","width","height"],Nx=["cx","cy","r","startAngle","endAngle"],Ex={cartesian2d:function(t){return!Ox(t,Rx)},polar:function(t){return!Ox(t,Nx)}},zx={cartesian2d:function(t,e,n){var t=t.getItemLayout(e),i=n&&(e=t,i=(n=n).get(["itemStyle","borderColor"]))&&"none"!==i?(i=n.get(["itemStyle","borderWidth"])||0,n=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),e=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height),Math.min(i,n,e)):0,n=0<t.width?1:-1,e=0<t.height?1:-1;return{x:t.x+n*i/2,y:t.y+e*i/2,width:t.width-n*i,height:t.height-e*i}},polar:function(t,e,n){t=t.getItemLayout(e);return{cx:t.cx,cy:t.cy,r0:t.r0,r:t.r,startAngle:t.startAngle,endAngle:t.endAngle,clockwise:t.clockwise}}};function Bx(t){return e=t?"Arc":"Angle",function(t){switch(t){case"start":case"insideStart":case"end":case"insideEnd":return t+e;default:return t}};var e}function Fx(t,e,n,i,r,o,a,s){var l=e.getItemVisual(n,"style"),u=(s?o.get("roundCap")||(L(u=t.shape,function(t,e,n){if(null==(t=t.get("borderRadius")))return n?{cornerRadius:0}:null;F(t)||(t=[t,t,t,t]);var i=Math.abs(e.r||0-e.r0||0);return{cornerRadius:B(t,function(t){return kr(t,i)})}}(i.getModel("itemStyle"),u,!0)),t.setShape(u)):(u=i.get(["itemStyle","borderRadius"])||0,t.setShape("r",u)),t.useStyle(l),i.getShallow("cursor")),u=(u&&t.attr("cursor",u),s?a?r.r>=r.r0?"endArc":"startArc":r.endAngle>=r.startAngle?"endAngle":"startAngle":a?0<=r.height?"bottom":"top":0<=r.width?"right":"left"),h=pc(i),l=(cc(t,h,{labelFetcher:o,labelDataIndex:n,defaultText:xx(o.getData(),n),inheritColor:l.fill,defaultOpacity:l.opacity,defaultOutsidePosition:u}),t.getTextContent()),h=(s&&l&&(s=i.get(["label","position"]),t.textConfig.inside="middle"===s||null,function(t,e,n,i){if(H(i))t.setTextConfig({rotation:i});else if(F(e))t.setTextConfig({rotation:0});else{var r,i=t.shape,o=i.clockwise?i.startAngle:i.endAngle,a=i.clockwise?i.endAngle:i.startAngle,s=(o+a)/2,i=n(e);switch(i){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":r=s;break;case"startAngle":case"insideStartAngle":r=o;break;case"endAngle":case"insideEndAngle":r=a;break;default:return t.setTextConfig({rotation:0})}n=1.5*Math.PI-r;"middle"===i&&n>Math.PI/2&&n<1.5*Math.PI&&(n-=Math.PI),t.setTextConfig({rotation:n})}}(t,"outside"===s?u:s,Bx(a),i.get(["label","rotate"]))),u=l,s=h,a=o.getRawValue(n),l=function(t){return bx(e,t)},u&&((u=_c(u)).prevValue=u.value,u.value=a,a=s.normal,u.valueAnimation=a.get("valueAnimation"),u.valueAnimation)&&(u.precision=a.get("precision"),u.defaultInterpolatedText=l,u.statesModels=s),i.getModel(["emphasis"]));Nl(t,h.get("focus"),h.get("blurScope"),h.get("disabled")),Bl(t,i),null!=(o=r).startAngle&&null!=o.endAngle&&o.startAngle===o.endAngle&&(t.style.fill="none",t.style.stroke="none",O(t.states,function(t){t.style&&(t.style.fill=t.style.stroke="none")}))}var Vx,Hx=function(){},Gx=(u(Wx,Vx=j),Wx.prototype.getDefaultShape=function(){return new Hx},Wx.prototype.buildPath=function(t,e){for(var n=e.points,i=this.baseDimIdx,r=1-this.baseDimIdx,o=[],a=[],s=this.barWidth,l=0;l<n.length;l+=3)a[i]=s,a[r]=n[l+2],o[i]=n[l+i],o[r]=n[l+r],t.rect(o[0],o[1],a[0],a[1])},Wx);function Wx(t){t=Vx.call(this,t)||this;return t.type="largeBar",t}function Ux(t,e,n,i){var r=t.getData(),o=r.getLayout("valueAxisHorizontal")?1:0,a=r.getLayout("largeDataIndices"),s=r.getLayout("size"),l=t.getModel("backgroundStyle"),u=r.getLayout("largeBackgroundPoints"),l=(u&&((u=new Gx({shape:{points:u},incremental:!!i,silent:!0,z2:0})).baseDimIdx=o,u.largeDataIndices=a,u.barWidth=s,u.useStyle(l.getItemStyle()),e.add(u),n)&&n.push(u),new Gx({shape:{points:r.getLayout("largePoints")},incremental:!!i,ignoreCoarsePointer:!0,z2:1}));l.baseDimIdx=o,l.largeDataIndices=a,l.barWidth=s,e.add(l),l.useStyle(r.getVisual("style")),l.style.stroke=null,D(l).seriesIndex=t.seriesIndex,t.get("silent")||(l.on("mousedown",Xx),l.on("mousemove",Xx)),n&&n.push(l)}var Xx=ay(function(t){t=function(t,e,n){for(var i=t.baseDimIdx,r=1-i,o=t.shape.points,a=t.largeDataIndices,s=[],l=[],u=t.barWidth,h=0,c=o.length/3;h<c;h++){var p=3*h;if(l[i]=u,l[r]=o[2+p],s[i]=o[p+i],s[r]=o[p+r],l[r]<0&&(s[r]+=l[r],l[r]=-l[r]),s[0]<=e&&e<=s[0]+l[0]&&s[1]<=n&&n<=s[1]+l[1])return a[h]}return-1}(this,t.offsetX,t.offsetY);D(this).dataIndex=0<=t?t:null},30,!1);function Yx(t,e,n){var i,r;return _x(n,"cartesian2d")?(i=e,r=n.getArea(),{x:(t?i:r).x,y:(t?r:i).y,width:(t?i:r).width,height:(t?r:i).height}):{cx:(r=n.getArea()).cx,cy:r.cy,r0:(t?r:e).r0,r:(t?r:e).r,startAngle:t?e.startAngle:0,endAngle:t?e.endAngle:2*Math.PI}}B_(function(t){t.registerChartView(kx),t.registerSeriesModel(cx),t.registerLayout(t.PRIORITY.VISUAL.LAYOUT,pt(e_,"bar")),t.registerLayout(t.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,n_("bar")),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,sx("bar")),t.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},function(e,t){var n=e.componentType||"series";t.eachComponent({mainType:n,query:e},function(t){e.sortInfo&&t.axis.setCategorySortInfo(e.sortInfo)})})});u(jx,qx=zg),jx.prototype.getInitialData=function(t){return Tv(null,this,{useEncodeDefaulter:!0})},jx.prototype.getLegendIcon=function(t){var e=new Gr,n=$y("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1),n=(e.add(n),n.setStyle(t.lineStyle),this.getData().getVisual("symbol")),i=this.getData().getVisual("symbolRotate"),n="none"===n?"circle":n,r=.8*t.itemHeight,r=$y(n,(t.itemWidth-r)/2,(t.itemHeight-r)/2,r,r,t.itemStyle.fill),i=(e.add(r),r.setStyle(t.itemStyle),"inherit"===t.iconRotate?i:t.iconRotate||0);return r.rotation=i*Math.PI/180,r.setOrigin([t.itemWidth/2,t.itemHeight/2]),-1<n.indexOf("empty")&&(r.style.stroke=r.style.fill,r.style.fill="#fff",r.style.lineWidth=2),e},jx.type="series.line",jx.dependencies=["grid","polar"],jx.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1};var qx,Zx=jx;function jx(){var t=null!==qx&&qx.apply(this,arguments)||this;return t.type=jx.type,t.hasSymbolVisual=!0,t}u(Qx,Kx=Gr),Qx.prototype._createSymbol=function(t,e,n,i,r){this.removeAll();r=$y(t,-1,-1,2,2,null,r);r.attr({z2:100,culling:!0,scaleX:i[0]/2,scaleY:i[1]/2}),r.drift=Jx,this._symbolType=t,this.add(r)},Qx.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},Qx.prototype.getSymbolType=function(){return this._symbolType},Qx.prototype.getSymbolPath=function(){return this.childAt(0)},Qx.prototype.highlight=function(){Sl(this.childAt(0))},Qx.prototype.downplay=function(){Ml(this.childAt(0))},Qx.prototype.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},Qx.prototype.setDraggable=function(t,e){var n=this.childAt(0);n.draggable=t,n.cursor=!e&&t?"move":n.cursor},Qx.prototype.updateData=function(t,e,n,i){this.silent=!1;var r,o,a,s=t.getItemVisual(e,"symbol")||"circle",l=t.hostModel,u=Qx.getSymbolSize(t,e),h=s!==this._symbolType,c=i&&i.disableAnimation;h?(r=t.getItemVisual(e,"symbolKeepAspect"),this._createSymbol(s,t,e,u,r)):((o=this.childAt(0)).silent=!1,a={scaleX:u[0]/2,scaleY:u[1]/2},c?o.attr(a):kh(o,a,l,e),Rh(o)),this._updateCommon(t,e,u,n,i),h&&(o=this.childAt(0),c||(a={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:o.style.opacity}},o.scaleX=o.scaleY=0,o.style.opacity=0,Dh(o,a,l,e))),c&&this.childAt(0).stopAnimation("leave")},Qx.prototype._updateCommon=function(e,t,n,i,r){var o,a,s,l,u,h,c,p,d=this.childAt(0),f=e.hostModel,g=(i&&(o=i.emphasisItemStyle,s=i.blurItemStyle,a=i.selectItemStyle,l=i.focus,u=i.blurScope,c=i.labelStatesModels,p=i.hoverScale,y=i.cursorStyle,h=i.emphasisDisabled),i&&!e.hasItemOption||(o=(g=(i=i&&i.itemModel?i.itemModel:e.getItemModel(t)).getModel("emphasis")).getModel("itemStyle").getItemStyle(),a=i.getModel(["select","itemStyle"]).getItemStyle(),s=i.getModel(["blur","itemStyle"]).getItemStyle(),l=g.get("focus"),u=g.get("blurScope"),h=g.get("disabled"),c=pc(i),p=g.getShallow("scale"),y=i.getShallow("cursor")),e.getItemVisual(t,"symbolRotate")),i=(d.attr("rotation",(g||0)*Math.PI/180||0),Qy(e.getItemVisual(t,"symbolOffset"),n)),g=(i&&(d.x=i[0],d.y=i[1]),y&&d.attr("cursor",y),e.getItemVisual(t,"style")),i=g.fill,y=(d instanceof ws?(y=d.style,d.useStyle(L({image:y.image,x:y.x,y:y.y,width:y.width,height:y.height},g))):(d.__isEmptyBrush?d.useStyle(L({},g)):d.useStyle(g),d.style.decal=null,d.setColor(i,r&&r.symbolInnerColor),d.style.strokeNoScale=!0),e.getItemVisual(t,"liftZ")),m=this._z2,v=(null!=y?null==m&&(this._z2=d.z2,d.z2+=y):null!=m&&(d.z2=m,this._z2=null),r&&r.useNameLabel);cc(d,c,{labelFetcher:f,labelDataIndex:t,defaultText:function(t){return v?e.getName(t):xx(e,t)},inheritColor:i,defaultOpacity:g.opacity}),this._sizeX=n[0]/2,this._sizeY=n[1]/2;y=d.ensureState("emphasis"),y.style=o,d.ensureState("select").style=a,d.ensureState("blur").style=s,m=null==p||!0===p?Math.max(1.1,3/this._sizeY):isFinite(p)&&0<p?+p:1;y.scaleX=this._sizeX*m,y.scaleY=this._sizeY*m,this.setSymbolScale(1),Nl(this,l,u,h)},Qx.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},Qx.prototype.fadeOut=function(t,e,n){var i=this.childAt(0),r=D(this).dataIndex,o=n&&n.animation;this.silent=i.silent=!0,n&&n.fadeLabel?(n=i.getTextContent())&&Ph(n,{style:{opacity:0}},e,{dataIndex:r,removeOpt:o,cb:function(){i.removeTextContent()}}):i.removeTextContent(),Ph(i,{style:{opacity:0},scaleX:0,scaleY:0},e,{dataIndex:r,cb:t,removeOpt:o})},Qx.getSymbolSize=function(t,e){return[(t=F(t=t.getItemVisual(e,"symbolSize"))?t:[+t,+t])[0]||0,t[1]||0]};var Kx,$x=Qx;function Qx(t,e,n,i){var r=Kx.call(this)||this;return r.updateData(t,e,n,i),r}function Jx(t,e){this.parent.drift(t,e)}function tw(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&(!i.isIgnore||!i.isIgnore(n))&&(!i.clipShape||i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function ew(t){return(t=null==t||R(t)?t:{isIgnore:t})||{}}function nw(t){var t=t.hostModel,e=t.getModel("emphasis");return{emphasisItemStyle:e.getModel("itemStyle").getItemStyle(),blurItemStyle:t.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:t.getModel(["select","itemStyle"]).getItemStyle(),focus:e.get("focus"),blurScope:e.get("blurScope"),emphasisDisabled:e.get("disabled"),hoverScale:e.get("scale"),labelStatesModels:pc(t),cursorStyle:t.get("cursor")}}rw.prototype.updateData=function(o,a){this._progressiveEls=null,a=ew(a);var s=this.group,l=o.hostModel,u=this._data,h=this._SymbolCtor,c=a.disableAnimation,p=nw(o),d={disableAnimation:c},f=a.getSymbolPoint||function(t){return o.getItemLayout(t)};u||s.removeAll(),o.diff(u).add(function(t){var e,n=f(t);tw(o,n,t,a)&&((e=new h(o,t,p,d)).setPosition(n),o.setItemGraphicEl(t,e),s.add(e))}).update(function(t,e){var n,i,e=u.getItemGraphicEl(e),r=f(t);tw(o,r,t,a)?(n=o.getItemVisual(t,"symbol")||"circle",i=e&&e.getSymbolType&&e.getSymbolType(),!e||i&&i!==n?(s.remove(e),(e=new h(o,t,p,d)).setPosition(r)):(e.updateData(o,t,p,d),i={x:r[0],y:r[1]},c?e.attr(i):kh(e,i,l)),s.add(e),o.setItemGraphicEl(t,e)):s.remove(e)}).remove(function(t){var e=u.getItemGraphicEl(t);e&&e.fadeOut(function(){s.remove(e)},l)}).execute(),this._getSymbolPoint=f,this._data=o},rw.prototype.updateLayout=function(){var n=this,t=this._data;t&&t.eachItemGraphicEl(function(t,e){e=n._getSymbolPoint(e);t.setPosition(e),t.markRedraw()})},rw.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=nw(t),this._data=null,this.group.removeAll()},rw.prototype.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}this._progressiveEls=[],n=ew(n);for(var r=t.start;r<t.end;r++){var o,a=e.getItemLayout(r);tw(e,a,r,n)&&((o=new this._SymbolCtor(e,r,this._seriesScope)).traverse(i),o.setPosition(a),this.group.add(o),e.setItemGraphicEl(r,o),this._progressiveEls.push(o))}},rw.prototype.eachRendered=function(t){ac(this._progressiveEls||this.group,t)},rw.prototype.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)},n.hostModel)}):e.removeAll()};var iw=rw;function rw(t){this.group=new Gr,this._SymbolCtor=t||$x}function ow(t,e,n){var i=t.getBaseAxis(),r=t.getOtherAxis(i),n=function(t,e){var n=0,t=t.scale.getExtent();"start"===e?n=t[0]:"end"===e?n=t[1]:H(e)&&!isNaN(e)?n=e:0<t[0]?n=t[0]:t[1]<0&&(n=t[1]);return n}(r,n),i=i.dim,r=r.dim,o=e.mapDimension(r),a=e.mapDimension(i),s="x"===r||"radius"===r?1:0,t=B(t.dimensions,function(t){return e.mapDimension(t)}),l=!1,u=e.getCalculationInfo("stackResultDimension");return Sv(e,t[0])&&(l=!0,t[0]=u),Sv(e,t[1])&&(l=!0,t[1]=u),{dataDimsForPoint:t,valueStart:n,valueAxisDim:r,baseAxisDim:i,stacked:!!l,valueDim:o,baseDim:a,baseDataOffset:s,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function aw(t,e,n,i){var r=NaN,o=(t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart),t.baseDataOffset),a=[];return a[o]=n.get(t.baseDim,i),a[1-o]=r,e.dataToPoint(a)}var sw=Math.min,lw=Math.max;function uw(t,e){return isNaN(t)||isNaN(e)}function hw(t,e,n,i,r,o,a,s,l){for(var u,h,c,p,d=n,f=0;f<i;f++){var g=e[2*d],y=e[2*d+1];if(r<=d||d<0)break;if(uw(g,y)){if(l){d+=o;continue}break}if(d===n)t[0<o?"moveTo":"lineTo"](g,y),c=g,p=y;else{var m=g-u,v=y-h;if(m*m+v*v<.5){d+=o;continue}if(0<a){for(var _=d+o,x=e[2*_],w=e[2*_+1];x===g&&w===y&&f<i;)f++,d+=o,x=e[2*(_+=o)],w=e[2*_+1],g=e[2*d],y=e[2*d+1];var b=f+1;if(l)for(;uw(x,w)&&b<i;)b++,x=e[2*(_+=o)],w=e[2*_+1];var S,M,T,C,I,k,D,A,P,m=0,v=0,L=void 0,O=void 0;i<=b||uw(x,w)?(D=g,A=y):(m=x-u,v=w-h,S=g-u,M=x-g,T=y-h,C=w-y,k=I=void 0,O="x"===s?(D=g-(P=0<m?1:-1)*(I=Math.abs(S))*a,A=y,L=g+P*(k=Math.abs(M))*a,y):"y"===s?(A=y-(P=0<v?1:-1)*(I=Math.abs(T))*a,L=D=g,y+P*(k=Math.abs(C))*a):(I=Math.sqrt(S*S+T*T),D=g-m*a*(1-(S=(k=Math.sqrt(M*M+C*C))/(k+I))),A=y-v*a*(1-S),O=y+v*a*S,L=sw(L=g+m*a*S,lw(x,g)),O=sw(O,lw(w,y)),L=lw(L,sw(x,g)),A=y-(v=(O=lw(O,sw(w,y)))-y)*I/k,D=sw(D=g-(m=L-g)*I/k,lw(u,g)),A=sw(A,lw(h,y)),L=g+(m=g-(D=lw(D,sw(u,g))))*k/I,y+(v=y-(A=lw(A,sw(h,y))))*k/I)),t.bezierCurveTo(c,p,D,A,g,y),c=L,p=O}else t.lineTo(g,y)}u=g,h=y,d+=o}return f}var cw,pw=function(){this.smooth=0,this.smoothConstraint=!0},dw=(u(fw,cw=j),fw.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},fw.prototype.getDefaultShape=function(){return new pw},fw.prototype.buildPath=function(t,e){var n=e.points,i=0,r=n.length/2;if(e.connectNulls){for(;0<r&&uw(n[2*r-2],n[2*r-1]);r--);for(;i<r&&uw(n[2*i],n[2*i+1]);i++);}for(;i<r;)i+=hw(t,n,i,r,r,1,e.smooth,e.smoothMonotone,e.connectNulls)+1},fw.prototype.getPointOn=function(t,e){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n,i,r=this.path.data,o=Ka.CMD,a="x"===e,s=[],l=0;l<r.length;){var u=void 0,h=void 0;switch(r[l++]){case o.M:n=r[l++],i=r[l++];break;case o.L:var c,u=r[l++],h=r[l++];if((c=a?(t-n)/(u-n):(t-i)/(h-i))<=1&&0<=c)return v=a?(h-i)*c+i:(u-n)*c+n,a?[t,v]:[v,t];n=u,i=h;break;case o.C:u=r[l++],h=r[l++];var p=r[l++],d=r[l++],f=r[l++],g=r[l++],y=a?Fn(n,u,p,f,t,s):Fn(i,h,d,g,t,s);if(0<y)for(var m=0;m<y;m++){var v,_=s[m];if(_<=1&&0<=_)return v=a?zn(i,h,d,g,_):zn(n,u,p,f,_),a?[t,v]:[v,t]}n=f,i=g}}},fw);function fw(t){t=cw.call(this,t)||this;return t.type="ec-polyline",t}u(mw,gw=pw);var gw,yw=mw;function mw(){return null!==gw&&gw.apply(this,arguments)||this}u(xw,vw=j),xw.prototype.getDefaultShape=function(){return new yw},xw.prototype.buildPath=function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,o=n.length/2,a=e.smoothMonotone;if(e.connectNulls){for(;0<o&&uw(n[2*o-2],n[2*o-1]);o--);for(;r<o&&uw(n[2*r],n[2*r+1]);r++);}for(;r<o;){var s=hw(t,n,r,o,o,1,e.smooth,a,e.connectNulls);hw(t,i,r+s-1,s,o,-1,e.stackedOnSmooth,a,e.connectNulls),r+=s+1,t.closePath()}};var vw,_w=xw;function xw(t){t=vw.call(this,t)||this;return t.type="ec-polygon",t}function ww(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++)if(t[n]!==e[n])return;return 1}}function bw(t){for(var e=1/0,n=1/0,i=-1/0,r=-1/0,o=0;o<t.length;){var a=t[o++],s=t[o++];isNaN(a)||(e=Math.min(a,e),i=Math.max(a,i)),isNaN(s)||(n=Math.min(s,n),r=Math.max(s,r))}return[[e,n],[i,r]]}function Sw(t,e){var t=bw(t),n=t[0],t=t[1],e=bw(e),i=e[0],e=e[1];return Math.max(Math.abs(n[0]-i[0]),Math.abs(n[1]-i[1]),Math.abs(t[0]-e[0]),Math.abs(t[1]-e[1]))}function Mw(t){return H(t)?t:t?.5:0}function Tw(t,e,n,i,r){var n=n.getBaseAxis(),o="x"===n.dim||"radius"===n.dim?0:1,a=[],s=0,l=[],u=[],h=[],c=[];if(r){for(s=0;s<t.length;s+=2){var p=e||t;isNaN(p[s])||isNaN(p[s+1])||c.push(t[s],t[s+1])}t=c}for(s=0;s<t.length-2;s+=2)switch(h[0]=t[s+2],h[1]=t[s+3],u[0]=t[s],u[1]=t[s+1],a.push(u[0],u[1]),i){case"end":l[o]=h[o],l[1-o]=u[1-o],a.push(l[0],l[1]);break;case"middle":var d=[];l[o]=d[o]=(u[o]+h[o])/2,l[1-o]=u[1-o],d[1-o]=h[1-o],a.push(l[0],l[1]),a.push(d[0],d[1]);break;default:l[o]=u[o],l[1-o]=h[1-o],a.push(l[0],l[1])}return a.push(t[s++],t[s++]),a}function Cw(t,e,n){var i=t.getVisual("visualMeta");if(i&&i.length&&t.count()&&"cartesian2d"===e.type){for(var r,o=i.length-1;0<=o;o--){var a,s=t.getDimensionInfo(i[o].dimension);if("x"===(a=s&&s.coordDim)||"y"===a){r=i[o];break}}if(r){var l=e.getAxis(a),e=B(r.stops,function(t){return{coord:l.toGlobalCoord(l.dataToCoord(t.value)),color:t.color}}),u=e.length,h=r.outerColors.slice(),n=(u&&e[0].coord>e[u-1].coord&&(e.reverse(),h.reverse()),function(t,e){var n,i,r=[],o=t.length;function a(t,e,n){var i=t.coord;return{coord:n,color:xi((n-i)/(e.coord-i),[t.color,e.color])}}for(var s=0;s<o;s++){var l=t[s],u=l.coord;if(u<0)n=l;else{if(e<u){i?r.push(a(i,l,e)):n&&r.push(a(n,l,0),a(n,l,e));break}n&&(r.push(a(n,l,0)),n=null),r.push(l),i=l}}return r}(e,"x"===a?n.getWidth():n.getHeight())),c=n.length;if(!c&&u)return e[0].coord<0?h[1]||e[u-1].color:h[0]||e[0].color;var p=n[0].coord-10,u=n[c-1].coord+10,d=u-p;if(d<.001)return"transparent";O(n,function(t){t.offset=(t.coord-p)/d}),n.push({offset:c?n[c-1].offset:.5,color:h[1]||"transparent"}),n.unshift({offset:c?n[0].offset:.5,color:h[0]||"transparent"});e=new dh(0,0,0,0,n,!0);return e[a]=p,e[a+"2"]=u,e}}}function Iw(t,e,n){var t=t.get("showAllSymbol"),i="auto"===t;if(!t||i){var r,o,a=n.getAxesByScale("ordinal")[0];if(a)if(!i||!function(t,e){for(var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count(),r=(isNaN(i)&&(i=0),e.count()),o=Math.max(1,Math.round(r/5)),a=0;a<r;a+=o)if(1.5*$x.getSymbolSize(e,a)[t.isHorizontal()?1:0]>i)return;return 1}(a,e))return r=e.mapDimension(a.dim),o={},O(a.getViewLabels(),function(t){t=a.scale.getRawOrdinalNumber(t.tickValue);o[t]=1}),function(t){return!o.hasOwnProperty(e.get(r,t))}}}function kw(t){for(var e,n,i=t.length/2;0<i&&(e=t[2*i-2],n=t[2*i-1],isNaN(e)||isNaN(n));i--);return i-1}function Dw(t,e){return[t[2*e],t[2*e+1]]}function Aw(t){if(t.get(["endLabel","show"]))return 1;for(var e=0;e<tl.length;e++)if(t.get([tl[e],"endLabel","show"]))return 1}function Pw(n,i,e,t){var r,o,a,s,l,u,h,c,p;return _x(i,"cartesian2d")?(r=t.getModel("endLabel"),o=r.get("valueAnimation"),a=t.getData(),s={lastFrameIndex:0},l=Aw(t)?function(t,e){n._endLabelOnDuring(t,e,a,s,o,r,i)}:null,u=i.getBaseAxis().isHorizontal(),h=dx(i,e,t,function(){var t=n._endLabel;t&&e&&null!=s.originalX&&t.attr({x:s.originalX,y:s.originalY})},l),t.get("clip",!0)||(c=h.shape,p=Math.max(c.width,c.height),u?(c.y-=p,c.height+=2*p):(c.x-=p,c.width+=2*p)),l&&l(1,h),h):fx(i,e,t)}u(Rw,Lw=Kg),Rw.prototype.init=function(){var t=new Gr,e=new iw;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t,this._changePolyState=ct(this._changePolyState,this)},Rw.prototype.render=function(t,e,n){var i,r=t.coordinateSystem,o=this.group,a=t.getData(),s=t.getModel("lineStyle"),l=t.getModel("areaStyle"),u=a.getLayout("points")||[],h="polar"===r.type,c=this._coordSys,p=this._symbolDraw,d=this._polyline,f=this._polygon,g=this._lineGroup,e=!e.ssr&&t.get("animation"),y=!l.isEmpty(),m=l.get("origin"),v=ow(r,a,m),v=y&&function(t,e,n){if(!n.valueDim)return[];for(var i=e.count(),r=jv(2*i),o=0;o<i;o++){var a=aw(n,t,e,o);r[2*o]=a[0],r[2*o+1]=a[1]}return r}(r,a,v),_=t.get("showSymbol"),x=t.get("connectNulls"),w=_&&!h&&Iw(t,a,r),b=this._data,S=(b&&b.eachItemGraphicEl(function(t,e){t.__temp&&(o.remove(t),b.setItemGraphicEl(e,null))}),_||p.remove(),o.add(g),!h&&t.get("step")),M=(r&&r.getArea&&t.get("clip",!0)&&(null!=(i=r.getArea()).width?(i.x-=.1,i.y-=.1,i.width+=.2,i.height+=.2):i.r0&&(i.r0-=.5,i.r+=.5)),this._clipShapeForSymbol=i,Cw(a,r,n)||a.getVisual("style")[a.getVisual("drawType")]),c=(d&&c.type===r.type&&S===this._step?(y&&!f?f=this._newPolygon(u,v):f&&!y&&(g.remove(f),f=this._polygon=null),h||this._initOrUpdateEndLabel(t,r,Ip(M)),(c=g.getClipPath())?Dh(c,{shape:Pw(this,r,!1,t).shape},t):g.setClipPath(Pw(this,r,!0,t)),_&&p.updateData(a,{isIgnore:w,clipShape:i,disableAnimation:!0,getSymbolPoint:function(t){return[u[2*t],u[2*t+1]]}}),ww(this._stackedOnPoints,v)&&ww(this._points,u)||(e?this._doUpdateAnimation(a,v,r,n,S,m,x):(S&&(v=v&&Tw(v,u,r,S,x),u=Tw(u,null,r,S,x)),d.setShape({points:u}),f&&f.setShape({points:u,stackedOnPoints:v})))):(_&&p.updateData(a,{isIgnore:w,clipShape:i,disableAnimation:!0,getSymbolPoint:function(t){return[u[2*t],u[2*t+1]]}}),e&&this._initSymbolLabelAnimation(a,r,i),S&&(v=v&&Tw(v,u,r,S,x),u=Tw(u,null,r,S,x)),d=this._newPolyline(u),y?f=this._newPolygon(u,v):f&&(g.remove(f),f=this._polygon=null),h||this._initOrUpdateEndLabel(t,r,Ip(M)),g.setClipPath(Pw(this,r,!0,t))),t.getModel("emphasis")),n=c.get("focus"),_=c.get("blurScope"),p=c.get("disabled"),w=(d.useStyle(z(s.getLineStyle(),{fill:"none",stroke:M,lineJoin:"bevel"})),Bl(d,t,"lineStyle"),0<d.style.lineWidth&&"bolder"===t.get(["emphasis","lineStyle","width"])&&(d.getState("emphasis").style.lineWidth=+d.style.lineWidth+1),D(d).seriesIndex=t.seriesIndex,Nl(d,n,_,p),Mw(t.get("smooth"))),e=t.get("smoothMonotone"),T=(d.setShape({smooth:w,smoothMonotone:e,connectNulls:x}),f&&(i=a.getCalculationInfo("stackedOnSeries"),y=0,f.useStyle(z(l.getAreaStyle(),{fill:M,opacity:.7,lineJoin:"bevel",decal:a.getVisual("style").decal})),i&&(y=Mw(i.get("smooth"))),f.setShape({smooth:w,stackedOnSmooth:y,smoothMonotone:e,connectNulls:x}),Bl(f,t,"areaStyle"),D(f).seriesIndex=t.seriesIndex,Nl(f,n,_,p)),this._changePolyState);a.eachItemGraphicEl(function(t){t&&(t.onHoverStateChange=T)}),this._polyline.onHoverStateChange=T,this._data=a,this._coordSys=r,this._stackedOnPoints=v,this._points=u,this._step=S,this._valueOrigin=m,t.get("triggerLineEvent")&&(this.packEventData(t,d),f)&&this.packEventData(t,f)},Rw.prototype.packEventData=function(t,e){D(e).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},Rw.prototype.highlight=function(t,e,n,i){var r=t.getData(),o=Po(r,i);if(this._changePolyState("emphasis"),!(o instanceof Array)&&null!=o&&0<=o){var a=r.getLayout("points");if(!(l=r.getItemGraphicEl(o))){var s=a[2*o],a=a[2*o+1];if(isNaN(s)||isNaN(a))return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(s,a))return;var l,u=t.get("zlevel")||0,h=t.get("z")||0,s=((l=new $x(r,o)).x=s,l.y=a,l.setZ(u,h),l.getSymbolPath().getTextContent());s&&(s.zlevel=u,s.z=h,s.z2=this._polyline.z2+1),l.__temp=!0,r.setItemGraphicEl(o,l),l.stopSymbolAnimation(!0),this.group.add(l)}l.highlight()}else Kg.prototype.highlight.call(this,t,e,n,i)},Rw.prototype.downplay=function(t,e,n,i){var r,o=t.getData(),a=Po(o,i);this._changePolyState("normal"),null!=a&&0<=a?(r=o.getItemGraphicEl(a))&&(r.__temp?(o.setItemGraphicEl(a,null),this.group.remove(r)):r.downplay()):Kg.prototype.downplay.call(this,t,e,n,i)},Rw.prototype._changePolyState=function(t){var e=this._polygon;ml(this._polyline,t),e&&ml(e,t)},Rw.prototype._newPolyline=function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new dw({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(e),this._polyline=e},Rw.prototype._newPolygon=function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new _w({shape:{points:t,stackedOnPoints:e},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n},Rw.prototype._initSymbolLabelAnimation=function(t,l,u){var h,c,e=l.getBaseAxis(),p=e.inverse,e=("cartesian2d"===l.type?(h=e.isHorizontal(),c=!1):"polar"===l.type&&(h="angle"===e.dim,c=!0),t.hostModel),d=e.get("animationDuration"),f=(k(d)&&(d=d(null)),e.get("animationDelay")||0),g=k(f)?f(null):f;t.eachItemGraphicEl(function(t,e){var n,i,r,o,a,s=t;s&&(o=[t.x,t.y],a=i=n=void 0,u&&(a=c?(r=u,o=l.pointToCoord(o),h?(n=r.startAngle,i=r.endAngle,-o[1]/180*Math.PI):(n=r.r0,i=r.r,o[0])):h?(n=u.x,i=u.x+u.width,t.x):(n=u.y+u.height,i=u.y,t.y)),r=i===n?0:(a-n)/(i-n),p&&(r=1-r),o=k(f)?f(e):d*r+g,a=(t=s.getSymbolPath()).getTextContent(),s.attr({scaleX:0,scaleY:0}),s.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:o}),a&&a.animateFrom({style:{opacity:0}},{duration:300,delay:o}),t.disableLabelAnimation=!0)})},Rw.prototype._initOrUpdateEndLabel=function(t,e,n){var i,r,o,a=t.getModel("endLabel");Aw(t)?(i=t.getData(),r=this._polyline,(o=i.getLayout("points"))?(this._endLabel||((this._endLabel=new Ns({z2:200})).ignoreClip=!0,r.setTextContent(this._endLabel),r.disableLabelAnimation=!0),0<=(o=kw(o))&&(cc(r,pc(t,"endLabel"),{inheritColor:n,labelFetcher:t,labelDataIndex:o,defaultText:function(t,e,n){return null!=n?bx(i,n):xx(i,t)},enableTextSetter:!0},(n=a,o=(t=(t=e).getBaseAxis()).isHorizontal(),t=t.inverse,a=o?t?"right":"left":"center",o=o?"middle":t?"top":"bottom",{normal:{align:n.get("align")||a,verticalAlign:n.get("verticalAlign")||o}})),r.textConfig.position=null)):(r.removeTextContent(),this._endLabel=null)):this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},Rw.prototype._endLabelOnDuring=function(t,e,n,i,r,o,a){var s,l,u,h,c,p,d,f,g,y,m=this._endLabel,v=this._polyline;m&&(t<1&&null==i.originalX&&(i.originalX=m.x,i.originalY=m.y),s=n.getLayout("points"),g=(l=n.hostModel).get("connectNulls"),u=o.get("precision"),o=o.get("distance")||0,c=(a=a.getBaseAxis()).isHorizontal(),a=a.inverse,e=e.shape,h=(c?o:0)*(a?-1:1),o=(c?0:-o)*(a?-1:1),d=void 0,1<=(f=(p=(c=function(t,e,n){for(var i,r,o=t.length/2,a="x"===n?0:1,s=0,l=-1,u=0;u<o;u++)if(r=t[2*u+a],!isNaN(r)&&!isNaN(t[2*u+1-a])){if(0!==u){if(i<=e&&e<=r||e<=i&&r<=e){l=u;break}s=u}i=r}return{range:[s,l],t:(e-i)/(r-i)}}(s,a=a?c?e.x:e.y+e.height:c?e.x+e.width:e.y,e=c?"x":"y")).range)[1]-p[0])?(1<f&&!g?(y=Dw(s,p[0]),m.attr({x:y[0]+h,y:y[1]+o}),r&&(d=l.getRawValue(p[0]))):((y=v.getPointOn(a,e))&&m.attr({x:y[0]+h,y:y[1]+o}),f=l.getRawValue(p[0]),g=l.getRawValue(p[1]),r&&(d=Fo(n,u,f,g,c.t))),i.lastFrameIndex=p[0]):(y=Dw(s,v=1===t||0<i.lastFrameIndex?p[0]:0),r&&(d=l.getRawValue(v)),m.attr({x:y[0]+h,y:y[1]+o})),r)&&"function"==typeof(a=_c(m)).setLabelText&&a.setLabelText(d)},Rw.prototype._doUpdateAnimation=function(t,e,n,i,r,o,a){var s=this._polyline,l=this._polygon,u=t.hostModel,e=function(t,e,n,i,r,o){a=[],e.diff(t).add(function(t){a.push({cmd:"+",idx:t})}).update(function(t,e){a.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){a.push({cmd:"-",idx:t})}).execute();for(var a,s=a,l=[],u=[],h=[],c=[],p=[],d=[],f=[],g=ow(r,e,o),y=t.getLayout("points")||[],m=e.getLayout("points")||[],v=0;v<s.length;v++){var _=s[v],x=!0,w=void 0;switch(_.cmd){case"=":var b=2*_.idx,w=2*_.idx1,S=y[b],M=y[1+b],T=m[w],C=m[w+1];(isNaN(S)||isNaN(M))&&(S=T,M=C),l.push(S,M),u.push(T,C),h.push(n[b],n[1+b]),c.push(i[w],i[w+1]),f.push(e.getRawIndex(_.idx1));break;case"+":S=_.idx,M=g.dataDimsForPoint,T=r.dataToPoint([e.get(M[0],S),e.get(M[1],S)]),C=(w=2*S,l.push(T[0],T[1]),u.push(m[w],m[w+1]),aw(g,r,e,S));h.push(C[0],C[1]),c.push(i[w],i[w+1]),f.push(e.getRawIndex(S));break;case"-":x=!1}x&&(p.push(_),d.push(d.length))}d.sort(function(t,e){return f[t]-f[e]});for(var I=jv(o=l.length),k=jv(o),D=jv(o),A=jv(o),P=[],v=0;v<d.length;v++){var L=d[v],O=2*v,R=2*L;I[O]=l[R],I[1+O]=l[1+R],k[O]=u[R],k[1+O]=u[1+R],D[O]=h[R],D[1+O]=h[1+R],A[O]=c[R],A[1+O]=c[1+R],P[v]=p[L]}return{current:I,next:k,stackedOnCurrent:D,stackedOnNext:A,status:P}}(this._data,t,this._stackedOnPoints,e,this._coordSys,this._valueOrigin),h=e.current,c=e.stackedOnCurrent,p=e.next,d=e.stackedOnNext;if(r&&(c=Tw(e.stackedOnCurrent,e.current,n,r,a),h=Tw(e.current,null,n,r,a),d=Tw(e.stackedOnNext,e.next,n,r,a),p=Tw(e.next,null,n,r,a)),3e3<Sw(h,p)||l&&3e3<Sw(c,d))s.stopAnimation(),s.setShape({points:p}),l&&(l.stopAnimation(),l.setShape({points:p,stackedOnPoints:d}));else{s.shape.__points=e.current,s.shape.points=h;for(var f,n={shape:{points:p}},g=(e.current!==h&&(n.shape.__points=e.next),s.stopAnimation(),kh(s,n,u),l&&(l.setShape({points:h,stackedOnPoints:c}),l.stopAnimation(),kh(l,{shape:{stackedOnPoints:d}},u),s.shape.points!==l.shape.points)&&(l.shape.points=s.shape.points),[]),y=e.status,m=0;m<y.length;m++)"="===y[m].cmd&&(f=t.getItemGraphicEl(y[m].idx1))&&g.push({el:f,ptIdx:m});s.animators&&s.animators.length&&s.animators[0].during(function(){l&&l.dirtyShape();for(var t=s.shape.__points,e=0;e<g.length;e++){var n=g[e].el,i=2*g[e].ptIdx;n.x=t[i],n.y=t[1+i],n.markRedraw()}})}},Rw.prototype.remove=function(t){var n=this.group,i=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),i&&i.eachItemGraphicEl(function(t,e){t.__temp&&(n.remove(t),i.setItemGraphicEl(e,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},Rw.type="line";var Lw,Ow=Rw;function Rw(){return null!==Lw&&Lw.apply(this,arguments)||this}B_(function(t){var i;t.registerChartView(Ow),t.registerSeriesModel(Zx),t.registerLayout((i=!0,{seriesType:"line",plan:qg(),reset:function(t){var h,e,c,p,d,n=t.getData(),f=t.coordinateSystem,t=t.pipelineContext,g=i||t.large;if(f)return t=B(f.dimensions,function(t){return n.mapDimension(t)}).slice(0,2),h=t.length,e=n.getCalculationInfo("stackResultDimension"),Sv(n,t[0])&&(t[0]=e),Sv(n,t[1])&&(t[1]=e),c=n.getStore(),p=n.getDimensionIndex(t[0]),d=n.getDimensionIndex(t[1]),h&&{progress:function(t,e){for(var n=t.end-t.start,i=g&&jv(n*h),r=[],o=[],a=t.start,s=0;a<t.end;a++){var l,u=void 0;u=1===h?(l=c.get(p,a),f.dataToPoint(l,null,o)):(r[0]=c.get(p,a),r[1]=c.get(d,a),f.dataToPoint(r,null,o)),g?(i[s++]=u[0],i[s++]=u[1]):e.setItemLayout(a,u.slice())}g&&e.setLayout("points",i)}}}})),t.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),t=t.getModel("lineStyle").getLineStyle();t&&!t.stroke&&(t.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",t)}}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,sx("line"))});u(zw,Nw=g),zw.type="grid",zw.dependencies=["xAxis","yAxis"],zw.layoutMode="box",zw.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"};var Nw,Ew=zw;function zw(){return null!==Nw&&Nw.apply(this,arguments)||this}u(Vw,Bw=g),Vw.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",Eo).models[0]},Vw.type="cartesian2dAxis";var Bw,Fw=Vw;function Vw(){return null!==Bw&&Bw.apply(this,arguments)||this}at(Fw,R_);var Yo={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},Hy=d({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Yo),Yy=d({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},Yo),Hw={category:Hy,value:Yy,time:d({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},Yy),log:z({logBase:10},Yy)},Gw={value:1,category:1,time:1,log:1};function Ww(o,a,s,l){O(Gw,function(t,r){var e,n=d(d({},Hw[r],!0),l,!0),n=(u(i,e=s),i.prototype.mergeDefaultAndTheme=function(t,e){var n=Np(this),i=n?zp(t):{};d(t,e.getTheme().get(r+"Axis")),d(t,this.getDefaultOption()),t.type=Uw(t),n&&Ep(t,i,n)},i.prototype.optionUpdated=function(){"category"===this.option.type&&(this.__ordinalMeta=Dv.createByAxisModel(this))},i.prototype.getCategories=function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},i.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},i.type=a+"Axis."+r,i.defaultOption=n,i);function i(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=a+"Axis."+r,t}o.registerComponentModel(n)}),o.registerSubTypeDefaulter(a+"Axis",Uw)}function Uw(t){return t.type||(t.data?"category":"value")}function Xw(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}Xw.prototype.getAxis=function(t){return this._axes[t]},Xw.prototype.getAxes=function(){return B(this._dimList,function(t){return this._axes[t]},this)},Xw.prototype.getAxesByScale=function(e){return e=e.toLowerCase(),ut(this.getAxes(),function(t){return t.scale.type===e})},Xw.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)};var Yw=["x","y"];function qw(t){return"interval"===t.type||"time"===t.type}u(Kw,Zw=Xw),Kw.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t,e,n,i,r=this.getAxis("x").scale,o=this.getAxis("y").scale;qw(r)&&qw(o)&&(r=r.getExtent(),o=o.getExtent(),i=this.dataToPoint([r[0],o[0]]),e=this.dataToPoint([r[1],o[1]]),t=r[1]-r[0],n=o[1]-o[0],t)&&n&&(t=(e[0]-i[0])/t,e=(e[1]-i[1])/n,n=i[0]-r[0]*t,r=i[1]-o[0]*e,i=this._transform=[t,0,0,e,n,r],this._invTransform=Fe([],i))},Kw.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},Kw.prototype.containPoint=function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},Kw.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},Kw.prototype.containZone=function(t,e){var t=this.dataToPoint(t),e=this.dataToPoint(e),n=this.getArea(),e=new X(t[0],t[1],e[0]-t[0],e[1]-t[1]);return n.intersect(e)},Kw.prototype.dataToPoint=function(t,e,n){n=n||[];var i,r=t[0],o=t[1];return this._transform&&null!=r&&isFinite(r)&&null!=o&&isFinite(o)?ee(n,t,this._transform):(t=this.getAxis("x"),i=this.getAxis("y"),n[0]=t.toGlobalCoord(t.dataToCoord(r,e)),n[1]=i.toGlobalCoord(i.dataToCoord(o,e)),n)},Kw.prototype.clampData=function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),n=n.parse(t[0]),i=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),n),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),i),Math.max(o[0],o[1])),e},Kw.prototype.pointToData=function(t,e){var n,i,r=[];return this._invTransform?ee(r,t,this._invTransform):(n=this.getAxis("x"),i=this.getAxis("y"),r[0]=n.coordToData(n.toLocalCoord(t[0]),e),r[1]=i.coordToData(i.toLocalCoord(t[1]),e),r)},Kw.prototype.getOtherAxis=function(t){return this.getAxis("x"===t.dim?"y":"x")},Kw.prototype.getArea=function(t){t=t||0;var e=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),i=Math.min(e[0],e[1])-t,r=Math.min(n[0],n[1])-t,e=Math.max(e[0],e[1])-i+t,n=Math.max(n[0],n[1])-r+t;return new X(i,r,e,n)};var Zw,jw=Kw;function Kw(){var t=null!==Zw&&Zw.apply(this,arguments)||this;return t.type="cartesian2d",t.dimensions=Yw,t}u(Jw,$w=Pc),Jw.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},Jw.prototype.getGlobalExtent=function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},Jw.prototype.pointToData=function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},Jw.prototype.setCategorySortInfo=function(t){if("category"!==this.type)return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)};var $w,Qw=Jw;function Jw(t,e,n,i,r){t=$w.call(this,t,e,n)||this;return t.index=0,t.type=i||"value",t.position=r||"bottom",t}function tb(t,e,n){n=n||{};var t=t.coordinateSystem,i=e.axis,r={},o=i.getAxesOnZeroOf()[0],a=i.position,s=o?"onZero":a,i=i.dim,t=t.getRect(),t=[t.x,t.x+t.width,t.y,t.y+t.height],l={left:0,right:1,top:0,bottom:1,onZero:2},u=e.get("offset")||0,u="x"===i?[t[2]-u,t[3]+u]:[t[0]-u,t[1]+u],h=(o&&(h=o.toGlobalCoord(o.dataToCoord(0)),u[l.onZero]=Math.max(Math.min(h,u[1]),u[0])),r.position=["y"===i?u[l[s]]:t[0],"x"===i?u[l[s]]:t[3]],r.rotation=Math.PI/2*("x"===i?0:1),r.labelDirection=r.tickDirection=r.nameDirection={top:-1,bottom:1,left:-1,right:1}[a],r.labelOffset=o?u[l[a]]-u[l.onZero]:0,e.get(["axisTick","inside"])&&(r.tickDirection=-r.tickDirection),wt(n.labelInside,e.get(["axisLabel","inside"]))&&(r.labelDirection=-r.labelDirection),e.get(["axisLabel","rotate"]));return r.labelRotate="top"===s?-h:h,r.z2=1,r}function eb(t){return"cartesian2d"===t.get("coordinateSystem")}function nb(i){var r={xAxisModel:null,yAxisModel:null};return O(r,function(t,e){var n=e.replace(/Model$/,""),n=i.getReferringComponents(n,Eo).models[0];r[e]=n}),r}var ib=Math.log;ob.prototype.getRect=function(){return this._rect},ob.prototype.update=function(t,e){var n=this._axesMap;function i(t){var d,e=ht(t),n=e.length;if(n){for(var i=[],r=n-1;0<=r;r--){var o=t[+e[r]],a=o.model,s=o.scale;Lv(s)&&a.get("alignTicks")&&null==a.get("interval")?i.push(o):(I_(s,a),Lv(s)&&(d=o))}i.length&&(d||I_((d=i.pop()).scale,d.model),O(i,function(t){var e=t.scale,t=t.model,n=d.scale,i=Xv.prototype,r=i.getTicks.call(n),o=i.getTicks.call(n,!0),a=r.length-1,n=i.getInterval.call(n),s=(t=C_(e,t)).extent,l=t.fixMin,t=t.fixMax,u=("log"===e.type&&(u=ib(e.base),s=[ib(s[0])/u,ib(s[1])/u]),e.setExtent(s[0],s[1]),e.calcNiceExtent({splitNumber:a,fixMin:l,fixMax:t}),i.getExtent.call(e)),h=(l&&(s[0]=u[0]),t&&(s[1]=u[1]),i.getInterval.call(e)),c=s[0],p=s[1];if(l&&t)h=(p-c)/a;else if(l)for(p=s[0]+h*a;p<s[1]&&isFinite(p)&&isFinite(s[1]);)h=Rv(h),p=s[0]+h*a;else if(t)for(c=s[1]-h*a;c>s[0]&&isFinite(c)&&isFinite(s[0]);)h=Rv(h),c=s[1]-h*a;else{u=(h=a<e.getTicks().length-1?Rv(h):h)*a;(c=no((p=Math.ceil(s[1]/h)*h)-u))<0&&0<=s[0]?(c=0,p=no(u)):0<p&&s[1]<=0&&(p=0,c=-no(u))}l=(r[0].value-o[0].value)/n,t=(r[a].value-o[a].value)/n,i.setExtent.call(e,c+h*l,p+h*t),i.setInterval.call(e,h),(l||t)&&i.setNiceExtent.call(e,c+h,p-h)}))}}this._updateScale(t,this.model),i(n.x),i(n.y);var r={};O(n.x,function(t){sb(n,"y",t,r)}),O(n.y,function(t){sb(n,"x",t,r)}),this.resize(this.model,e)},ob.prototype.resize=function(t,e,n){var i=t.getBoxLayoutParams(),n=!n&&t.get("containLabel"),a=Rp(i,{width:e.getWidth(),height:e.getHeight()}),r=(this._rect=a,this._axesList);function o(){O(r,function(t){var e,n,i=t.isHorizontal(),r=i?[0,a.width]:[0,a.height],o=t.inverse?1:0;t.setExtent(r[o],r[1-o]),r=t,e=i?a.x:a.y,o=r.getExtent(),n=o[0]+o[1],r.toGlobalCoord="x"===r.dim?function(t){return t+e}:function(t){return n-t+e},r.toLocalCoord="x"===r.dim?function(t){return t-e}:function(t){return n-t+e}})}o(),n&&(O(r,function(t){var e,n,i;t.model.get(["axisLabel","inside"])||(e=P_(t))&&(n=t.isHorizontal()?"height":"width",i=t.model.get(["axisLabel","margin"]),a[n]-=e[n]+i,"top"===t.position?a.y+=e.height+i:"left"===t.position&&(a.x+=e.width+i))}),o()),O(this._coordsList,function(t){t.calcAffineTransform()})},ob.prototype.getAxis=function(t,e){t=this._axesMap[t];if(null!=t)return t[e||0]},ob.prototype.getAxes=function(){return this._axesList.slice()},ob.prototype.getCartesian=function(t,e){if(null!=t&&null!=e)return this._coordsMap["x"+t+"y"+e];R(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,i=this._coordsList;n<i.length;n++)if(i[n].getAxis("x").index===t||i[n].getAxis("y").index===e)return i[n]},ob.prototype.getCartesians=function(){return this._coordsList.slice()},ob.prototype.convertToPixel=function(t,e,n){e=this._findConvertTarget(e);return e.cartesian?e.cartesian.dataToPoint(n):e.axis?e.axis.toGlobalCoord(e.axis.dataToCoord(n)):null},ob.prototype.convertFromPixel=function(t,e,n){e=this._findConvertTarget(e);return e.cartesian?e.cartesian.pointToData(n):e.axis?e.axis.coordToData(e.axis.toLocalCoord(n)):null},ob.prototype._findConvertTarget=function(t){var e,n,i=t.seriesModel,r=t.xAxisModel||i&&i.getReferringComponents("xAxis",Eo).models[0],o=t.yAxisModel||i&&i.getReferringComponents("yAxis",Eo).models[0],t=t.gridModel,a=this._coordsList;return i?I(a,e=i.coordinateSystem)<0&&(e=null):r&&o?e=this.getCartesian(r.componentIndex,o.componentIndex):r?n=this.getAxis("x",r.componentIndex):o?n=this.getAxis("y",o.componentIndex):t&&t.coordinateSystem===this&&(e=this._coordsList[0]),{cartesian:e,axis:n}},ob.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},ob.prototype._initCartesian=function(o,t,e){var a=this,s=this,l={left:!1,right:!1,top:!1,bottom:!1},u={x:{},y:{}},h={x:0,y:0};function n(r){return function(t,e){var n,i;ab(t,o)&&(n=t.get("position"),"x"===r?"top"!==n&&"bottom"!==n&&(n=l.bottom?"top":"bottom"):"left"!==n&&"right"!==n&&(n=l.left?"right":"left"),l[n]=!0,i="category"===(n=new Qw(r,k_(t),[0,0],t.get("type"),n)).type,n.onBand=i&&t.get("boundaryGap"),n.inverse=t.get("inverse"),(t.axis=n).model=t,n.grid=s,n.index=e,s._axesList.push(n),u[r][e]=n,h[r]++)}}t.eachComponent("xAxis",n("x"),this),t.eachComponent("yAxis",n("y"),this),h.x&&h.y?O((this._axesMap=u).x,function(i,r){O(u.y,function(t,e){var e="x"+r+"y"+e,n=new jw(e);n.master=a,n.model=o,a._coordsMap[e]=n,a._coordsList.push(n),n.addAxis(i),n.addAxis(t)})}):(this._axesMap={},this._axesList=[])},ob.prototype._updateScale=function(t,i){function r(e,n){var i,t,r;O((i=e,t=n.dim,r={},O(i.mapDimensionsAll(t),function(t){r[Mv(i,t)]=!0}),ht(r)),function(t){n.scale.unionExtentFromData(e,t)})}O(this._axesList,function(t){var e;t.scale.setExtent(1/0,-1/0),"category"===t.type&&(e=t.model.get("categorySortInfo"),t.scale.setSortInfo(e))}),t.eachSeries(function(t){var e,n;eb(t)&&(n=(e=nb(t)).xAxisModel,e=e.yAxisModel,ab(n,i))&&ab(e,i)&&(n=this.getCartesian(n.componentIndex,e.componentIndex),e=t.getData(),t=n.getAxis("x"),n=n.getAxis("y"),r(e,t),r(e,n))},this)},ob.prototype.getTooltipAxes=function(n){var i=[],r=[];return O(this.getCartesians(),function(t){var e=null!=n&&"auto"!==n?t.getAxis(n):t.getBaseAxis(),t=t.getOtherAxis(e);I(i,e)<0&&i.push(e),I(r,t)<0&&r.push(t)}),{baseAxes:i,otherAxes:r}},ob.create=function(i,r){var o=[];return i.eachComponent("grid",function(t,e){var n=new ob(t,i,r);n.name="grid_"+e,n.resize(t,r,!0),t.coordinateSystem=n,o.push(n)}),i.eachSeries(function(t){var e,n,i;eb(t)&&(e=(n=nb(t)).xAxisModel,n=n.yAxisModel,i=e.getCoordSysModel().coordinateSystem,t.coordinateSystem=i.getCartesian(e.componentIndex,n.componentIndex))}),o},ob.dimensions=Yw;var rb=ob;function ob(t,e,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=Yw,this._initCartesian(t,e,n),this.model=t}function ab(t,e){return t.getCoordSysModel()===e}function sb(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],t=n.model,e=t.get(["axisLine","onZero"]),n=t.get(["axisLine","onZeroAxisIndex"]);if(e){if(null!=n)lb(o[n])&&(r=o[n]);else for(var a in o)if(o.hasOwnProperty(a)&&lb(o[a])&&!i[s(o[a])]){r=o[a];break}r&&(i[s(r)]=!0)}function s(t){return t.dim+"_"+t.index}}function lb(t){return t&&"category"!==t.type&&"time"!==t.type&&(e=(t=(t=t).scale.getExtent())[0],t=t[1],!(0<e&&0<t||e<0&&t<0));var e}var ub=Math.PI,hb=(cb.prototype.hasBuilder=function(t){return!!pb[t]},cb.prototype.add=function(t){pb[t](this.opt,this.axisModel,this.group,this._transformGroup)},cb.prototype.getGroup=function(){return this.group},cb.innerTextLayout=function(t,e,n){var i,e=ao(e-t),t=so(e)?(i=0<n?"top":"bottom","center"):so(e-ub)?(i=0<n?"bottom":"top","center"):(i="middle",0<e&&e<ub?0<n?"right":"left":0<n?"left":"right");return{rotation:e,textAlign:t,textVerticalAlign:i}},cb.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},cb.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},cb);function cb(t,e){this.group=new Gr,this.opt=e,this.axisModel=t,z(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});t=new Gr({x:e.position[0],y:e.position[1],rotation:e.rotation});t.updateTransform(),this._transformGroup=t}var pb={axisLine:function(i,t,r,e){var o,a,s,l,u,h,c,n=t.get(["axisLine","show"]);(n="auto"===n&&i.handleAutoShown?i.handleAutoShown("axisLine"):n)&&(n=t.axis.getExtent(),e=e.transform,o=[n[0],0],a=[n[1],0],s=a[0]<o[0],e&&(ee(o,o,e),ee(a,a,e)),l=L({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),Zh((n=new ju({shape:{x1:o[0],y1:o[1],x2:a[0],y2:a[1]},style:l,strokeContainThreshold:i.strokeContainThreshold||5,silent:!0,z2:1})).shape,n.style.lineWidth),n.anid="line",r.add(n),null!=(u=t.get(["axisLine","symbol"])))&&(e=t.get(["axisLine","symbolSize"]),V(u)&&(u=[u,u]),(V(e)||H(e))&&(e=[e,e]),n=Qy(t.get(["axisLine","symbolOffset"])||0,e),h=e[0],c=e[1],O([{rotate:i.rotation+Math.PI/2,offset:n[0],r:0},{rotate:i.rotation-Math.PI/2,offset:n[1],r:Math.sqrt((o[0]-a[0])*(o[0]-a[0])+(o[1]-a[1])*(o[1]-a[1]))}],function(t,e){var n;"none"!==u[e]&&null!=u[e]&&(e=$y(u[e],-h/2,-c/2,h,c,l.stroke,!0),n=t.r+t.offset,e.attr({rotation:t.rotate,x:(t=s?a:o)[0]+n*Math.cos(i.rotation),y:t[1]-n*Math.sin(i.rotation),silent:!0,z2:11}),r.add(e))}))},axisTickLabel:function(t,e,n,i){var r,o,a,s,l,u=function(t,e,n,i){var r=n.axis,o=n.getModel("axisTick"),a=o.get("show");"auto"===a&&i.handleAutoShown&&(a=i.handleAutoShown("axisTick"));if(a&&!r.scale.isBlank()){for(var a=o.getModel("lineStyle"),i=i.tickDirection*o.get("length"),s=yb(r.getTicksCoords(),e.transform,i,z(a.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),l=0;l<s.length;l++)t.add(s[l]);return s}}(n,i,e,t),h=function(g,y,m,v){var _,x,w,b,S,M,T,C,I=m.axis,t=wt(v.axisLabelShow,m.get(["axisLabel","show"]));if(t&&!I.scale.isBlank())return _=m.getModel("axisLabel"),x=_.get("margin"),w=I.getViewLabels(),t=(wt(v.labelRotate,_.get("rotate"))||0)*ub/180,b=hb.innerTextLayout(v.rotation,t,v.labelDirection),S=m.getCategories&&m.getCategories(!0),M=[],T=hb.isLabelSilent(m),C=m.get("triggerEvent"),O(w,function(t,e){var n="ordinal"===I.scale.type?I.scale.getRawOrdinalNumber(t.tickValue):t.tickValue,i=t.formattedLabel,r=t.rawLabel,o=_,a=(o=S&&S[n]&&R(a=S[n])&&a.textStyle?new Rc(a.textStyle,_,m.ecModel):o).getTextColor()||m.get(["axisLine","lineStyle","color"]),s=I.dataToCoord(n),l=o.getShallow("align",!0)||b.textAlign,u=N(o.getShallow("alignMinLabel",!0),l),h=N(o.getShallow("alignMaxLabel",!0),l),c=o.getShallow("verticalAlign",!0)||o.getShallow("baseline",!0)||b.textVerticalAlign,p=N(o.getShallow("verticalAlignMinLabel",!0),c),d=N(o.getShallow("verticalAlignMaxLabel",!0),c),f=new Ns({x:s,y:v.labelOffset+v.labelDirection*x,rotation:b.rotation,silent:T,z2:10+(t.level||0),style:dc(o,{text:i,align:0===e?u:e===w.length-1?h:l,verticalAlign:0===e?p:e===w.length-1?d:c,fill:k(a)?a("category"===I.type?r:"value"===I.type?n+"":n,e):a})});f.anid="label_"+n,rc({el:f,componentModel:m,itemName:i,formatterParamsExtra:{isTruncated:function(){return f.isTruncated},value:r,tickIndex:e}}),C&&((s=hb.makeAxisEventDataBase(m)).targetType="axisLabel",s.value=r,s.tickIndex=e,"category"===I.type&&(s.dataIndex=n),D(f).eventData=s),y.add(f),f.updateTransform(),M.push(f),g.add(f),f.decomposeTransform()}),M}(n,i,e,t),c=(o=h,u=u,O_((r=e).axis)||(d=r.get(["axisLabel","showMinLabel"]),r=r.get(["axisLabel","showMaxLabel"]),u=u||[],y=(o=o||[])[0],f=o[1],a=o[o.length-1],o=o[o.length-2],s=u[0],g=u[1],l=u[u.length-1],u=u[u.length-2],!1===d?(db(y),db(s)):fb(y,f)&&(d?(db(f),db(g)):(db(y),db(s))),!1===r?(db(a),db(l)):fb(o,a)&&(r?(db(o),db(u)):(db(a),db(l)))),n),p=i,d=e,f=t.tickDirection,g=d.axis,y=d.getModel("minorTick");if(y.get("show")&&!g.scale.isBlank()){var m=g.getMinorTicksCoords();if(m.length)for(var g=y.getModel("lineStyle"),v=f*y.get("length"),_=z(g.getLineStyle(),z(d.getModel("axisTick").getLineStyle(),{stroke:d.get(["axisLine","lineStyle","color"])})),x=0;x<m.length;x++)for(var w=yb(m[x],p.transform,v,_,"minorticks_"+x),b=0;b<w.length;b++)c.add(w[b])}e.get(["axisLabel","hideOverlap"])&&R1(L1(B(h,function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}})))},axisName:function(t,e,n,i){var r,o,a,s,l,u,h,c,p=wt(t.axisName,e.get("name"));p&&(c=e.get("nameLocation"),l=t.nameDirection,r=e.getModel("nameTextStyle"),u=e.get("nameGap")||0,o=(h=e.axis.getExtent())[0]>h[1]?-1:1,o=["start"===c?h[0]-o*u:"end"===c?h[1]+o*u:(h[0]+h[1])/2,gb(c)?t.labelOffset+l*u:0],null!=(u=e.get("nameRotate"))&&(u=u*ub/180),gb(c)?a=hb.innerTextLayout(t.rotation,null!=u?u:t.rotation,l):(a=function(t,e,n,i){var r,n=ao(n-t),t=i[0]>i[1],i="start"===e&&!t||"start"!==e&&t;e=so(n-ub/2)?(r=i?"bottom":"top","center"):so(n-1.5*ub)?(r=i?"top":"bottom","center"):(r="middle",n<1.5*ub&&ub/2<n?i?"left":"right":i?"right":"left");return{rotation:n,textAlign:e,textVerticalAlign:r}}(t.rotation,c,u||0,h),null!=(s=t.axisNameAvailableWidth)&&(s=Math.abs(s/Math.sin(a.rotation)),isFinite(s)||(s=null))),l=r.getFont(),u=(c=e.get("nameTruncate",!0)||{}).ellipsis,h=wt(t.nameTruncateMaxWidth,c.maxWidth,s),rc({el:t=new Ns({x:o[0],y:o[1],rotation:a.rotation,silent:hb.isLabelSilent(e),style:dc(r,{text:p,font:l,overflow:"truncate",width:h,ellipsis:u,fill:r.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:r.get("align")||a.textAlign,verticalAlign:r.get("verticalAlign")||a.textVerticalAlign}),z2:1}),componentModel:e,itemName:p}),t.__fullText=p,t.anid="name",e.get("triggerEvent")&&((c=hb.makeAxisEventDataBase(e)).targetType="axisName",c.name=p,D(t).eventData=c),i.add(t),t.updateTransform(),n.add(t),t.decomposeTransform())}};function db(t){t&&(t.ignore=!0)}function fb(t,e){var n,i=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(i&&r)return ze(n=Oe([]),n,-t.rotation),i.applyTransform(Ne([],n,t.getLocalTransform())),r.applyTransform(Ne([],n,e.getLocalTransform())),i.intersect(r)}function gb(t){return"middle"===t||"center"===t}function yb(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord,u=(a[0]=u,s[a[1]=0]=u,s[1]=n,e&&(ee(a,a,e),ee(s,s,e)),new ju({shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0}));Zh(u.shape,u.style.lineWidth),u.anid=r+"_"+t[l].tickValue,o.push(u)}return o}function mb(t,e){var h,c,r,p,d,f,o,n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return h=n,e=e,r=(c=t).getComponent("tooltip"),p=c.getComponent("axisPointer"),d=p.get("link",!0)||[],f=[],O(e.getCoordinateSystems(),function(s){var l,u,t,e,n;function i(t,e,n){var i,r,o=n.model.getModel("axisPointer",p),a=o.get("show");a&&("auto"!==a||t||xb(o))&&(null==e&&(e=o.get("triggerTooltip")),a=(o=t?function(t,e,n,i,r,o){var a=e.getModel("axisPointer"),s={};O(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){s[t]=y(a.get(t))}),s.snap="category"!==t.type&&!!o,"cross"===a.get("type")&&(s.type="line");e=s.label||(s.label={});null==e.show&&(e.show=!1),"cross"===r&&(r=a.get(["label","show"]),e.show=null==r||r,o||(r=s.lineStyle=a.get("crossStyle"))&&z(e,r.textStyle));return t.model.getModel("axisPointer",new Rc(s,n,i))}(n,u,p,c,t,e):o).get("snap"),t=o.get("triggerEmphasis"),i=wb(n.model),r=e||a||"category"===n.type,e=h.axesInfo[i]={key:i,axis:n,coordSys:s,axisPointerModel:o,triggerTooltip:e,triggerEmphasis:t,involveSeries:r,snap:a,useHandle:xb(o),seriesModels:[],linkGroup:null},l[i]=e,h.seriesInvolved=h.seriesInvolved||r,null!=(t=function(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(vb(o[i+"AxisId"],n.id)||vb(o[i+"AxisIndex"],n.componentIndex)||vb(o[i+"AxisName"],n.name))return r}}(d,n)))&&((a=f[t]||(f[t]={axesInfo:{}})).axesInfo[i]=e,a.mapper=d[t].mapper,e.linkGroup=a)}s.axisPointerEnabled&&(t=wb(s.model),l=h.coordSysAxesInfo[t]={},u=(h.coordSysMap[t]=s).model.getModel("tooltip",r),O(s.getAxes(),pt(i,!1,null)),s.getTooltipAxes)&&r&&u.get("show")&&(t="axis"===u.get("trigger"),e="cross"===u.get(["axisPointer","type"]),n=s.getTooltipAxes(u.get(["axisPointer","axis"])),(t||e)&&O(n.baseAxes,pt(i,!e||"cross",t)),e)&&O(n.otherAxes,pt(i,"cross",!1))}),n.seriesInvolved&&(o=n,t.eachSeries(function(n){var i=n.coordinateSystem,t=n.get(["tooltip","trigger"],!0),e=n.get(["tooltip","show"],!0);i&&"none"!==t&&!1!==t&&"item"!==t&&!1!==e&&!1!==n.get(["axisPointer","show"],!0)&&O(o.coordSysAxesInfo[wb(i.model)],function(t){var e=t.axis;i.getAxis(e.dim)===e&&(t.seriesModels.push(n),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=n.getData().count())})})),n}function vb(t,e){return"all"===t||F(t)&&0<=I(t,e)||t===e}function _b(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[wb(t)]}function xb(t){return!!t.get(["handle","show"])}function wb(t){return t.type+"||"+t.id}var bb,Sb={},Mb=(u(Tb,bb=Xg),Tb.prototype.render=function(t,e,n,i){var r,o,a,s,l,u;this.axisPointerClass&&(r=_b(r=t))&&(l=r.axisPointerModel,o=r.axis.scale,a=l.option,u=l.get("status"),null!=(s=l.get("value"))&&(s=o.parse(s)),l=xb(l),null==u&&(a.status=l?"show":"hide"),(u=o.getExtent().slice())[0]>u[1]&&u.reverse(),(s=null==s||s>u[1]?u[1]:s)<u[0]&&(s=u[0]),a.value=s,l)&&(a.status=r.axis.scale.isBlank()?"hide":"show"),bb.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(t,n,!0)},Tb.prototype.updateAxisPointer=function(t,e,n,i){this._doUpdateAxisPointerClass(t,n,!1)},Tb.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},Tb.prototype.dispose=function(t,e){this._disposeAxisPointer(e),bb.prototype.dispose.apply(this,arguments)},Tb.prototype._doUpdateAxisPointerClass=function(t,e,n){var i,r=Tb.getAxisPointerClass(this.axisPointerClass);r&&((i=(i=_b(i=t))&&i.axisPointerModel)?(this._axisPointer||(this._axisPointer=new r)).render(t,i,e,n):this._disposeAxisPointer(e))},Tb.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},Tb.registerAxisPointerClass=function(t,e){Sb[t]=e},Tb.getAxisPointerClass=function(t){return t&&Sb[t]},Tb.type="axis",Tb);function Tb(){var t=null!==bb&&bb.apply(this,arguments)||this;return t.type=Tb.type,t}var Cb=Lo();var Ib,kb=["axisLine","axisTickLabel","axisName"],Db=["splitArea","splitLine","minorSplitLine"],eh=(u(Ab,Ib=Mb),Ab.prototype.render=function(i,t,e,n){this.group.removeAll();var r,o,a=this._axisGroup;this._axisGroup=new Gr,this.group.add(this._axisGroup),i.get("show")&&(o=tb(r=i.getCoordSysModel(),i),o=new hb(i,L({handleAutoShown:function(t){for(var e=r.coordinateSystem.getCartesians(),n=0;n<e.length;n++)if(Lv(e[n].getOtherAxis(i.axis).scale))return!0;return!1}},o)),O(kb,o.add,o),this._axisGroup.add(o.getGroup()),O(Db,function(t){i.get([t,"show"])&&Lb[t](this,this._axisGroup,i,r)},this),n&&"changeAxisOrder"===n.type&&n.isInitSort||Jh(a,this._axisGroup,i),Ib.prototype.render.call(this,i,t,e,n))},Ab.prototype.remove=function(){Cb(this).splitAreaColors=null},Ab.type="cartesianAxis",Ab);function Ab(){var t=null!==Ib&&Ib.apply(this,arguments)||this;return t.type=Ab.type,t.axisPointerClass="CartesianAxisPointer",t}var Pb,Lb={splitLine:function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank())for(var n=n.getModel("splitLine"),o=n.getModel("lineStyle"),a=o.get("color"),s=!1!==n.get("showMinLine"),l=!1!==n.get("showMaxLine"),a=F(a)?a:[a],u=i.coordinateSystem.getRect(),h=r.isHorizontal(),c=0,p=r.getTicksCoords({tickModel:n}),d=[],f=[],g=o.getLineStyle(),y=0;y<p.length;y++){var m,v=r.toGlobalCoord(p[y].coord);0===y&&!s||y===p.length-1&&!l||(m=p[y].tickValue,h?(d[0]=v,d[1]=u.y,f[0]=v,f[1]=u.y+u.height):(d[0]=u.x,d[1]=v,f[0]=u.x+u.width,f[1]=v),v=c++%a.length,Zh((m=new ju({anid:null!=m?"line_"+m:null,autoBatch:!0,shape:{x1:d[0],y1:d[1],x2:f[0],y2:f[1]},style:z({stroke:a[v]},g),silent:!0})).shape,g.lineWidth),e.add(m))}},minorSplitLine:function(t,e,n,i){var r=n.axis,n=n.getModel("minorSplitLine").getModel("lineStyle"),o=i.coordinateSystem.getRect(),a=r.isHorizontal(),s=r.getMinorTicksCoords();if(s.length)for(var l=[],u=[],h=n.getLineStyle(),c=0;c<s.length;c++)for(var p=0;p<s[c].length;p++){var d=r.toGlobalCoord(s[c][p].coord),d=(a?(l[0]=d,l[1]=o.y,u[0]=d,u[1]=o.y+o.height):(l[0]=o.x,l[1]=d,u[0]=o.x+o.width,u[1]=d),new ju({anid:"minor_line_"+s[c][p].tickValue,autoBatch:!0,shape:{x1:l[0],y1:l[1],x2:u[0],y2:u[1]},style:h,silent:!0}));Zh(d.shape,h.lineWidth),e.add(d)}},splitArea:function(t,e,n,i){var r=e,e=i,o=(i=n).axis;if(!o.scale.isBlank()){var i=i.getModel("splitArea"),n=i.getModel("areaStyle"),a=n.get("color"),s=e.coordinateSystem.getRect(),l=o.getTicksCoords({tickModel:i,clamp:!0});if(l.length){var u=a.length,h=Cb(t).splitAreaColors,c=E(),p=0;if(h)for(var d=0;d<l.length;d++){var f=h.get(l[d].tickValue);if(null!=f){p=(f+(u-1)*d)%u;break}}for(var g=o.toGlobalCoord(l[0].coord),y=n.getAreaStyle(),a=F(a)?a:[a],d=1;d<l.length;d++){var m=o.toGlobalCoord(l[d].coord),v=void 0,_=void 0,x=void 0,w=void 0,g=o.isHorizontal()?(v=g,_=s.y,w=s.height,v+(x=m-v)):(v=s.x,_=g,x=s.width,_+(w=m-_)),m=l[d-1].tickValue;null!=m&&c.set(m,p),r.add(new As({anid:null!=m?"area_"+m:null,shape:{x:v,y:_,width:x,height:w},style:z({fill:a[p]},y),autoBatch:!0,silent:!0})),p=(p+1)%u}Cb(t).splitAreaColors=c}}}},Ob=(u(Rb,Pb=eh),Rb.type="xAxis",Rb);function Rb(){var t=null!==Pb&&Pb.apply(this,arguments)||this;return t.type=Rb.type,t}u(zb,Nb=eh),zb.type="yAxis";var Nb,Eb=zb;function zb(){var t=null!==Nb&&Nb.apply(this,arguments)||this;return t.type=Ob.type,t}u(Vb,Bb=Xg),Vb.prototype.render=function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new As({shape:t.coordinateSystem.getRect(),style:z({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},Vb.type="grid";var Bb,Fb=Vb;function Vb(){var t=null!==Bb&&Bb.apply(this,arguments)||this;return t.type="grid",t}var Hb={offset:0};B_(function(t){t.registerComponentView(Fb),t.registerComponentModel(Ew),t.registerCoordinateSystem("cartesian2d",rb),Ww(t,"x",Fw,Hb),Ww(t,"y",Fw,Hb),t.registerComponentView(Ob),t.registerComponentView(Eb),t.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})});u(Ub,Gb=g),Ub.type="title",Ub.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}};var Gb,Wb=Ub;function Ub(){var t=null!==Gb&&Gb.apply(this,arguments)||this;return t.type=Ub.type,t.layoutMode={type:"box",ignoreSize:!0},t}u(qb,Xb=Xg),qb.prototype.render=function(t,e,n){var i,r,o,a,s,l,u,h,c;this.group.removeAll(),t.get("show")&&(i=this.group,u=t.getModel("textStyle"),r=t.getModel("subtextStyle"),h=t.get("textAlign"),c=N(t.get("textBaseline"),t.get("textVerticalAlign")),s=(u=new Ns({style:dc(u,{text:t.get("text"),fill:u.getTextColor()},{disableBox:!0}),z2:10})).getBoundingRect(),l=t.get("subtext"),r=new Ns({style:dc(r,{text:l,fill:r.getTextColor(),y:s.height+t.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),o=t.get("link"),a=t.get("sublink"),s=t.get("triggerEvent",!0),u.silent=!o&&!s,r.silent=!a&&!s,o&&u.on("click",function(){kp(o,"_"+t.get("target"))}),a&&r.on("click",function(){kp(a,"_"+t.get("subtarget"))}),D(u).eventData=D(r).eventData=s?{componentType:"title",componentIndex:t.componentIndex}:null,i.add(u),l&&i.add(r),s=i.getBoundingRect(),(l=t.getBoxLayoutParams()).width=s.width,l.height=s.height,l=Rp(l,{width:n.getWidth(),height:n.getHeight()},t.get("padding")),h||("right"===(h="middle"===(h=t.get("left")||t.get("right"))?"center":h)?l.x+=l.width:"center"===h&&(l.x+=l.width/2)),c||("bottom"===(c="center"===(c=t.get("top")||t.get("bottom"))?"middle":c)?l.y+=l.height:"middle"===c&&(l.y+=l.height/2),c=c||"top"),i.x=l.x,i.y=l.y,i.markRedraw(),u.setStyle(n={align:h,verticalAlign:c}),r.setStyle(n),s=i.getBoundingRect(),u=l.margin,(h=t.getItemStyle(["color","opacity"])).fill=t.get("backgroundColor"),c=new As({shape:{x:s.x-u[3],y:s.y-u[0],width:s.width+u[1]+u[3],height:s.height+u[0]+u[2],r:t.get("borderRadius")},style:h,subPixelOptimize:!0,silent:!0}),i.add(c))},qb.type="title";var Xb,Yb=qb;function qb(){var t=null!==Xb&&Xb.apply(this,arguments)||this;return t.type=qb.type,t}B_(function(t){t.registerComponentModel(Wb),t.registerComponentView(Yb)});u(Kb,Zb=g),Kb.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n),t.selected=t.selected||{},this._updateSelector(t)},Kb.prototype.mergeOption=function(t,e){Zb.prototype.mergeOption.call(this,t,e),this._updateSelector(t)},Kb.prototype._updateSelector=function(t){var n=t.selector,i=this.ecModel;F(n=!0===n?t.selector=["all","inverse"]:n)&&O(n,function(t,e){V(t)&&(t={type:t}),n[e]=d(t,(e=i,"all"===(t=t.type)?{type:"all",title:e.getLocaleModel().get(["legend","selector","all"])}:"inverse"===t?{type:"inverse",title:e.getLocaleModel().get(["legend","selector","inverse"])}:void 0))})},Kb.prototype.optionUpdated=function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,n=0;n<t.length;n++){var i=t[n].get("name");if(this.isSelected(i)){this.select(i),e=!0;break}}e||this.select(t[0].get("name"))}},Kb.prototype._updateData=function(i){var r=[],o=[],t=(i.eachRawSeries(function(t){var e,n=t.name;o.push(n),t.legendVisualProvider&&(n=t.legendVisualProvider.getAllNames(),i.isSeriesFiltered(t)||(o=o.concat(n)),n.length)?r=r.concat(n):e=!0,e&&ko(t)&&r.push(t.name)}),this._availableNames=o,this.get("data")||r),e=E(),t=B(t,function(t){return(V(t)||H(t))&&(t={name:t}),e.get(t.name)?null:(e.set(t.name,!0),new Rc(t,this,this.ecModel))},this);this._data=ut(t,function(t){return!!t})},Kb.prototype.getData=function(){return this._data},Kb.prototype.select=function(t){var e=this.option.selected;"single"===this.get("selectedMode")&&O(this._data,function(t){e[t.get("name")]=!1}),e[t]=!0},Kb.prototype.unSelect=function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},Kb.prototype.toggleSelected=function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},Kb.prototype.allSelect=function(){var t=this._data,e=this.option.selected;O(t,function(t){e[t.get("name",!0)]=!0})},Kb.prototype.inverseSelect=function(){var t=this._data,e=this.option.selected;O(t,function(t){t=t.get("name",!0);e.hasOwnProperty(t)||(e[t]=!0),e[t]=!e[t]})},Kb.prototype.isSelected=function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&0<=I(this._availableNames,t)},Kb.prototype.getOrient=function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},Kb.type="legend.plain",Kb.dependencies=["series"],Kb.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}};var Zb,jb=Kb;function Kb(){var t=null!==Zb&&Zb.apply(this,arguments)||this;return t.type=Kb.type,t.layoutMode={type:"box",ignoreSize:!0},t}var $b,Qb=pt,Jb=O,tS=Gr,eS=(u(nS,$b=Xg),nS.prototype.init=function(){this.group.add(this._contentGroup=new tS),this.group.add(this._selectorGroup=new tS),this._isFirstRender=!0},nS.prototype.getContentGroup=function(){return this._contentGroup},nS.prototype.getSelectorGroup=function(){return this._selectorGroup},nS.prototype.render=function(t,e,n){var i,r,o,a,s,l=this._isFirstRender;this._isFirstRender=!1,this.resetInner(),t.get("show",!0)&&(r=t.get("align"),i=t.get("orient"),r&&"auto"!==r||(r="right"===t.get("left")&&"vertical"===i?"right":"left"),a=t.get("selector",!0),s=t.get("selectorPosition",!0),this.renderInner(r,t,e,n,a,i,s=!a||s&&"auto"!==s?s:"horizontal"===i?"end":"start"),o=Rp(e=t.getBoxLayoutParams(),i={width:n.getWidth(),height:n.getHeight()},n=t.get("padding")),o=Rp(z({width:(r=this.layoutInner(t,r,o,l,a,s)).width,height:r.height},e),i,n),this.group.x=o.x-r.x,this.group.y=o.y-r.y,this.group.markRedraw(),this.group.add(this._backgroundEl=(l=r,s=wp((a=t).get("padding")),(e=a.getItemStyle(["color","opacity"])).fill=a.get("backgroundColor"),l=new As({shape:{x:l.x-s[3],y:l.y-s[0],width:l.width+s[1]+s[3],height:l.height+s[0]+s[2],r:a.get("borderRadius")},style:e,silent:!0,z2:-1}))))},nS.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},nS.prototype.renderInner=function(s,l,u,h,t,e,n){var c=this.getContentGroup(),p=E(),d=l.get("selectedMode"),f=[];u.eachRawSeries(function(t){t.get("legendHoverLink")||f.push(t.id)}),Jb(l.getData(),function(r,o){var e,t,n,i,a=r.get("name");this.newlineDisabled||""!==a&&"\n"!==a?(e=u.getSeriesByName(a)[0],p.get(a)||(e?(i=(n=e.getData()).getVisual("legendLineStyle")||{},t=n.getVisual("legendIcon"),n=n.getVisual("style"),(i=this._createItem(e,a,o,r,l,s,i,n,t,d,h)).on("click",Qb(iS,a,null,h,f)).on("mouseover",Qb(oS,e.name,null,h,f)).on("mouseout",Qb(aS,e.name,null,h,f)),u.ssr&&i.eachChild(function(t){t=D(t);t.seriesIndex=e.seriesIndex,t.dataIndex=o,t.ssrType="legend"}),p.set(a,!0)):u.eachRawSeries(function(e){var t,n,i;p.get(a)||e.legendVisualProvider&&(n=e.legendVisualProvider).containName(a)&&(i=n.indexOfName(a),t=n.getItemVisual(i,"style"),n=n.getItemVisual(i,"legendIcon"),(i=gi(t.fill))&&0===i[3]&&(i[3]=.2,t=L(L({},t),{fill:wi(i,"rgba")})),(i=this._createItem(e,a,o,r,l,s,{},t,n,d,h)).on("click",Qb(iS,null,a,h,f)).on("mouseover",Qb(oS,null,a,h,f)).on("mouseout",Qb(aS,null,a,h,f)),u.ssr&&i.eachChild(function(t){t=D(t);t.seriesIndex=e.seriesIndex,t.dataIndex=o,t.ssrType="legend"}),p.set(a,!0))},this))):((n=new tS).newline=!0,c.add(n))},this),t&&this._createSelector(t,l,h,e,n)},nS.prototype._createSelector=function(t,i,r,e,n){var o=this.getSelectorGroup();Jb(t,function(t){var e=t.type,n=new Ns({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){r.dispatchAction({type:"all"===e?"legendAllSelect":"legendInverseSelect",legendId:i.id})}});o.add(n),cc(n,{normal:i.getModel("selectorLabel"),emphasis:i.getModel(["emphasis","selectorLabel"])},{defaultText:t.title}),Rl(n)})},nS.prototype._createItem=function(t,e,n,i,r,o,a,s,l,u,h){var c=t.visualDrawType,p=r.get("itemWidth"),d=r.get("itemHeight"),f=r.isSelected(e),g=i.get("symbolRotate"),y=i.get("symbolKeepAspect"),m=i.get("icon"),a=function(t,e,n,i,r,o,a){function s(n,i){"auto"===n.lineWidth&&(n.lineWidth=0<i.lineWidth?2:0),Jb(n,function(t,e){"inherit"===n[e]&&(n[e]=i[e])})}var l=e.getModel("itemStyle"),u=l.getItemStyle(),t=0===t.lastIndexOf("empty",0)?"fill":"stroke",l=l.getShallow("decal");u.decal=l&&"inherit"!==l?Im(l,a):i.decal,"inherit"===u.fill&&(u.fill=i[r]);"inherit"===u.stroke&&(u.stroke=i[t]);"inherit"===u.opacity&&(u.opacity=("fill"===r?i:n).opacity);s(u,i);l=e.getModel("lineStyle"),a=l.getLineStyle();s(a,n),"auto"===u.fill&&(u.fill=i.fill),"auto"===u.stroke&&(u.stroke=i.fill),"auto"===a.stroke&&(a.stroke=i.fill),o||(r=e.get("inactiveBorderWidth"),n=u[t],u.lineWidth="auto"===r?0<i.lineWidth&&n?2:0:u.lineWidth,u.fill=e.get("inactiveColor"),u.stroke=e.get("inactiveBorderColor"),a.stroke=l.get("inactiveColor"),a.lineWidth=l.get("inactiveWidth"));return{itemStyle:u,lineStyle:a}}(l=m||l||"roundRect",i,a,s,c,f,h),s=new tS,c=i.getModel("textStyle"),m=(!k(t.getLegendIcon)||m&&"inherit"!==m?(h="inherit"===m&&t.getData().getVisual("symbol")?"inherit"===g?t.getData().getVisual("symbolRotate"):g:0,s.add(function(t){var e=t.icon||"roundRect",n=$y(e,0,0,t.itemWidth,t.itemHeight,t.itemStyle.fill,t.symbolKeepAspect);n.setStyle(t.itemStyle),n.rotation=(t.iconRotate||0)*Math.PI/180,n.setOrigin([t.itemWidth/2,t.itemHeight/2]),-1<e.indexOf("empty")&&(n.style.stroke=n.style.fill,n.style.fill="#fff",n.style.lineWidth=2);return n}({itemWidth:p,itemHeight:d,icon:l,iconRotate:h,itemStyle:a.itemStyle,lineStyle:a.lineStyle,symbolKeepAspect:y}))):s.add(t.getLegendIcon({itemWidth:p,itemHeight:d,icon:l,iconRotate:g,itemStyle:a.itemStyle,lineStyle:a.lineStyle,symbolKeepAspect:y})),"left"===o?p+5:-5),h=o,t=r.get("formatter"),l=e,g=(V(t)&&t?l=t.replace("{name}",null!=e?e:""):k(t)&&(l=t(e)),f?c.getTextColor():i.get("inactiveColor")),a=(s.add(new Ns({style:dc(c,{text:l,x:m,y:d/2,fill:g,align:h,verticalAlign:"middle"},{inheritColor:g})})),new As({shape:s.getBoundingRect(),style:{fill:"transparent"}})),y=i.getModel("tooltip");return y.get("show")&&rc({el:a,componentModel:r,itemName:e,itemTooltipOption:y.option}),s.add(a),s.eachChild(function(t){t.silent=!0}),a.silent=!u,this.getContentGroup().add(s),Rl(s),s.__legendDataIndex=n,s},nS.prototype.layoutInner=function(t,e,n,i,r,o){var a,s,l,u,h,c=this.getContentGroup(),p=this.getSelectorGroup(),n=(Op(t.get("orient"),c,t.get("itemGap"),n.width,n.height),c.getBoundingRect()),d=[-n.x,-n.y];return p.markRedraw(),c.markRedraw(),r?(Op("horizontal",p,t.get("selectorItemGap",!0)),a=[-(r=p.getBoundingRect()).x,-r.y],s=t.get("selectorButtonGap",!0),l=0===(t=t.getOrient().index)?"width":"height",u=0===t?"height":"width",h=0===t?"y":"x","end"===o?a[t]+=n[l]+s:d[t]+=r[l]+s,a[1-t]+=n[u]/2-r[u]/2,p.x=a[0],p.y=a[1],c.x=d[0],c.y=d[1],(o={x:0,y:0})[l]=n[l]+s+r[l],o[u]=Math.max(n[u],r[u]),o[h]=Math.min(0,r[h]+a[1-t]),o):(c.x=d[0],c.y=d[1],this.group.getBoundingRect())},nS.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},nS.type="legend.plain",nS);function nS(){var t=null!==$b&&$b.apply(this,arguments)||this;return t.type=nS.type,t.newlineDisabled=!1,t}function iS(t,e,n,i){aS(t,e,n,i),n.dispatchAction({type:"legendToggleSelect",name:null!=t?t:e}),oS(t,e,n,i)}function rS(t){for(var e,n=t.getZr().storage.getDisplayList(),i=0,r=n.length;i<r&&!(e=n[i].states.emphasis);)i++;return e&&e.hoverLayer}function oS(t,e,n,i){rS(n)||n.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:i})}function aS(t,e,n,i){rS(n)||n.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:i})}function sS(t){var n=t.findComponents({mainType:"legend"});n&&n.length&&t.filterSeries(function(t){for(var e=0;e<n.length;e++)if(!n[e].isSelected(t.name))return!1;return!0})}function lS(e,n,t){var i="allSelect"===e||"inverseSelect"===e,r={},o=[],a=(t.eachComponent({mainType:"legend",query:n},function(t){i?t[e]():t[e](n.name),uS(t,r),o.push(t.componentIndex)}),{});return t.eachComponent("legend",function(n){O(r,function(t,e){n[t?"select":"unSelect"](e)}),uS(n,a)}),i?{selected:a,legendIndex:o}:{name:n.name,selected:a}}function uS(n,t){var i=t||{};O(n.getData(),function(t){var e,t=t.get("name");"\n"!==t&&""!==t&&(e=n.isSelected(t),Bt(i,t)?i[t]=i[t]&&e:i[t]=e)})}function hS(t){t.registerComponentModel(jb),t.registerComponentView(eS),t.registerProcessor(t.PRIORITY.PROCESSOR.SERIES_FILTER,sS),t.registerSubTypeDefaulter("legend",function(){return"plain"}),(t=t).registerAction("legendToggleSelect","legendselectchanged",pt(lS,"toggleSelected")),t.registerAction("legendAllSelect","legendselectall",pt(lS,"allSelect")),t.registerAction("legendInverseSelect","legendinverseselect",pt(lS,"inverseSelect")),t.registerAction("legendSelect","legendselected",pt(lS,"select")),t.registerAction("legendUnSelect","legendunselected",pt(lS,"unSelect"))}u(dS,cS=jb),dS.prototype.setScrollDataIndex=function(t){this.option.scrollDataIndex=t},dS.prototype.init=function(t,e,n){var i=zp(t);cS.prototype.init.call(this,t,e,n),fS(this,t,i)},dS.prototype.mergeOption=function(t,e){cS.prototype.mergeOption.call(this,t,e),fS(this,this.option,t)},dS.type="legend.scroll",dS.defaultOption=Bc(jb.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800});var cS,pS=dS;function dS(){var t=null!==cS&&cS.apply(this,arguments)||this;return t.type=dS.type,t}function fS(t,e,n){var i=[1,1];i[t.getOrient().index]=0,Ep(e,n,{type:"box",ignoreSize:!!i})}var gS,yS=Gr,mS=["width","height"],vS=["x","y"],_S=(u(xS,gS=eS),xS.prototype.init=function(){gS.prototype.init.call(this),this.group.add(this._containerGroup=new yS),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new yS)},xS.prototype.resetInner=function(){gS.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},xS.prototype.renderInner=function(t,i,e,r,n,o,a){var s=this,l=(gS.prototype.renderInner.call(this,t,i,e,r,n,o,a),this._controllerGroup),t=i.get("pageIconSize",!0),u=F(t)?t:[t,t],e=(h("pagePrev",0),i.getModel("pageTextStyle"));function h(t,e){var n=t+"DataIndex",e=nc(i.get("pageIcons",!0)[i.getOrient().name][e],{onclick:ct(s._pageGo,s,n,i,r)},{x:-u[0]/2,y:-u[1]/2,width:u[0],height:u[1]});e.name=t,l.add(e)}l.add(new Ns({name:"pageText",style:{text:"xx/xx",fill:e.getTextColor(),font:e.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),h("pageNext",1)},xS.prototype.layoutInner=function(t,e,n,i,r,o){var a=this.getSelectorGroup(),s=t.getOrient().index,l=mS[s],u=vS[s],h=mS[1-s],c=vS[1-s],p=(r&&Op("horizontal",a,t.get("selectorItemGap",!0)),t.get("selectorButtonGap",!0)),d=a.getBoundingRect(),f=[-d.x,-d.y],g=y(n),n=(r&&(g[l]=n[l]-d[l]-p),this._layoutContentAndController(t,i,g,s,l,h,c,u));return r&&("end"===o?f[s]+=n[l]+p:(t=d[l]+p,f[s]-=t,n[u]-=t),n[l]+=d[l]+p,f[1-s]+=n[c]+n[h]/2-d[h]/2,n[h]=Math.max(n[h],d[h]),n[c]=Math.min(n[c],d[c]+f[1-s]),a.x=f[0],a.y=f[1],a.markRedraw()),n},xS.prototype._layoutContentAndController=function(t,e,n,i,r,o,a,s){var l=this.getContentGroup(),u=this._containerGroup,h=this._controllerGroup,c=(Op(t.get("orient"),l,t.get("itemGap"),i?n.width:null,i?null:n.height),Op("horizontal",h,t.get("pageButtonItemGap",!0)),l.getBoundingRect()),p=h.getBoundingRect(),d=this._showController=c[r]>n[r],f=[-c.x,-c.y],e=(e||(f[i]=l[s]),[0,0]),s=[-p.x,-p.y],g=N(t.get("pageButtonGap",!0),t.get("itemGap",!0)),f=(d&&("end"===t.get("pageButtonPosition",!0)?s[i]+=n[r]-p[r]:e[i]+=p[r]+g),s[1-i]+=c[o]/2-p[o]/2,l.setPosition(f),u.setPosition(e),h.setPosition(s),{x:0,y:0}),c=(f[r]=(d?n:c)[r],f[o]=Math.max(c[o],p[o]),f[a]=Math.min(0,p[a]+s[1-i]),u.__rectSize=n[r],d?((e={x:0,y:0})[r]=Math.max(n[r]-p[r]-g,0),e[o]=f[o],u.setClipPath(new As({shape:e})),u.__rectSize=e[r]):h.eachChild(function(t){t.attr({invisible:!0,silent:!0})}),this._getPageInfo(t));return null!=c.pageIndex&&kh(l,{x:c.contentPosition[0],y:c.contentPosition[1]},d?t:null),this._updatePageInfoView(t,c),f},xS.prototype._pageGo=function(t,e,n){t=this._getPageInfo(e)[t];null!=t&&n.dispatchAction({type:"legendScroll",scrollDataIndex:t,legendId:e.id})},xS.prototype._updatePageInfoView=function(n,i){var r=this._controllerGroup,t=(O(["pagePrev","pageNext"],function(t){var e=null!=i[t+"DataIndex"],t=r.childOfName(t);t&&(t.setStyle("fill",e?n.get("pageIconColor",!0):n.get("pageIconInactiveColor",!0)),t.cursor=e?"pointer":"default")}),r.childOfName("pageText")),e=n.get("pageFormatter"),o=i.pageIndex,o=null!=o?o+1:0,a=i.pageCount;t&&e&&t.setStyle("text",V(e)?e.replace("{current}",null==o?"":o+"").replace("{total}",null==a?"":a+""):e({current:o,total:a}))},xS.prototype._getPageInfo=function(t){var e=t.get("scrollDataIndex",!0),n=this.getContentGroup(),i=this._containerGroup.__rectSize,t=t.getOrient().index,r=mS[t],o=vS[t],e=this._findTargetItemIndex(e),a=n.children(),s=a[e],l=a.length,u=l?1:0,h={contentPosition:[n.x,n.y],pageCount:u,pageIndex:u-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(s){n=g(s);h.contentPosition[t]=-n.s;for(var c=e+1,p=n,d=n,f=null;c<=l;++c)(!(f=g(a[c]))&&d.e>p.s+i||f&&!y(f,p.s))&&(p=d.i>p.i?d:f)&&(null==h.pageNextDataIndex&&(h.pageNextDataIndex=p.i),++h.pageCount),d=f;for(c=e-1,p=n,d=n,f=null;-1<=c;--c)(f=g(a[c]))&&y(d,f.s)||!(p.i<d.i)||(d=p,null==h.pagePrevDataIndex&&(h.pagePrevDataIndex=p.i),++h.pageCount,++h.pageIndex),p=f}return h;function g(t){var e,n;if(t)return{s:n=(e=t.getBoundingRect())[o]+t[o],e:n+e[r],i:t.__legendDataIndex}}function y(t,e){return t.e>=e&&t.s<=e+i}},xS.prototype._findTargetItemIndex=function(n){var i,r;return this._showController?(this.getContentGroup().eachChild(function(t,e){t=t.__legendDataIndex;null==r&&null!=t&&(r=e),t===n&&(i=e)}),null!=i?i:r):0},xS.type="legend.scroll",xS);function xS(){var t=null!==gS&&gS.apply(this,arguments)||this;return t.type=xS.type,t.newlineDisabled=!0,t._currentIndex=0,t}B_(function(t){B_(hS),t.registerComponentModel(pS),t.registerComponentView(_S),t.registerAction("legendScroll","legendscroll",function(t,e){var n=t.scrollDataIndex;null!=n&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(n)})})});var wS=Lo(),bS=y,SS=ct;function MS(){this._dragging=!1,this.animationThreshold=15}function TS(t,e,n,i){!function n(i,t){{var r;return R(i)&&R(t)?(r=!0,O(t,function(t,e){r=r&&n(i[e],t)}),!!r):i===t}}(wS(n).lastProp,i)&&(wS(n).lastProp=i,e?kh(n,i,t):(n.stopAnimation(),n.attr(i)))}function CS(t,e){t[e.get(["label","show"])?"show":"hide"]()}function IS(t){return{x:t.x||0,y:t.y||0,rotation:t.rotation||0}}function kS(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)})}function DS(t,e,n,i,r){var o=AS(n.get("value"),e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get(["label","precision"]),formatter:n.get(["label","formatter"])}),n=n.getModel("label"),a=wp(n.get("padding")||0),s=n.getFont(),l=Mr(o,s),u=r.position,h=l.width+a[1]+a[3],l=l.height+a[0]+a[2],c=r.align,c=("right"===c&&(u[0]-=h),"center"===c&&(u[0]-=h/2),r.verticalAlign),i=("bottom"===c&&(u[1]-=l),"middle"===c&&(u[1]-=l/2),r=u,c=h,h=l,i=(l=i).getWidth(),l=l.getHeight(),r[0]=Math.min(r[0]+c,i)-c,r[1]=Math.min(r[1]+h,l)-h,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0),n.get("backgroundColor"));i&&"auto"!==i||(i=e.get(["axisLine","lineStyle","color"])),t.label={x:u[0],y:u[1],style:dc(n,{text:o,font:s,fill:n.getTextColor(),padding:a,backgroundColor:i}),z2:10}}function AS(t,e,n,i,r){t=e.scale.parse(t);var o,a=e.scale.getLabel({value:t},{precision:r.precision}),r=r.formatter;return r&&(o={value:A_(e,{value:t}),axisDimension:e.dim,axisIndex:e.index,seriesData:[]},O(i,function(t){var e=n.getSeriesByIndex(t.seriesIndex),t=t.dataIndexInside,e=e&&e.getDataParams(t);e&&o.seriesData.push(e)}),V(r)?a=r.replace("{value}",a):k(r)&&(a=r(o))),a}function PS(t,e,n){var i=Le();return ze(i,i,n.rotation),Ee(i,i,n.position),$h([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}MS.prototype.render=function(t,e,n,i){var r,o,a=e.get("value"),s=e.get("status");this._axisModel=t,this._axisPointerModel=e,this._api=n,!i&&this._lastValue===a&&this._lastStatus===s||(this._lastValue=a,this._lastStatus=s,i=this._group,r=this._handle,s&&"hide"!==s?(i&&i.show(),r&&r.show(),this.makeElOption(s={},a,t,e,n),(o=s.graphicKey)!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=o,o=this._moveAnimation=this.determineAnimation(t,e),i?(o=pt(TS,e,o),this.updatePointerEl(i,s,o),this.updateLabelEl(i,s,o,e)):(i=this._group=new Gr,this.createPointerEl(i,s,t,e),this.createLabelEl(i,s,t,e),n.getZr().add(i)),kS(i,e,!0),this._renderHandle(a)):(i&&i.hide(),r&&r.hide()))},MS.prototype.remove=function(t){this.clear(t)},MS.prototype.dispose=function(t){this.clear(t)},MS.prototype.determineAnimation=function(t,e){var n,i=e.get("animation"),r=t.axis,o="category"===r.type,e=e.get("snap");return!(!e&&!o)&&("auto"===i||null==i?(n=this.animationThreshold,o&&r.getBandWidth()>n||!!e&&(o=_b(t).seriesDataCount,e=r.getExtent(),Math.abs(e[0]-e[1])/o>n)):!0===i)},MS.prototype.makeElOption=function(t,e,n,i,r){},MS.prototype.createPointerEl=function(t,e,n,i){var r=e.pointer;r&&(r=wS(t).pointerEl=new sc[r.type](bS(e.pointer)),t.add(r))},MS.prototype.createLabelEl=function(t,e,n,i){e.label&&(e=wS(t).labelEl=new Ns(bS(e.label)),t.add(e),CS(e,i))},MS.prototype.updatePointerEl=function(t,e,n){t=wS(t).pointerEl;t&&e.pointer&&(t.setStyle(e.pointer.style),n(t,{shape:e.pointer.shape}))},MS.prototype.updateLabelEl=function(t,e,n,i){t=wS(t).labelEl;t&&(t.setStyle(e.label.style),n(t,{x:e.label.x,y:e.label.y}),CS(t,i))},MS.prototype._renderHandle=function(t){var e,n,i,r,o,a;!this._dragging&&this.updateHandleTransform&&(e=this._axisPointerModel,n=this._api.getZr(),i=this._handle,r=e.getModel("handle"),a=e.get("status"),r.get("show")&&a&&"hide"!==a?(this._handle||(o=!0,i=this._handle=nc(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){Ie(t.event)},onmousedown:SS(this._onHandleDragMove,this,0,0),drift:SS(this._onHandleDragMove,this),ondragend:SS(this._onHandleDragEnd,this)}),n.add(i)),kS(i,e,!1),i.setStyle(r.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"])),F(a=r.get("size"))||(a=[a,a]),i.scaleX=a[0]/2,i.scaleY=a[1]/2,sy(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,o)):(i&&n.remove(i),this._handle=null))},MS.prototype._moveHandleToValue=function(t,e){TS(this._axisPointerModel,!e&&this._moveAnimation,this._handle,IS(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},MS.prototype._onHandleDragMove=function(t,e){var n=this._handle;n&&(this._dragging=!0,t=this.updateHandleTransform(IS(n),[t,e],this._axisModel,this._axisPointerModel),this._payloadInfo=t,n.stopAnimation(),n.attr(IS(t)),wS(n).lastProp=null,this._doDispatchAxisPointer())},MS.prototype._doDispatchAxisPointer=function(){var t,e;this._handle&&(t=this._payloadInfo,e=this._axisModel,this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:e.axis.dim,axisIndex:e.componentIndex}]}))},MS.prototype._onHandleDragEnd=function(){var t;this._dragging=!1,this._handle&&(t=this._axisPointerModel.get("value"),this._moveHandleToValue(t),this._api.dispatchAction({type:"hideTip"}))},MS.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var t=t.getZr(),e=this._group,n=this._handle;t&&e&&(this._lastGraphicKey=null,e&&t.remove(e),n&&t.remove(n),this._group=null,this._handle=null,this._payloadInfo=null),ly(this,"_doDispatchAxisPointer")},MS.prototype.doClear=function(){},MS.prototype.buildLabel=function(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}};u(RS,LS=MS),RS.prototype.makeElOption=function(t,e,n,i,r){var o,a,s=n.axis,l=s.grid,u=i.get("type"),h=NS(l,s).getOtherAxis(s).getGlobalExtent(),c=s.toGlobalCoord(s.dataToCoord(e,!0)),p=(u&&"none"!==u&&(o=(a=i).get("type"),a=a.getModel(o+"Style"),"line"===o?(p=a.getLineStyle()).fill=null:"shadow"===o&&((p=a.getAreaStyle()).stroke=null),o=p,(a=ES[u](s,c,h)).style=o,t.graphicKey=a.type,t.pointer=a),tb(l.model,n));u=e,s=t,c=p,h=n,o=i,a=r,l=hb.innerTextLayout(c.rotation,0,c.labelDirection),c.labelMargin=o.get(["label","margin"]),DS(s,h,o,a,{position:PS(h.axis,u,c),align:l.textAlign,verticalAlign:l.textVerticalAlign})},RS.prototype.getHandleTransform=function(t,e,n){var i=tb(e.axis.grid.model,e,{labelInside:!1}),n=(i.labelMargin=n.get(["handle","margin"]),PS(e.axis,t,i));return{x:n[0],y:n[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},RS.prototype.updateHandleTransform=function(t,e,n,i){var n=n.axis,r=n.grid,o=n.getGlobalExtent(!0),r=NS(r,n).getOtherAxis(n).getGlobalExtent(),n="x"===n.dim?0:1,a=[t.x,t.y],e=(a[n]+=e[n],a[n]=Math.min(o[1],a[n]),a[n]=Math.max(o[0],a[n]),(r[1]+r[0])/2),o=[e,e];o[n]=a[n];return{x:a[0],y:a[1],rotation:t.rotation,cursorPoint:o,tooltipOption:[{verticalAlign:"middle"},{align:"center"}][n]}};var LS,OS=RS;function RS(){return null!==LS&&LS.apply(this,arguments)||this}function NS(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var ES={line:function(t,e,n){var i;return i=[e,n[0]],e=[e,n[1]],n=zS(t),{type:"Line",subPixelOptimize:!0,shape:{x1:i[n=n||0],y1:i[1-n],x2:e[n],y2:e[1-n]}}},shadow:function(t,e,n){var i=Math.max(1,t.getBandWidth()),r=n[1]-n[0];return{type:"Rect",shape:(e=[e-i/2,n[0]],n=[i,r],i=zS(t),{x:e[i=i||0],y:e[1-i],width:n[i],height:n[1-i]})}}};function zS(t){return"x"===t.dim?0:1}u(VS,BS=g),VS.type="axisPointer",VS.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}};var BS,FS=VS;function VS(){var t=null!==BS&&BS.apply(this,arguments)||this;return t.type=VS.type,t}var HS=Lo(),GS=O;function WS(t,e,n){var i,c,p;function r(t,h){c.on(t,function(e){n=p;var n,i,r={dispatchAction:o,pendings:i={showTip:[],hideTip:[]}};function o(t){var e=i[t.type];e?e.push(t):(t.dispatchAction=o,n.dispatchAction(t))}GS(HS(c).records,function(t){t&&h(t,e,r.dispatchAction)});var t,a=r.pendings,s=p,l=a.showTip.length,u=a.hideTip.length;l?t=a.showTip[l-1]:u&&(t=a.hideTip[u-1]),t&&(t.dispatchAction=null,s.dispatchAction(t))})}b.node||(i=e.getZr(),HS(i).records||(HS(i).records={}),p=e,HS(c=i).initialized||(HS(c).initialized=!0,r("click",pt(XS,"click")),r("mousemove",pt(XS,"mousemove")),r("globalout",US)),(HS(i).records[t]||(HS(i).records[t]={})).handler=n)}function US(t,e,n){t.handler("leave",null,n)}function XS(t,e,n,i){e.handler(t,n,i)}function YS(t,e){b.node||(e=e.getZr(),(HS(e).records||{})[t]&&(HS(e).records[t]=null))}u(jS,qS=Xg),jS.prototype.render=function(t,e,n){var e=e.getComponent("tooltip"),i=t.get("triggerOn")||e&&e.get("triggerOn")||"mousemove|click";WS("axisPointer",n,function(t,e,n){"none"!==i&&("leave"===t||0<=i.indexOf(t))&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},jS.prototype.remove=function(t,e){YS("axisPointer",e)},jS.prototype.dispose=function(t,e){YS("axisPointer",e)},jS.type="axisPointer";var qS,ZS=jS;function jS(){var t=null!==qS&&qS.apply(this,arguments)||this;return t.type=jS.type,t}function KS(t,e){var n,i,r,o,a=[],s=t.seriesIndex;return null==s||!(e=e.getSeriesByIndex(s))||null==(s=Po(n=e.getData(),t))||s<0||F(s)?{point:[]}:(i=n.getItemGraphicEl(s),r=e.coordinateSystem,e.getTooltipPosition?a=e.getTooltipPosition(s)||[]:r&&r.dataToPoint?a=t.isStacked?(e=r.getBaseAxis(),t=r.getOtherAxis(e).dim,e=e.dim,t="x"===t||"radius"===t?1:0,e=n.mapDimension(e),(o=[])[t]=n.get(e,s),o[1-t]=n.get(n.getCalculationInfo("stackResultDimension"),s),r.dataToPoint(o)||[]):r.dataToPoint(n.getValues(B(r.dimensions,function(t){return n.mapDimension(t)}),s))||[]:i&&((e=i.getBoundingRect().clone()).applyTransform(i.transform),a=[e.x+e.width/2,e.y+e.height/2]),{point:a,el:i})}var $S=Lo();function QS(t,e,n){var o,a,i,s,l,r,u,h,c,p,d,f,g,y,m=t.currTrigger,v=[t.x,t.y],_=t,x=t.dispatchAction||ct(n.dispatchAction,n),w=e.getComponent("axisPointer").coordSysAxesInfo;if(w)return iM(v)&&(v=KS({seriesIndex:_.seriesIndex,dataIndex:_.dataIndex},e).point),o=iM(v),a=_.axesInfo,i=w.axesInfo,s="leave"===m||iM(v),l={},e={list:[],map:{}},u={showPointer:pt(tM,r={}),showTooltip:pt(eM,e)},O(w.coordSysMap,function(t,e){var r=o||t.containPoint(v);O(w.coordSysAxesInfo[e],function(t,e){var n=t.axis,i=function(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}(a,t);s||!r||a&&!i||null!=(i=null!=(i=i&&i.value)||o?i:n.pointToData(v))&&JS(t,i,u,!1,l)})}),h={},O(i,function(n,t){var i=n.linkGroup;i&&!r[t]&&O(i.axesInfo,function(t,e){var e=r[e];t!==n&&e&&(e=e.value,i.mapper&&(e=n.axis.scale.parse(i.mapper(e,nM(t),nM(n)))),h[n.key]=e)})}),O(h,function(t,e){JS(i[e],t,u,!0,l)}),c=r,_=i,p=l.axesInfo=[],O(_,function(t,e){var n=t.axisPointerModel.option,e=c[e];e?(t.useHandle||(n.status="show"),n.value=e.value,n.seriesDataIndices=(e.payloadBatch||[]).slice()):t.useHandle||(n.status="hide"),"show"===n.status&&p.push({axisDim:t.axis.dim,axisIndex:t.axis.model.componentIndex,value:n.value})}),m=e,_=t,e=x,iM(t=v)||!m.list.length?e({type:"hideTip"}):(x=((m.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{},e({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:_.tooltipOption,position:_.position,dataIndexInside:x.dataIndexInside,dataIndex:x.dataIndex,seriesIndex:x.seriesIndex,dataByCoordSys:m.list})),e=i,_=(t=n).getZr(),x="axisPointerLastHighlights",d=$S(_)[x]||{},f=$S(_)[x]={},O(e,function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&t.triggerEmphasis&&O(n.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;f[e]=t})}),g=[],y=[],O(d,function(t,e){f[e]||y.push(t)}),O(f,function(t,e){d[e]||g.push(t)}),y.length&&t.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:y}),g.length&&t.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:g}),l}function JS(t,e,n,i,r){var o,a,s,l,u,h,c,p,d,f,g=t.axis;!g.scale.isBlank()&&g.containData(e)&&(t.involveSeries?(a=e,s=t.axis,l=s.dim,u=a,h=[],c=Number.MAX_VALUE,p=-1,O(t.seriesModels,function(e,t){var n,i=e.getData().mapDimensionsAll(l);if(e.getAxisTooltipData)var r=e.getAxisTooltipData(i,a,s),o=r.dataIndices,r=r.nestestValue;else{if(!(o=e.getData().indicesOfNearest(i[0],a,"category"===s.type?.5:null)).length)return;r=e.getData().get(i[0],o[0])}null!=r&&isFinite(r)&&(i=a-r,(n=Math.abs(i))<=c)&&((n<c||0<=i&&p<0)&&(c=n,p=i,u=r,h.length=0),O(o,function(t){h.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}),f=(o={payloadBatch:h,snapToValue:u}).snapToValue,(d=o.payloadBatch)[0]&&null==r.seriesIndex&&L(r,d[0]),!i&&t.snap&&g.containData(f)&&null!=f&&(e=f),n.showPointer(t,e,d),n.showTooltip(t,o,f)):n.showPointer(t,e))}function tM(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function eM(t,e,n,i){var r,o,n=n.payloadBatch,a=e.axis,s=a.model,l=e.axisPointerModel;e.triggerTooltip&&n.length&&(r=wb(e=e.coordSys.model),(o=t.map[r])||(o=t.map[r]={coordSysId:e.id,coordSysIndex:e.componentIndex,coordSysType:e.type,coordSysMainType:e.mainType,dataByAxis:[]},t.list.push(o)),o.dataByAxis.push({axisDim:a.dim,axisIndex:s.componentIndex,axisType:s.type,axisId:s.id,value:i,valueLabelOpt:{precision:l.get(["label","precision"]),formatter:l.get(["label","formatter"])},seriesDataIndices:n.slice()}))}function nM(t){var e=t.axis.model,n={},t=n.axisDim=t.axis.dim;return n.axisIndex=n[t+"AxisIndex"]=e.componentIndex,n.axisName=n[t+"AxisName"]=e.name,n.axisId=n[t+"AxisId"]=e.id,n}function iM(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function rM(t){Mb.registerAxisPointerClass("CartesianAxisPointer",OS),t.registerComponentModel(FS),t.registerComponentView(ZS),t.registerPreprocessor(function(t){var e;t&&(t.axisPointer&&0!==t.axisPointer.length||(t.axisPointer={}),e=t.axisPointer.link)&&!F(e)&&(t.axisPointer.link=[e])}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=mb(t,e)}),t.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},QS)}u(sM,oM=g),sM.type="tooltip",sM.dependencies=["axisPointer"],sM.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}};var oM,aM=sM;function sM(){var t=null!==oM&&oM.apply(this,arguments)||this;return t.type=sM.type,t}function lM(t){var e=t.get("confine");return null!=e?e:"richText"===t.get("renderMode")}function uM(t){if(b.domSupported)for(var e=document.documentElement.style,n=0,i=t.length;n<i;n++)if(t[n]in e)return t[n]}var hM=uM(["transform","webkitTransform","OTransform","MozTransform","msTransform"]);function cM(t,e){if(!t)return e;e=xp(e,!0);var n=t.indexOf(e);return(t=-1===n?e:"-"+t.slice(0,n)+"-"+e).toLowerCase()}var pM=cM(uM(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),"transition"),dM=cM(hM,"transform"),fM="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(b.transform3dSupported?"will-change:transform;":"");function gM(t,e,n){var i,t=t.toFixed(0)+"px",e=e.toFixed(0)+"px";return b.transformSupported?(i="translate"+((i=b.transform3dSupported)?"3d":"")+"("+t+","+e+(i?",0":"")+")",n?"top:0;left:0;"+dM+":"+i+";":[["top",0],["left",0],[hM,i]]):n?"top:"+e+";left:"+t+";":[["top",e],["left",t]]}function yM(i,t,e){var n,r,o=[],a=i.get("transitionDuration"),s=i.get("backgroundColor"),l=i.get("shadowBlur"),u=i.get("shadowColor"),h=i.get("shadowOffsetX"),c=i.get("shadowOffsetY"),p=i.getModel("textStyle"),d=Ag(i,"html");return o.push("box-shadow:"+(h+"px "+c+"px "+l+"px "+u)),t&&a&&o.push((u="opacity"+(l=" "+(h=a)/2+"s "+(c="cubic-bezier(0.23,1,0.32,1)"))+",visibility"+l,e||(l=" "+h+"s "+c,u+=b.transformSupported?","+dM+l:",left"+l+",top"+l),pM+":"+u)),s&&o.push("background-color:"+s),O(["width","color","radius"],function(t){var e="border-"+t,n=xp(e),n=i.get(n);null!=n&&o.push(e+":"+n+("color"===t?"":"px"))}),o.push((r=[],t=(n=p).get("fontSize"),(a=n.getTextColor())&&r.push("color:"+a),r.push("font:"+n.getFont()),a=N(n.get("lineHeight"),Math.round(3*t/2)),t&&r.push("line-height:"+a+"px"),t=n.get("textShadowColor"),a=n.get("textShadowBlur")||0,e=n.get("textShadowOffsetX")||0,h=n.get("textShadowOffsetY")||0,t&&a&&r.push("text-shadow:"+e+"px "+h+"px "+a+"px "+t),O(["decoration","align"],function(t){var e=n.get(t);e&&r.push("text-"+t+":"+e)}),r.join(";"))),null!=d&&o.push("padding:"+wp(d).join("px ")+"px"),o.join(";")+";"}function mM(t,e,n,i,r){var o,a,s=e&&e.painter;n?(o=s&&s.getViewportRoot())&&(a=t,n=n,ge(fe,o,i,r,!0))&&ge(a,n,fe[0],fe[1]):(t[0]=i,t[1]=r,(o=s&&s.getViewportRootOffset())&&(t[0]+=o.offsetLeft,t[1]+=o.offsetTop)),t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}_M.prototype.update=function(t){this._container||(i=this._api.getDom(),n="position",n=(e=(e=i).currentStyle||document.defaultView&&document.defaultView.getComputedStyle(e))?n?e[n]:e:null,"absolute"!==(e=i.style).position&&"absolute"!==n&&(e.position="relative"));var e,n,i=t.get("alwaysShowContent");i&&this._moveIfResized(),this._alwaysShowContent=i,this.el.className=t.get("className")||""},_M.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var n=this.el,i=n.style,r=this._styleCoord;n.innerHTML?i.cssText=fM+yM(t,!this._firstShow,this._longHide)+gM(r[0],r[1],!0)+"border-color:"+Ip(e)+";"+(t.get("extraCssText")||"")+";pointer-events:"+(this._enterable?"auto":"none"):i.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},_M.prototype.setContent=function(t,e,n,i,r){var o=this.el;if(null==t)o.innerHTML="";else{var a,s,l,u,h,c="";if(V(r)&&"item"===n.get("trigger")&&!lM(n)&&(n=n,i=i,c=V(r=r)&&"inside"!==r?(a=n.get("backgroundColor"),n=n.get("borderWidth"),i=Ip(i),r=r="left"===(r=r)?"right":"right"===r?"left":"top"===r?"bottom":"top",p=Math.max(1.5*Math.round(n),6),s="",l=dM+":",-1<I(["left","right"],r)?(s+="top:50%",l+="translateY(-50%) rotate("+(h="left"==r?-225:-45)+"deg)"):(s+="left:50%",l+="translateX(-50%) rotate("+(h="top"==r?225:45)+"deg)"),h=h*Math.PI/180,h=(u=p+n)*Math.abs(Math.cos(h))+u*Math.abs(Math.sin(h)),i=i+" solid "+n+"px;",'<div style="'+["position:absolute;width:"+p+"px;height:"+p+"px;z-index:-1;",(s+=";"+r+":-"+Math.round(100*((h-Math.SQRT2*n)/2+Math.SQRT2*n-(h-u)/2))/100+"px")+";"+l+";","border-bottom:"+i,"border-right:"+i,"background-color:"+a+";"].join("")+'"></div>'):""),V(t))o.innerHTML=t+c;else if(t){o.innerHTML="",F(t)||(t=[t]);for(var p,d=0;d<t.length;d++)yt(t[d])&&t[d].parentNode!==o&&o.appendChild(t[d]);c&&o.childNodes.length&&((p=document.createElement("div")).innerHTML=c,o.appendChild(p))}}},_M.prototype.setEnterable=function(t){this._enterable=t},_M.prototype.getSize=function(){var t=this.el;return t?[t.offsetWidth,t.offsetHeight]:[0,0]},_M.prototype.moveTo=function(t,e){var n,i;this.el&&(mM(n=this._styleCoord,this._zr,this._container,t,e),null!=n[0])&&null!=n[1]&&(i=this.el.style,O(gM(n[0],n[1]),function(t){i[t[0]]=t[1]}))},_M.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},_M.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",b.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},_M.prototype.hideLater=function(t){!this._show||this._inContent&&this._enterable||this._alwaysShowContent||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ct(this.hide,this),t)):this.hide())},_M.prototype.isShow=function(){return this._show},_M.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null};var vM=_M;function _M(t,e){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,b.wxa)return null;var n=document.createElement("div"),i=(n.domBelongToZr=!0,this.el=n,this._zr=t.getZr()),e=e.appendTo,e=e&&(V(e)?document.querySelector(e):yt(e)?e:k(e)&&e(t.getDom())),r=(mM(this._styleCoord,i,e,t.getWidth()/2,t.getHeight()/2),(e||t.getDom()).appendChild(n),this._api=t,this._container=e,this);n.onmouseenter=function(){r._enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},n.onmousemove=function(t){var e;t=t||window.event,r._enterable||(e=i.handler,Ce(i.painter.getViewportRoot(),t,!0),e.dispatch("mousemove",t))},n.onmouseleave=function(){r._inContent=!1,r._enterable&&r._show&&r.hideLater(r._hideDelay)}}wM.prototype.update=function(t){t=t.get("alwaysShowContent");t&&this._moveIfResized(),this._alwaysShowContent=t},wM.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},wM.prototype.setContent=function(t,e,n,i,r){var o=this,a=(R(t)&&f(""),this.el&&this._zr.remove(this.el),n.getModel("textStyle")),s=(this.el=new Ns({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:i,textShadowColor:a.get("textShadowColor"),fill:n.get(["textStyle","color"]),padding:Ag(n,"richText"),verticalAlign:"top",align:"left"},z:n.get("z")}),O(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(t){o.el.style[t]=n.get(t)}),O(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(t){o.el.style[t]=a.get(t)||0}),this._zr.add(this.el),this);this.el.on("mouseover",function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0}),this.el.on("mouseout",function(){s._enterable&&s._show&&s.hideLater(s._hideDelay),s._inContent=!1})},wM.prototype.setEnterable=function(t){this._enterable=t},wM.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),t=SM(t.style);return[e.width+t.left+t.right,e.height+t.top+t.bottom]},wM.prototype.moveTo=function(t,e){var n,i,r=this.el;r&&(MM(i=this._styleCoord,this._zr,t,e),t=i[0],e=i[1],n=bM((i=r.style).borderWidth||0),i=SM(i),r.x=t+n+i.left,r.y=e+n+i.top,r.markRedraw())},wM.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},wM.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},wM.prototype.hideLater=function(t){!this._show||this._inContent&&this._enterable||this._alwaysShowContent||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ct(this.hide,this),t)):this.hide())},wM.prototype.isShow=function(){return this._show},wM.prototype.dispose=function(){this._zr.remove(this.el)};var xM=wM;function wM(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),MM(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}function bM(t){return Math.max(0,t)}function SM(t){var e=bM(t.shadowBlur||0),n=bM(t.shadowOffsetX||0),t=bM(t.shadowOffsetY||0);return{left:bM(e-n),right:bM(e+n),top:bM(e-t),bottom:bM(e+t)}}function MM(t,e,n,i){t[0]=n,t[1]=i,t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}var TM,CM=new As({shape:{x:-1,y:-1,width:2,height:2}}),IM=(u(kM,TM=Xg),kM.prototype.init=function(t,e){var n;!b.node&&e.getDom()&&(t=t.getComponent("tooltip"),n=this._renderMode="auto"===(n=t.get("renderMode"))?b.domSupported?"html":"richText":n||"html",this._tooltipContent="richText"===n?new xM(e):new vM(e,{appendTo:t.get("appendToBody",!0)?"body":t.get("appendTo",!0)}))},kM.prototype.render=function(t,e,n){!b.node&&n.getDom()&&(this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,(e=this._tooltipContent).update(t),e.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow(),"richText"!==this._renderMode&&t.get("transitionDuration")?sy(this,"_updatePosition",50,"fixRate"):ly(this,"_updatePosition"))},kM.prototype._initGlobalListener=function(){var i=this._tooltipModel.get("triggerOn");WS("itemTooltip",this._api,ct(function(t,e,n){"none"!==i&&(0<=i.indexOf(t)?this._tryShow(e,n):"leave"===t&&this._hide(n))},this))},kM.prototype._keepShow=function(){var t,e=this._tooltipModel,n=this._ecModel,i=this._api,r=e.get("triggerOn");null!=this._lastX&&null!=this._lastY&&"none"!==r&&"click"!==r&&(t=this,clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){i.isDisposed()||t.manuallyShowTip(e,n,i,{x:t._lastX,y:t._lastY,dataByCoordSys:t._lastDataByCoordSys})}))},kM.prototype.manuallyShowTip=function(t,e,n,i){var r,o,a,s;i.from!==this.uid&&!b.node&&n.getDom()&&(r=AM(i,n),this._ticket="",s=i.dataByCoordSys,(o=function(n,t,e){var i=No(n).queryOptionMap,r=i.keys()[0];if(r&&"series"!==r){var o,t=zo(t,r,i.get(r),{useDefault:!1,enableAll:!1,enableNone:!1}).models[0];if(t)if(e.getViewOfComponentModel(t).group.traverse(function(t){var e=D(t).tooltipConfig;if(e&&e.name===n.name)return o=t,!0}),o)return{componentMainType:r,componentIndex:t.componentIndex,el:o}}}(i,e,n))?((a=o.el.getBoundingRect().clone()).applyTransform(o.el.transform),this._tryShow({offsetX:a.x+a.width/2,offsetY:a.y+a.height/2,target:o.el,position:i.position,positionDefault:"bottom"},r)):i.tooltip&&null!=i.x&&null!=i.y?((a=CM).x=i.x,a.y=i.y,a.update(),D(a).tooltipConfig={name:null,option:i.tooltip},this._tryShow({offsetX:i.x,offsetY:i.y,target:a},r)):s?this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,dataByCoordSys:s,tooltipOption:i.tooltipOption},r):null!=i.seriesIndex?this._manuallyAxisShowTip(t,e,n,i)||(a=(o=KS(i,e)).point[0],s=o.point[1],null!=a&&null!=s&&this._tryShow({offsetX:a,offsetY:s,target:o.el,position:i.position,positionDefault:"bottom"},r)):null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target},r)))},kM.prototype.manuallyHideTip=function(t,e,n,i){var r=this._tooltipContent;this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,i.from!==this.uid&&this._hide(AM(i,n))},kM.prototype._manuallyAxisShowTip=function(t,e,n,i){var r=i.seriesIndex,o=i.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=o&&null!=a){a=e.getSeriesByIndex(r);if(a){e=DM([a.getData().getItemModel(o),a,(a.coordinateSystem||{}).model],this._tooltipModel);if("axis"===e.get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:o,position:i.position}),!0}}},kM.prototype._tryShow=function(t,e){var n,i,r,o=t.target;this._tooltipModel&&(this._lastX=t.offsetX,this._lastY=t.offsetY,(n=t.dataByCoordSys)&&n.length?this._showAxisTooltip(n,t):o?"legend"!==D(o).ssrType&&(By(o,function(t){return null!=D(t).dataIndex?(i=t,1):null!=D(t).tooltipConfig&&(r=t,1)},!(this._lastDataByCoordSys=null)),i?this._showSeriesItemTooltip(t,i,e):r?this._showComponentItemTooltip(t,r,e):this._hide(e)):(this._lastDataByCoordSys=null,this._hide(e)))},kM.prototype._showOrMove=function(t,e){t=t.get("showDelay");e=ct(e,this),clearTimeout(this._showTimout),0<t?this._showTimout=setTimeout(e,t):e()},kM.prototype._showAxisTooltip=function(t,e){var u=this._ecModel,h=this._tooltipModel,n=[e.offsetX,e.offsetY],i=DM([e.tooltipOption],h),c=this._renderMode,p=[],d=bg("section",{blocks:[],noHeader:!0}),f=[],g=new Pg,r=(O(t,function(t){O(t.dataByAxis,function(r){var o,a,s=u.getComponent(r.axisDim+"Axis",r.axisIndex),l=r.value;s&&null!=l&&(o=AS(l,s.axis,u,r.seriesDataIndices,r.valueLabelOpt),a=bg("section",{header:o,noHeader:!Ct(o),sortBlocks:!0,blocks:[]}),d.blocks.push(a),O(r.seriesDataIndices,function(t){var e,n=u.getSeriesByIndex(t.seriesIndex),t=t.dataIndexInside,i=n.getDataParams(t);i.dataIndex<0||(i.axisDim=r.axisDim,i.axisIndex=r.axisIndex,i.axisType=r.axisType,i.axisId=r.axisId,i.axisValue=A_(s.axis,{value:l}),i.axisValueLabel=o,i.marker=g.makeTooltipMarker("item",Ip(i.color),c),(e=(t=Mf(n.formatTooltip(t,!0,null))).frag)&&(n=DM([n],h).get("valueFormatter"),a.blocks.push(n?L({valueFormatter:n},e):e)),t.text&&f.push(t.text),p.push(i))}))})}),d.blocks.reverse(),f.reverse(),e.position),e=i.get("order"),e=Ig(d,g,c,e,u.get("useUTC"),i.get("textStyle")),e=(e&&f.unshift(e),"richText"===c?"\n\n":"<br/>"),o=f.join(e);this._showOrMove(i,function(){this._updateContentNotChangedOnAxis(t,p)?this._updatePosition(i,r,n[0],n[1],this._tooltipContent,p):this._showTooltipContent(i,o,p,Math.random()+"",n[0],n[1],r,null,g)})},kM.prototype._showSeriesItemTooltip=function(t,e,n){var i,r,o,a,s,l=this._ecModel,e=D(e),u=e.seriesIndex,h=l.getSeriesByIndex(u),c=e.dataModel||h,p=e.dataIndex,e=e.dataType,d=c.getData(e),f=this._renderMode,g=t.positionDefault,y=DM([d.getItemModel(p),c,h&&(h.coordinateSystem||{}).model],this._tooltipModel,g?{position:g}:null),h=y.get("trigger");null!=h&&"item"!==h||(i=c.getDataParams(p,e),r=new Pg,i.marker=r.makeTooltipMarker("item",Ip(i.color),f),g=Mf(c.formatTooltip(p,!1,e)),h=y.get("order"),e=y.get("valueFormatter"),o=g.frag,a=o?Ig(e?L({valueFormatter:e},o):o,r,f,h,l.get("useUTC"),y.get("textStyle")):g.text,s="item_"+c.name+"_"+p,this._showOrMove(y,function(){this._showTooltipContent(y,a,i,s,t.offsetX,t.offsetY,t.position,t.target,r)}),n({type:"showTip",dataIndexInside:p,dataIndex:d.getRawIndex(p),seriesIndex:u,from:this.uid}))},kM.prototype._showComponentItemTooltip=function(e,n,t){var i="html"===this._renderMode,r=D(n),o=r.tooltipConfig.option||{},a=o.encodeHTMLContent,a=(V(o)&&(o={content:o,formatter:o},a=!0),a&&i&&o.content&&((o=y(o)).content=_e(o.content)),[o]),i=this._ecModel.getComponent(r.componentMainType,r.componentIndex),r=(i&&a.push(i),a.push({formatter:o.content}),e.positionDefault),s=DM(a,this._tooltipModel,r?{position:r}:null),l=s.get("content"),u=Math.random()+"",h=new Pg;this._showOrMove(s,function(){var t=y(s.get("formatterParams")||{});this._showTooltipContent(s,l,t,u,e.offsetX,e.offsetY,e.position,n,h)}),t({type:"showTip",from:this.uid})},kM.prototype._showTooltipContent=function(n,t,i,e,r,o,a,s,l){var u,h,c,p,d;this._ticket="",n.get("showContent")&&n.get("show")&&((u=this._tooltipContent).setEnterable(n.get("enterable")),h=n.get("formatter"),a=a||n.get("position"),t=t,c=this._getNearestPoint([r,o],i,n.get("trigger"),n.get("borderColor")).color,h&&(t=V(h)?(p=n.ecModel.get("useUTC"),t=h,Tp(t=(d=F(i)?i[0]:i)&&d.axisType&&0<=d.axisType.indexOf("time")?ip(d.axisValue,t,p):t,i,!0)):k(h)?(d=ct(function(t,e){t===this._ticket&&(u.setContent(e,l,n,c,a),this._updatePosition(n,a,r,o,u,i,s))},this),this._ticket=e,h(i,e,d)):h),u.setContent(t,l,n,c,a),u.show(n,c),this._updatePosition(n,a,r,o,u,i,s))},kM.prototype._getNearestPoint=function(t,e,n,i){return"axis"===n||F(e)?{color:i||("html"===this._renderMode?"#fff":"none")}:F(e)?void 0:{color:i||e.color||e.borderColor}},kM.prototype._updatePosition=function(t,e,n,i,r,o,a){var s,l=this._api.getWidth(),u=this._api.getHeight(),h=(e=e||t.get("position"),r.getSize()),c=t.get("align"),p=t.get("verticalAlign"),d=a&&a.getBoundingRect().clone();a&&d.applyTransform(a.transform),F(e=k(e)?e([n,i],o,r.el,d,{viewSize:[l,u],contentSize:h.slice()}):e)?(n=eo(e[0],l),i=eo(e[1],u)):R(e)?((o=e).width=h[0],o.height=h[1],n=(o=Rp(o,{width:l,height:u})).x,i=o.y,p=c=null):i=(n=(s=V(e)&&a?function(t,e,n,i){var r=n[0],o=n[1],a=Math.ceil(Math.SQRT2*i)+8,s=0,l=0,u=e.width,h=e.height;switch(t){case"inside":s=e.x+u/2-r/2,l=e.y+h/2-o/2;break;case"top":s=e.x+u/2-r/2,l=e.y-o-a;break;case"bottom":s=e.x+u/2-r/2,l=e.y+h+a;break;case"left":s=e.x-r-a,l=e.y+h/2-o/2;break;case"right":s=e.x+u+a,l=e.y+h/2-o/2}return[s,l]}(e,d,h,t.get("borderWidth")):function(t,e,n,i,r,o,a){var n=n.getSize(),s=n[0],n=n[1];null!=o&&(i<t+s+o+2?t-=s+o:t+=o);null!=a&&(r<e+n+a?e-=n+a:e+=a);return[t,e]}(n,i,r,l,u,c?null:20,p?null:20))[0],s[1]),c&&(n-=PM(c)?h[0]/2:"right"===c?h[0]:0),p&&(i-=PM(p)?h[1]/2:"bottom"===p?h[1]:0),lM(t)&&(o=n,a=i,e=l,d=u,c=(c=r).getSize(),p=c[0],c=c[1],o=Math.min(o+p,e)-p,a=Math.min(a+c,d)-c,o=Math.max(o,0),a=Math.max(a,0),n=(s=[o,a])[0],i=s[1]),r.moveTo(n,i)},kM.prototype._updateContentNotChangedOnAxis=function(n,o){var t=this._lastDataByCoordSys,a=this._cbParamsList,s=!!t&&t.length===n.length;return s&&O(t,function(t,e){var t=t.dataByAxis||[],r=(n[e]||{}).dataByAxis||[];(s=s&&t.length===r.length)&&O(t,function(t,e){var e=r[e]||{},n=t.seriesDataIndices||[],i=e.seriesDataIndices||[];(s=s&&t.value===e.value&&t.axisType===e.axisType&&t.axisId===e.axisId&&n.length===i.length)&&O(n,function(t,e){e=i[e];s=s&&t.seriesIndex===e.seriesIndex&&t.dataIndex===e.dataIndex}),a&&O(t.seriesDataIndices,function(t){var t=t.seriesIndex,e=o[t],t=a[t];e&&t&&t.data!==e.data&&(s=!1)})})}),this._lastDataByCoordSys=n,this._cbParamsList=o,!!s},kM.prototype._hide=function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},kM.prototype.dispose=function(t,e){!b.node&&e.getDom()&&(ly(this,"_updatePosition"),this._tooltipContent.dispose(),YS("itemTooltip",e))},kM.type="tooltip",kM);function kM(){var t=null!==TM&&TM.apply(this,arguments)||this;return t.type=kM.type,t}function DM(t,e,n){for(var i=e.ecModel,r=n?(r=new Rc(n,i,i),new Rc(e.option,r,i)):e,o=t.length-1;0<=o;o--){var a=t[o];(a=a&&(V(a=a instanceof Rc?a.get("tooltip",!0):a)?{formatter:a}:a))&&(r=new Rc(a,r,i))}return r}function AM(t,e){return t.dispatchAction||ct(e.dispatchAction,e)}function PM(t){return"center"===t||"middle"===t}B_(function(t){B_(rM),t.registerComponentModel(aM),t.registerComponentView(IM),t.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},Ft),t.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},Ft)}),B_(rx);var LM={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},OM=(RM.prototype.evaluate=function(t){var e=typeof t;return V(e)?this._condVal.test(t):!!H(e)&&this._condVal.test(t+"")},RM);function RM(t){null==(this._condVal=V(t)?new RegExp(t):_t(t)?t:null)&&f("")}EM.prototype.evaluate=function(){return this.value};var NM=EM;function EM(){}BM.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(!t[e].evaluate())return!1;return!0};var zM=BM;function BM(){}VM.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(t[e].evaluate())return!0;return!1};var FM=VM;function VM(){}GM.prototype.evaluate=function(){return!this.child.evaluate()};var HM=GM;function GM(){}UM.prototype.evaluate=function(){for(var t=!!this.valueParser,e=(0,this.getValue)(this.valueGetterParam),n=t?this.valueParser(e):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(t?n:e))return!1;return!0};var WM=UM;function UM(){}function XM(t,e){if(!0===t||!1===t)return(n=new NM).value=t,n;var n;if(qM(t)||f(""),t.and)return YM("and",t,e);if(t.or)return YM("or",t,e);if(t.not)return n=e,qM(o=(o=t).not)||f(""),(l=new HM).child=XM(o,n),l.child||f(""),l;for(var i=t,r=e,o=r.prepareGetValue(i),a=[],s=ht(i),l=i.parser,u=l?Ff(l):null,h=0;h<s.length;h++){var c,p=s[h];"parser"===p||r.valueGetterAttrMap.get(p)||(c=Bt(LM,p)?LM[p]:p,p=i[p],p=u?u(p):p,(c=function(t,e){return"eq"===t||"ne"===t?new Xf("eq"===t,e):Bt(Vf,t)?new Hf(t,e):null}(c,p)||"reg"===c&&new OM(p))||f(""),a.push(c))}return a.length||f(""),(l=new WM).valueGetterParam=o,l.valueParser=u,l.getValue=r.getValue,l.subCondList=a,l}function YM(t,e,n){e=e[t],F(e)||f(""),e.length||f(""),t=new("and"===t?zM:FM);return t.children=B(e,function(t){return XM(t,n)}),t.children.length||f(""),t}function qM(t){return R(t)&&!st(t)}jM.prototype.evaluate=function(){return this._cond.evaluate()};var ZM=jM;function jM(t,e){this._cond=XM(t,e)}var KM={type:"echarts:filter",transform:function(t){for(var e,n,i=t.upstream,r=(t=t.config,n={valueGetterAttrMap:E({dimension:!0}),prepareGetValue:function(t){var e=t.dimension,t=(Bt(t,"dimension")||f(""),i.getDimensionInfo(e));return t||f(""),{dimIdx:t.index}},getValue:function(t){return i.retrieveValueFromItem(e,t.dimIdx)}},new ZM(t,n)),o=[],a=0,s=i.count();a<s;a++)e=i.getRawDataItem(a),r.evaluate()&&o.push(e);return{data:o}}},$M={type:"echarts:sort",transform:function(t){for(var a=t.upstream,t=t.config,t=xo(t),s=(t.length||f(""),[]),t=(O(t,function(t){var e=t.dimension,n=t.order,i=t.parser,t=t.incomparable,e=(null==e&&f(""),"asc"!==n&&"desc"!==n&&f(""),t&&"min"!==t&&"max"!==t&&f(""),"asc"!==n&&"desc"!==n&&f(""),a.getDimensionInfo(e)),r=(e||f(""),i?Ff(i):null);i&&!r&&f(""),s.push({dimIdx:e.index,parser:r,comparator:new Wf(n,t)})}),a.sourceFormat),e=(t!==Zp&&t!==jp&&f(""),[]),n=0,i=a.count();n<i;n++)e.push(a.getRawDataItem(n));return e.sort(function(t,e){for(var n=0;n<s.length;n++){var i=s[n],r=a.retrieveValueFromItem(t,i.dimIdx),o=a.retrieveValueFromItem(e,i.dimIdx),i=(i.parser&&(r=i.parser(r),o=i.parser(o)),i.comparator.evaluate(r,o));if(0!==i)return i}return 0}),{data:e}}};B_(function(t){t.registerTransform(KM),t.registerTransform($M)}),t.Axis=Pc,t.ChartView=Kg,t.ComponentModel=g,t.ComponentView=Xg,t.List=mv,t.Model=Rc,t.PRIORITY=Uy,t.SeriesModel=zg,t.color=_i,t.connect=function(e){var t;return F(e)&&(t=e,e=null,O(t,function(t){null!=t.group&&(e=t.group)}),e=e||"g_"+M0++,O(t,function(t){t.group=e})),b0[e]=!0,e},t.dataTool={},t.dependencies={zrender:"5.6.1"},t.disConnect=Wy,t.disconnect=C0,t.dispose=function(t){V(t)?t=w0[t]:t instanceof s0||(t=I0(t)),t instanceof s0&&!t.isDisposed()&&t.dispose()},t.env=b,t.extendChartView=function(t){return t=Kg.extend(t),Kg.registerClass(t),t},t.extendComponentModel=function(t){return t=g.extend(t),g.registerClass(t),t},t.extendComponentView=function(t){return t=Xg.extend(t),Xg.registerClass(t),t},t.extendSeriesModel=function(t){return t=zg.extend(t),zg.registerClass(t),t},t.format=Dc,t.getCoordinateSystemDimensions=function(t){if(t=Sd.get(t))return t.getDimensionsInfo?t.getDimensionsInfo():t.dimensions.slice()},t.getInstanceByDom=I0,t.getInstanceById=function(t){return w0[t]},t.getMap=function(t){var e=Am.getMap;return e&&e(t)},t.graphic=$c,t.helper=Xy,t.init=function(t,e,n){var i=!(n&&n.ssr);if(i){var r=I0(t);if(r)return r}return(r=new s0(t,e,n)).id="ec_"+S0++,w0[r.id]=r,i&&Bo(t,T0,r.id),i0(r),Dm.trigger("afterinit",r),r},t.innerDrawElementOnCanvas=bm,t.matrix=Ve,t.number=Qo,t.parseGeoJSON=e1,t.parseGeoJson=e1,t.registerAction=R0,t.registerCoordinateSystem=N0,t.registerLayout=E0,t.registerLoading=V0,t.registerLocale=Xc,t.registerMap=H0,t.registerPostInit=P0,t.registerPostUpdate=L0,t.registerPreprocessor=D0,t.registerProcessor=A0,t.registerTheme=k0,t.registerTransform=G0,t.registerUpdateLifecycle=O0,t.registerVisual=z0,t.setCanvasCreator=function(t){T({createCanvas:t})},t.setPlatformAPI=T,t.throttle=ay,t.time=jh,t.use=B_,t.util=Tc,t.vector=re,t.version="5.6.0",t.zrUtil=Ht,t.zrender=Qr});