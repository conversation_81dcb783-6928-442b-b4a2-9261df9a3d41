<template>
  <view class="sleep-container">
    <view class="titles">
      <view class="left">我的健康数据</view>
      <view class="right" @click="handleRecord">全部数据</view>
    </view>
    <view class="chart-container">
      <l-echart ref="chartRef" @finished="init"></l-echart>
    </view>
  </view>
</template>

<script>
import LEchart from "@/pagesA/components/lime-echart/components/l-echart/l-echart.vue";
const echarts = require('@/pagesA/static/echarts.min.js');
import { getHealthCheckHistory } from '@/api/healthManage.js'
export default {
  components: {
    LEchart, 
  },
  props: {
    params: {
      type: Object,
      default: () => ({
        startDate: '2025-3-01',
        endDate: '2025-3-10'
      })
    }
  },
  data() {
    return {
      checkInDate: [],
      blood: [],
      option: {
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            type: 'line',
            smooth: true,
            showSymbol: false, // 隐藏数据点
            lineStyle: { color: '#5db87a', width: 2 },
            areaStyle: { color: 'rgba(174, 227, 191, 0.7)' },
            data: []
          }
        ]
      }
    };
  },
  methods: {
    handleRecord() {
      uni.$u.route({
        url: "pagesA/health/data",
      });
    },
    initData(chart) {
      console.log("123");
      // const params = {
      //  startDate: '2025-3-01',
      //  endDate: '2025-3-10'
      // }
      getHealthCheckHistory(this.params).then(res => {
        // console.log("res", res);
        this.option.xAxis.data = res.data.sleepRecord.map(item => this.formatDate(item.checkInDate));
        this.option.series[0].data = res.data.sleepRecord.map(item => item.sleepTime);
        // console.log(this.bmi);
        chart.setOption(this.option)
      })
    },
    formatDate(inputDate) {
      const date = new Date(inputDate); // 解析日期字符串
      const month = String(date.getMonth() + 1); // 获取月份
      const day = String(date.getDate());        // 获取日期

      return `${month}/${day}`;
    },
    async init() {
      const chart = await this.$refs.chartRef.init(echarts);
      this.initData(chart)
    }
  }
}
</script>

<style scoped lang="scss">
.sleep-container {
  width: 100%;
  height: 500rpx;
  background-color: #fff;
  border-radius: 20rpx;
  margin-top: 40rpx;

  .titles {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;

    .left {
      font-weight: 800;
      font-size: 32rpx;
      color: #2D3642;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 12rpx;
        background: linear-gradient(90deg, #0691FF 0%, rgba(17, 122, 205, 0) 100%);
        bottom: 2rpx;
        left: 0;
      }
    }

    .right {
      font-weight: 400;
      font-size: 24rpx;
      color: #2D3642;
    }
  }

  .chart-container {
    width: 100%;
    height: 400rpx;
  }
}
</style>