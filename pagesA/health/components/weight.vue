<template>
  <view class="weight-container">
    <view class="titles">
      <view class="left">体重</view>
      <view class="right">kg/日期</view>
    </view>
    <view class="chart-container">
      <l-echart v-if="isNoEmpty" ref="chartRef" @finished="init"></l-echart>
      <view class="empty-wrap" v-else>
        <u-empty
          icon="https://ma.aimeyear.com/staticFile/images/healthManage/empty_data.png"
          width="100"
          height="100"
          text="暂无数据"
        >
        </u-empty>
      </view>
    </view>
  </view>
</template>

<script>
import LEchart from "@/pagesA/components/lime-echart/components/l-echart/l-echart.vue";
const echarts = require("@/pagesA/static/echarts.min.js");
let chart = null;
export default {
  components: {
    LEchart, 
  },
  props: {
    echartData: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      option: {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "line",
          },
          confine: true,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: this.echartData.x || [],
          axisLine: {
            lineStyle: {
              color: "#E5E5E5",
            },
          },
          axisLabel: {
            color: "#999999",
            fontSize: 12,
            formatter: (value) => {
              // 将 YYYY-MM-DD 格式转换为 MM-DD
              return value.slice(5); // 截取月-日部分
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              type: "dashed",
              color: "#E5E5E5",
            },
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#999999",
            fontSize: 12,
          },
        },
        series: [
          {
            data: this.echartData.y || [],
            type: "bar",
            barWidth: 12,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#0D95FF",
                },
                {
                  offset: 1,
                  color: "rgba(13,149,255,0)",
                },
              ]),
              borderRadius: [8, 8, 0, 0],
            },
          },
        ],
      },
    };
  },
  computed: {
    isNoEmpty() {
      let { x, y } = this.echartData || {};
      return (x && x.length > 0) || (y && y.length > 0);
    },
  },
  watch: {
    echartData: {
      handler(newVal) {
        this.updateChart(newVal);
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    async init() {
		console.log('init');
      chart = await this.$refs.chartRef.init(echarts);
      this.updateChart(this.echartData);
    },

    updateChart(data) {
      console.log("echarts", echarts);
      if (!chart) return;

      const option = {
        ...this.option,
        xAxis: {
          ...this.option.xAxis,
          data: data.x || [],
        },
        series: [
          {
            ...this.option.series[0],
            data: data.y || [],
          },
        ],
      };

      chart.setOption(option);
    },
  },
};
</script>

<style scoped lang="scss">
.weight-container {
  width: 100%;
  height: 500rpx;
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 40rpx;

  .titles {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;

    .left {
      font-weight: 800;
      font-size: 32rpx;
      color: #2d3642;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 12rpx;
        background: linear-gradient(90deg, #0691ff 0%, rgba(17, 122, 205, 0) 100%);
        bottom: 2rpx;
        left: 0;
      }
    }

    .right {
      font-weight: 400;
      font-size: 24rpx;
      color: #2d3642;
    }
  }

  .chart-container {
    width: 100%;
    height: 400rpx;
    position: relative;

    .empty-wrap {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
