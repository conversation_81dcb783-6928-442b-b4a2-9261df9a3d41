<template>
  <view class="ww-health-scontainer">
    <page-nav title="打卡记录" url="pagesA/healthManage/index" :border="false"></page-nav>
    <view class="search-container">
      <!-- 日期选择 -->
      <view class="example-body">
        <uni-datetime-picker v-model="range" type="daterange" @change="maskClick" :clear-icon="false" />
      </view>
    </view>
    <view>
      <weight :echartData="weightRecord" />
      <bmi :echartData="bmiRecord" />
      <blood :echartData="bloodRecord" />
      <heartRate :echartData="heartRateRecord" />
      <fastBloodGluco :echartData="fastBloodGlucoRecord" />
      <view style="padding-bottom: 40rpx;"></view>
    </view>
  </view>
</template>

<script>
import weight from './components/weight.vue';
import bmi from './components/bmi.vue'
import blood from './components/blood.vue'
import heartRate from './components/heartRate.vue'
import fastBloodGluco from './components/fastBloodGluco.vue'
import { getHealthCheckHistory } from '@/api/healthManage.js'
export default {
  components: {
    weight,
    bmi,
    blood,
    heartRate,
    fastBloodGluco
  },
  data() {
    const today = new Date();
    const lastWeek = new Date(today);
    lastWeek.setDate(today.getDate() - 6); // 获取7天前的日期（包含今天）

    return {
      form: [],
      range: [
        this.formatDate(lastWeek),
        this.formatDate(today)
      ],
      weightRecord: {
        x: [],
        y: []
      },
      bmiRecord: {},
      bloodRecord: {
        x: [],
        systolic: [],
        diastolic: []
      },
      heartRateRecord: {
        x: [],
        y: []
      },
      fastBloodGlucoRecord: {
        x: [],
        y: []
      }
    };
  },
  onLoad() {
    this.getHealthData();
  },
  methods: {
    async getHealthData(params = {}) {
      const queryParams = params.startDate ? params : {
        startDate: this.range[0],
        endDate: this.range[1]
      };

      const res = await getHealthCheckHistory(queryParams);
      console.log('getHealthData res:', res);
      const data = res.data;

      this.weightRecord = {
        x: (data.weightRecord || []).map(item => item.checkInDate),
        y: (data.weightRecord || []).map(item => item.weight)
      };

      this.bmiRecord = {
        x: (data.weightRecord || []).map(item => item.checkInDate),
        y: (data.weightRecord || []).map(item => item.bmi)
      };

      // 处理血压数据
      this.bloodRecord = {
        x: (data.bloodPressureRecord || []).map(item => item.checkInDate),
        systolic: (data.bloodPressureRecord || []).map(item => item.systolic),
        diastolic: (data.bloodPressureRecord || []).map(item => item.diastolic)
      };

      // 处理心率数据
      this.heartRateRecord = {
        x: (data.bloodPressureRecord || []).map(item => item.checkInDate),
        y: (data.bloodPressureRecord || []).map(item => item.heartrate)
      };

      // 处理空腹血糖数据
      this.fastBloodGlucoRecord = {
        x: (data.bloodSugarRecord || []).map(item => item.checkInDate),
        y: (data.bloodSugarRecord || []).map(item => item.fastBloodGluco)
      };
    },
    maskClick(e) {
      console.log('日期变化:', e, this.range);
      if (this.range && this.range.length === 2) {
        this.getHealthData({
          startDate: this.range[0],
          endDate: this.range[1]
        });
      }
    },
    // 格式化日期为 YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  }
}
</script>

<style lang="scss">
.ww-health-scontainer {
  padding: 20rpx;
  background: #f6f7fb;
  min-height: 100vh;

  .search-container {
    background: #ffffff;
    border-radius: 22rpx;
    padding: 20rpx 30rpx;
	margin-bottom: 30rpx;
  }
}
</style>
