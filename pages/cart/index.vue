<template>
    <view class="container">
        <!-- 顶部导航 -->
        <u-navbar title="购物车" bgColor="transparent" placeholder>
            <template #left>
                <text class="manage-btn" @click="toggleManageMode">{{ isManageMode ? '取消' : '管理' }}</text>
            </template>
        </u-navbar>

        <!-- 购物车内容 -->
        <view class="cart-content">
            <!-- 使用z-paging包裹商品列表 -->
            <z-paging ref="paging" v-model="cartItems" @query="queryList" :loading-more-enabled="false"
                empty-view-text="购物车是空的" :safe-area-inset-top="false" :fixed="false" :auto="false">
                <!-- 购物车商品列表 -->
                <view class="cart-list">
                    <cart-item v-for="item in cartItems" :key="item.id" :item="item" @toggle-select="toggleItemSelect"
                        @update-quantity="updateItemQuantity" @remove-item="removeItem"></cart-item>
                </view>
            </z-paging>
        </view>

        <!-- 底部结算栏 -->
        <view class="bottom-bar" v-if="cartItems.length > 0">
            <view class="select-all" @click="toggleSelectAll">
                <view class="checkbox" :class="{ checked: isAllSelected }">
                    <u-icon v-if="isAllSelected" name="checkmark" color="#fff" size="16"></u-icon>
                </view>
                <text class="select-all-text">全选</text>
            </view>

            <!-- 价格信息 - 非管理模式下显示 -->
            <view class="total-section" v-if="!isManageMode">
                <view class="total-info">
                    <text class="total-label">合计：</text>
                    <text class="total-price"><text class="price-icon">¥ </text> {{ totalPrice.toFixed(2) }}</text>
                </view>
                <view class="saved-info">已优惠：{{ savedAmount.toFixed(2) }} (原价{{ originalTotalPrice.toFixed(2) }})
                </view>
            </view>

            <get-phone :customStyle="{ width: '200rpx' }">
                <view class="checkout-btn" :class="{ mode: isManageMode }"
                    @click="isManageMode ? deleteSelectedItems() : handleCheckout()">
                    <text class="checkout-text">{{ isManageMode ? '删除' : `去结算 (${selectedCount})` }}</text>
                </view>
            </get-phone>

        </view>
    </view>
</template>

<script>
import ZPMixin from '@/uni_modules/z-paging/components/z-paging/js/z-paging-mixin'
import CartItem from './components/cart-item.vue'
import { getCartList, updateCartItemQuantity, removeCartItems } from '@/api/cart.js'

export default {
    mixins: [ZPMixin],
    components: {
        CartItem
    },
    data() {
        return {
            // 模拟购物车数据
            cartItems: [],
            // 是否处于管理模式
            isManageMode: false,
            // 数量更新的loading状态，用于防止并发请求
            quantityUpdateLoading: new Set(),
            // 节流控制器 - 存储每个商品的节流定时器
            quantityUpdateTimers: new Map(),
            // 删除操作的loading状态
            deleteLoading: false
        }
    },
    computed: {
        // 是否全选
        isAllSelected() {
            return this.cartItems.length > 0 && this.cartItems.every(item => item.selected);
        },

        // 选中的商品数量
        selectedCount() {
            return this.cartItems.filter(item => item.selected).length;
        },

        // 选中商品的总价
        totalPrice() {
            return this.cartItems
                .filter(item => item.selected)
                .reduce((total, item) => total + (item.price * item.quantity), 0);
        },

        // 选中商品的原价总计
        originalTotalPrice() {
            return this.cartItems
                .filter(item => item.selected)
                .reduce((total, item) => total + ((item.originalPrice || item.price) * item.quantity), 0);
        },

        // 节省的金额
        savedAmount() {
            return this.originalTotalPrice - this.totalPrice;
        }
    },
    beforeDestroy() {
        // 清理所有节流定时器，防止内存泄漏
        this.quantityUpdateTimers.forEach(timer => {
            clearTimeout(timer);
        });
        this.quantityUpdateTimers.clear();

        // 重置删除loading状态
        this.deleteLoading = false;
    },
    onShow() {
        this.$refs.paging.reload()
    },
    methods: {
        // z-paging数据查询方法
        async queryList(pageNo, pageSize) {
            try {
                console.log('购物车数据查询', { pageNo, pageSize });

                // 调用购物车列表接口
                const response = await getCartList();

                console.log('购物车接口响应：', response);

                if (response.code === 200 && response.rows) {
                    // 处理接口返回的数据结构
                    const { rows = [], total = 0 } = response;

                    console.log(`购物车数据加载成功，共${total}条记录，当前页${rows.length}条`);

                    // 为每个商品项添加选中状态（如果接口没有返回）
                    const processedData = rows.map(item => ({
                        ...item,
                        selected: item.selected !== undefined ? item.selected : false,
                        // 确保必要字段存在
                        id: item.id || item.goodsId,
                        image: item.image || item.goodsImage,
                        name: item.name || item.goodsName,
                        price: item.price || 0,
                        originalPrice: item.originalPrice || item.price || 0,
                        quantity: item.quantity || 1
                    }));

                    // 将处理后的数据传给z-paging
                    this.$refs.paging.complete(processedData);
                } else {
                    // 接口返回错误
                    console.error('获取购物车数据失败：', response.message || '未知错误');
                    uni.showToast({
                        title: response.message || '获取购物车数据失败',
                        icon: 'none'
                    });
                    // 传递空数组给z-paging
                    this.$refs.paging.complete([]);
                }

            } catch (error) {
                console.error('购物车数据查询异常：', error);

                // 处理网络异常
                let errorMessage = '网络异常，请稍后重试';
                if (error.message) {
                    errorMessage = error.message;
                } else if (error.data && error.data.message) {
                    errorMessage = error.data.message;
                }

                uni.showToast({
                    title: errorMessage,
                    icon: 'none'
                });

                // 传递空数组给z-paging，避免组件异常
                this.$refs.paging.complete([]);
            }
        },


        // 切换商品选中状态
        toggleItemSelect(itemId) {
            // TODO: 接口调用 - 更新商品选中状态
            // 接口地址: PUT /app/cart/select
            // 请求参数: { itemId: number, selected: boolean }
            // 响应数据: { code: 200, message: "success" }

            const item = this.cartItems.find(item => item.id === itemId);
            if (item) {
                item.selected = !item.selected;
            }
        },

        // 更新商品数量（集成节流控制和接口调用）
        updateItemQuantity(itemId, newQuantity) {
            // 参数验证
            if (!itemId || newQuantity < 1) {
                console.warn('数量更新参数无效：', { itemId, newQuantity });
                return;
            }

            // 防止同一商品的并发更新
            if (this.quantityUpdateLoading.has(itemId)) {
                console.log('商品数量更新中，跳过重复请求：', itemId);
                return;
            }

            // 乐观更新：先更新UI显示
            const item = this.cartItems.find(item => item.id === itemId);
            if (!item) {
                console.warn('未找到对应商品：', itemId);
                return;
            }

            const oldQuantity = item.quantity;
            item.quantity = newQuantity;

            // 节流控制：清除之前的定时器
            if (this.quantityUpdateTimers.has(itemId)) {
                clearTimeout(this.quantityUpdateTimers.get(itemId));
            }

            // 设置新的节流定时器
            const timer = setTimeout(async () => {
                await this.doUpdateQuantityAPI(itemId, newQuantity, oldQuantity, item);
                // 清除定时器记录
                this.quantityUpdateTimers.delete(itemId);
            }, 600); // 600毫秒节流

            // 保存定时器引用
            this.quantityUpdateTimers.set(itemId, timer);
        },

        // 实际的数量更新接口调用
        async doUpdateQuantityAPI(itemId, newQuantity, oldQuantity, item) {
            try {
                // 添加loading状态
                this.quantityUpdateLoading.add(itemId);

                console.log('调用数量更新接口：', { itemId, newQuantity });

                // 构建请求参数
                const params = {
                    id: itemId,
                    quantity: newQuantity
                };

                // 调用接口
                const response = await updateCartItemQuantity(params);

                if (response.code === 200) {
                    console.log('数量更新成功：', { itemId, newQuantity });

                    // 可选：显示成功提示（通常不需要，避免过多提示）
                    // uni.showToast({
                    //     title: '数量已更新',
                    //     icon: 'success',
                    //     duration: 1000
                    // });
                } else {
                    // 接口返回错误，回滚UI状态
                    console.error('数量更新失败：', response.message || '未知错误');
                    item.quantity = oldQuantity;

                    uni.showToast({
                        title: response.message || '更新失败，请重试',
                        icon: 'none'
                    });
                }

            } catch (error) {
                console.error('数量更新异常：', error);

                // 网络异常，回滚UI状态
                item.quantity = oldQuantity;

                let errorMessage = '网络异常，请稍后重试';
                if (error.message) {
                    errorMessage = error.message;
                } else if (error.data && error.data.message) {
                    errorMessage = error.data.message;
                }

                uni.showToast({
                    title: errorMessage,
                    icon: 'none'
                });

            } finally {
                // 移除loading状态
                this.quantityUpdateLoading.delete(itemId);
            }
        },

        // 切换全选状态
        toggleSelectAll() {
            const newSelectState = !this.isAllSelected;
            this.cartItems.forEach(item => {
                item.selected = newSelectState;
            });
        },

        // 去结算
        handleCheckout() {
            const selectedItems = this.cartItems.filter(item => item.selected);
            if (selectedItems.length === 0) {
                uni.showToast({
                    title: '请选择要结算的商品',
                    icon: 'none'
                });
                return;
            }

            // 将选中商品数据存储到全局状态或通过参数传递
            // 这里使用uni.setStorageSync临时存储，实际项目中可以使用Vuex等状态管理
            uni.setStorageSync('orderItems', selectedItems);

            console.log('去结算，选中商品：', selectedItems);

            // 跳转到确认订单页面
            uni.navigateTo({
                url: '/pages/order/confirm'
            });
        },

        // 切换管理模式
        toggleManageMode() {
            this.isManageMode = !this.isManageMode;
        },

        // 删除选中的商品
        deleteSelectedItems() {
            const selectedItems = this.cartItems.filter(item => item.selected);
            if (selectedItems.length === 0) {
                uni.showToast({
                    title: '请选择要删除的商品',
                    icon: 'none'
                });
                return;
            }

            // 确认删除
            uni.showModal({
                title: '确认删除',
                content: `确定要删除选中的${selectedItems.length}个商品吗？`,
                success: async (res) => {
                    if (res.confirm) {
                        const selectedIds = selectedItems.map(item => item.id);
                        await this.doRemoveItems(selectedIds);
                    }
                }
            });
        },

        // 删除单个商品
        removeItem(itemId) {
            // 确认删除
            uni.showModal({
                title: '确认删除',
                content: '确定要删除这个商品吗？',
                success: async (res) => {
                    if (res.confirm) {
                        await this.doRemoveItems([itemId]);
                    }
                }
            });
        },

        // 统一的删除接口调用方法
        async doRemoveItems(itemIds) {
            // 防止重复删除操作
            if (this.deleteLoading) {
                console.log('删除操作进行中，跳过重复请求');
                return;
            }

            try {
                // 设置loading状态
                this.deleteLoading = true;

                console.log('调用删除接口：', { itemIds });

                // 调用删除接口
                const response = await removeCartItems(itemIds);

                if (response.code === 200) {
                    console.log('删除成功：', { itemIds });

                    // 删除成功，更新本地数据
                    this.cartItems = this.cartItems.filter(item => !itemIds.includes(item.id));

                    // 如果是管理模式且没有商品了，退出管理模式
                    if (this.isManageMode && this.cartItems.length === 0) {
                        this.isManageMode = false;
                    }

                    uni.showToast({
                        title: `删除成功`,
                        icon: 'success'
                    });

                } else {
                    // 接口返回错误
                    console.error('删除失败：', response.message || '未知错误');

                    uni.showToast({
                        title: response.message || '删除失败，请重试',
                        icon: 'none'
                    });
                }

            } catch (error) {
                console.error('删除异常：', error);

                let errorMessage = '网络异常，请稍后重试';
                if (error.message) {
                    errorMessage = error.message;
                } else if (error.data && error.data.message) {
                    errorMessage = error.data.message;
                }

                uni.showToast({
                    title: errorMessage,
                    icon: 'none'
                });

            } finally {
                // 移除loading状态
                this.deleteLoading = false;
            }
        },

        // 手动刷新购物车数据
        refreshCartData() {
            if (this.$refs.paging) {
                this.$refs.paging.reload();
            }
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep {

    .u-number-box__plus,
    .u-number-box__minus {
        width: 40rpx !important;
        background-color: transparent !important;
    }

    .u-number-box__input {
        border-radius: 8rpx;
    }
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: linear-gradient(to bottom,
            #e3c997,
            #ebd7b1,
            #f7edda,
            #fbf5ea,
            #fbf6ed,
            #faf7f5);
    overflow: hidden;

    .manage-btn {
        font-size: 24rpx;
        color: #222;
    }

    .cart-content {
        flex: 1;
        padding: 0 25rpx;

        /* z-paging容器样式 */
        .z-paging-content {
            height: 100%;
        }

        .cart-list {
            padding-top: 20rpx;
            /* 商品列表样式已在cart-item组件中定义 */
        }


    }

    .bottom-bar {
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 30rpx;
        z-index: 999;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;

        .select-all {
            display: flex;
            align-items: center;
            margin-right: 30rpx;

            .checkbox {
                width: 34rpx;
                height: 34rpx;
                border: 2rpx solid #ddd;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 15rpx;

                &.checked {
                    background-color: #E5B05E;
                    border-color: #E5B05E;
                }
            }

            .select-all-text {
                font-size: 24rpx;
                color: #222;
            }
        }

        .total-section {
            flex: 1;
            margin-right: 30rpx;

            .total-info {
                display: flex;
                align-items: baseline;
                margin-bottom: 5rpx;

                .total-label {
                    font-size: 24rpx;
                    color: #000;
                }

                .total-price {
                    font-size: 38rpx;
                    font-weight: bold;
                    color: #E5B05E;

                    .price-icon {
                        font-size: 27rpx;
                        margin-right: 6rpx;
                    }
                }
            }

            .saved-info {
                font-size: 20rpx;
                color: #3D3D3D;
            }
        }

        .checkout-btn {
            width: 200rpx;
            height: 72rpx;
            line-height: 72rpx;
            background-color: #E5B05E;
            border-radius: 50rpx;
            text-align: center;

            &.mode {
                background-color: #ff0000
            }



            .checkout-text {
                font-size: 26rpx;
                color: #fff;
                font-weight: bold;
            }
        }
    }
}
</style>
