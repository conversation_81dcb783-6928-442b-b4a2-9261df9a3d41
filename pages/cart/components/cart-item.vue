<template>
    <view class="cart-item">
        <!-- 选择框 -->
        <view class="checkbox-container" :class="{ disabled: isCheckboxDisabled }" @click="toggleSelect">
            <view class="checkbox" :class="{ checked: item.selected, disabled: isCheckboxDisabled }">
                <u-icon v-if="item.selected" name="checkmark" color="#fff" size="16"></u-icon>
            </view>
        </view>

        <!-- 商品信息 -->
        <view class="product-info">
            <!-- 商品图片 -->
            <view class="product-image" @click="itemClick">
                <u-image :src="item.image" width="200rpx" height="200rpx"></u-image>
            </view>

            <!-- 商品详情 -->
            <view class="product-details">
                <view class="u-line-2 product-name" @click="itemClick">{{ item.name }}</view>


                <view class="out_stock" v-if="item.goodsStatus === '1'">商品已下架</view>
                <!-- 价格和数量控制 -->
                <view v-else class="price-quantity">
                    <!-- 价格信息 - 始终显示 -->
                    <view class="price-section" @click="itemClick">
                        <view class="original-price" v-if="item.originalPrice"><text class="price-icon">¥ </text> {{
                            item.originalPrice }}
                        </view>
                        <view class="current-price"><text class="price-icon">¥ </text> {{ item.price }}
                        </view>
                    </view>

                    <!-- 数量控制 - 始终显示 -->
                    <view class="quantity-control">
                        <u-number-box v-model="item.quantity" @change="onQuantityChange" :min="1" :max="item.goodsStock"
                            :integer="true" :button-size="24" :input-width="40" bg-color="#F5F5F5" color="#222"
                            :icon-style="{ fontSize: '10px' }"></u-number-box>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'CartItem',
    props: {
        item: {
            type: Object,
            required: true,
            default: () => ({})
        },
        isManageMode: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        // 判断勾选框是否应该被禁用
        isCheckboxDisabled() {
            // 当商品下架且不是管理模式时，禁用勾选框
            return this.item.goodsStatus === '1' && !this.isManageMode;
        }
    },
    methods: {
        itemClick() {
            uni.$u.route({
                type: "navigateTo",
                url: "/pages/prdDetail/index",
                params: {
                    id: this.item.goodsId,
                },
            });
        },
        // 切换选中状态
        toggleSelect() {
            // 如果勾选框被禁用，则不执行勾选操作
            if (this.isCheckboxDisabled) {
                return;
            }
            this.$emit('toggle-select', this.item.id);
        },

        // 数量变更处理
        onQuantityChange(e) {
            const newQuantity = e.value;

            // 数量验证
            if (newQuantity < 1) {
                // 数量小于1时，弹出确认删除对话框
                uni.showModal({
                    title: '确认删除',
                    content: '是否删除该商品？',
                    success: (res) => {
                        if (res.confirm) {
                            this.$emit('remove-item', this.item.id);
                        } else {
                            // 用户取消删除，恢复数量为1
                            this.$nextTick(() => {
                                this.item.quantity = 1;
                            });
                        }
                    }
                });
                return;
            }

            // 数量验证通过，触发更新事件
            if (newQuantity !== this.item.quantity) {
                this.$emit('update-quantity', this.item.id, newQuantity);
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.cart-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
    background-color: #fff;
    border-radius: 20rpx;
    margin-bottom: 20rpx;

    .checkbox-container {
        padding: 0 30rpx;
        display: flex;
        align-items: center;

        .checkbox {
            width: 34rpx;
            height: 34rpx;
            border: 2rpx solid #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            &.checked {
                background-color: #E5B05E;
                border-color: #E5B05E;
            }
        }
    }

    .product-info {
        flex: 1;
        display: flex;
        padding-right: 30rpx;

        .product-image {
            margin-right: 20rpx;
            border-radius: 8rpx;
            overflow: hidden;
        }

        .product-details {
            flex: 1;

            .product-name {
                font-size: 24rpx;
                font-weight: bold;
                color: #222;
                margin-bottom: 66rpx;
                line-height: 35rpx;
            }

            .out_stock {
                color: #999;
                font-size: 24rpx;
                margin-bottom: 20rpx;
            }

            .price-quantity {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;

                .price-section {
                    .original-price {
                        font-size: 19rpx;
                        color: #929292;
                        text-decoration: line-through;
                        margin-bottom: 5rpx;

                        .price-icon {
                            font-size: 16rpx;
                            margin-right: 6rpx;
                        }
                    }

                    .current-price {
                        font-size: 38rpx;
                        color: #E5B05E;
                        font-weight: bold;

                        .price-icon {
                            font-size: 27rpx;
                            margin-right: 6rpx;
                        }
                    }
                }

                .quantity-control {
                    display: flex;
                    align-items: center;


                }
            }
        }
    }
}
</style>
