<template>
    <view class="order_item">
        <view class="header_wrap">
            <view class="orderNum">
                订单号{{ item.orderNumber }}
            </view>
            <view class="copy" @click="copyOrderNumber">
                复制
                <!-- <u-icon name="file-text"></u-icon> -->
            </view>
            <view class="status" :style="{ color: statusDetail.color }">
                {{ statusDetail.label }}
            </view>
        </view>
        <u-line></u-line>
        <view class="item_content" @click="handleDetail" v-for="col in item.orderDetailList" :key="col.id">
            <view :style="{ position: 'relative' }">
                <prd-label :label="col.tagName"></prd-label>
                <!-- height="168rpx" -->
                <u--image class="icon_img" width="216rpx" height="216rpx" :showLoading="true" mode="aspectFit"
                    :src="col.goodsPic"></u--image>
            </view>

            <view class="left_wrap">
                <view class="title">{{ col.goodsName }}</view>
                <!-- <view class="time">{{ col.createTime }}</view> -->
                <view class="price_con">
                    <view class="price">
                        <view class="price_icon">￥</view>
                        <view class="amount">{{ col.goodsPrice }}</view>
                    </view>
                    <view class="count">
                        x{{ col.num }}
                    </view>
                </view>
            </view>
        </view>
        <u-line></u-line>
        <view class="footer_wrap">
            <view class="money_wrap">
                <view>合计：</view>
                <view class="unit">￥</view>
                <view class="money">{{ item.orderAmount }}</view>
            </view>
            <view v-if="['1', '7'].includes(item.status)" slot="value" class="action_wrap">
                <block v-if="item.status === '1'">
                    <u-button shape="circle" text="取消订单" :customStyle="btnStyles" @click="handleCancel"
                        style="margin-right: 10px;"></u-button>
                    <u-button shape="circle" text="继续付款" color="#117ACD"
                        :customStyle="{ ...btnStyles, marginLeft: '20rpx', color: '#fff' }"
                        @click="handleDetail"></u-button>
                </block>
                <u-button v-if="item.status === '7'" shape="circle" text="确认收货" color="#117ACD"
                    :customStyle="{ ...btnStyles, marginLeft: '20rpx', color: '#fff' }"
                    @click="handleReceive"></u-button>
            </view>
        </view>
    </view>
</template>

<script>
import {
    orderStatusEnums
} from '@/util/enums.js';

export default {
    props: {
        item: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    data() {
        return {

        }
    },
    computed: {
        statusDetail() {
            let item = orderStatusEnums.find(row => row.value === this.item.status);
            return item || orderStatusEnums[0]
        },
        btnStyles() {
            let styles = {
                height: 'auto',
                padding: '12rpx 20rpx',
                fontWeight: '400',
                fontSize: '28rpx',
                color: '#666666',
            }
            return styles;
        },
    },
    methods: {
        handleReceive() {
            this.$emit('onReceive')
        },
        handleDetail() {
            uni.$u.route('/pages/orderDetail/orderDetail', {
                orderId: this.item.id
            });

        },
        handleCancel() {
            this.$emit('onCancel')
        },
        copyOrderNumber() {
            uni.setClipboardData({
                data: this.item.orderNumber,
                success: () => {
                    uni.showToast({
                        title: '订单号已复制',
                        icon: 'success',
                        duration: 1500
                    });
                },
                fail: () => {
                    uni.showToast({
                        title: '复制失败',
                        icon: 'none',
                        duration: 1500
                    });
                }
            });
        },
    },
}
</script>

<style lang="scss" scoped>
.order_item {
    background: #fff;
    margin-top: 20rpx;
    padding: 0 20rpx;
    border-radius: 16rpx;
    overflow: hidden;

    .header_wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 28rpx 0;

        .orderNum {
            font-weight: 500;
            font-size: 30rpx;
            color: #142635;
        }

        .copy {
            font-size: 25rpx;
            color: #6E6E6E;
        }

        .status {
            font-weight: 500;
            font-size: 30rpx;
        }
    }


    .item_content {
        padding: 30rpx 0;
        display: flex;

        .left_wrap {
            padding-left: 20rpx;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;

            .title {
                font-weight: 500;
                font-size: 26rpx;
                color: #333333;
            }

            .time {
                font-weight: 500;
                font-size: 26rpx;
                color: rgba(51, 51, 51, 0.6);
            }

            .price_con {
                display: flex;

                align-items: center;
                justify-content: space-between;

                .price {
                    display: flex;
                    align-items: baseline;

                    .price_icon {
                        font-weight: bold;
                        font-size: 20rpx;
                        color: #FD3232;
                    }

                    .amount {
                        font-weight: bold;
                        font-size: 36rpx;
                        color: #FD3232;
                    }
                }

                .count {
                    font-weight: bold;
                    font-size: 36rpx;
                    color: #333333;
                }
            }
        }


    }

    .footer_wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;

        .money_wrap {
            font-weight: bold;
            font-size: 20rpx;
            color: #FD3232;
            display: flex;
            align-items: baseline;

            .unit {
                font-size: 20rpx;
            }

            .money {
                font-size: 36rpx;
            }
        }

        .action_wrap {
            display: flex;
            align-items: center;
        }
    }


}
</style>