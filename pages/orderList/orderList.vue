<template>
	<view class="content">
		<z-paging ref="paging" v-model="dataList" @query="queryList" :use-page-scroll="true">
			<view slot="top">
				<page-nav title="我的订单" navigatorType="switchTab" url="pages/user/index" :border="false"></page-nav>
				<tab-list v-model="status" :list="tabList" @onChange="tabsChange"></tab-list>
			</view>
			<view class="list">
				<view class="item" v-for="item in dataList" :key="item.id">
					<order-item :key="item.id" :item="item" @onReceive="handleReceive(item.orderId)"
						@onCancel="handleCancel(item.orderId)"></order-item>
				</view>
			</view>
		</z-paging>
		<!-- 取消订单弹框 -->
		<cancel-order-modal ref="cancelOrderModal" @onSuccess="onSuccess"></cancel-order-modal>
		<!-- 确认收货 -->
		<receive-modal ref="receiveModal" @onSuccess="onSuccess"></receive-modal>
	</view>
</template>

<script>
	import ZPMixin from '@/uni_modules/z-paging/components/z-paging/js/z-paging-mixin'
	import OrderItem from './components/orderItem.vue';
	import {
		getOrderList
	} from '@/api/order.js';
	import {
		orderStatusEnums
	} from '@/util/enums.js'

	export default {
		mixins: [ZPMixin],
		components: {
			OrderItem
		},
		data() {
			return {
				dataList: [],
				status: 'all',
				tabList: [{
					value: 'all',
					label: '全部',
					type: 'all'
				}, ...orderStatusEnums],
				// 订单类型与状态映射关系
				typeStatusMap: {
					'payment': '1', // 待付款
					'delivery': '2', // 待发货
					'receiving': '7', // 待收货
					'refund': '5', // 退款/售后
					'all': 'all' // 全部
				}
			}
		},
		onLoad(options) {
			console.log('订单列表页接收参数:', options);
			// 处理URL参数
			if (options.type && this.typeStatusMap[options.type]) {
				this.status = this.typeStatusMap[options.type];
				console.log('设置状态为:', this.status);
				// 如果有状态参数延迟一点执行reload，确保组件已加载完成
				setTimeout(() => {
					if (this.$refs.paging) {
						console.log('重新加载订单列表');
						this.$refs.paging.reload();
					}
				}, 100);
			}
		},
		methods: {
			tabsChange(index) {
				this.$refs.paging.reload();
			},
			handleReceive(orderId) {
				this.$refs.receiveModal.open(orderId);
			},
			handleCancel(orderId) {
				this.$refs.cancelOrderModal.open(orderId);
			},
			onSuccess() {
				this.$refs.paging.reload();
			},
			queryList(pageNum, pageSize) {
				const params = this.status !== 'all' ? {
					status: this.status
				} : {};
				console.log('查询参数:', params);
				getOrderList({
					pageNum,
					pageSize,
					reasonable: false,
					...params,
				}).then(res => {
					// 将请求结果通过complete传给z-paging处理，同时也代表请求结束，这一行必须调用
					this.$refs.paging.complete(res.rows);
				}).catch(res => {
					// 如果请求失败写this.$refs.paging.complete(false)，会自动展示错误页面
					// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
					// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
					this.$refs.paging.complete(false);
				})
			}
		},
	}
</script>

<style lang="scss" scoped>
	.content {
		background-color: #F9F9F9;

		.list {
			padding: 0 20rpx;
		}
	}
</style>