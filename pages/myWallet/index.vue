<template>
	<view class="page_wrap">
		<page-nav title="我的钱包" navigatorType="switchTab" url="pages/user/index" :border="false"></page-nav>
		<view class="wallet_wrap">
			<view class="record" @click="handleRecord">
				提现记录
			</view>
			<view class="wallet_list">
				<view class="wallet_item">
					<view class="desc">
						总提现金额
					</view>
					<view class="amount">
						￥{{walletDetail.estimatedBalance || 0}}
					</view>
				</view>
				<view class="wallet_item">
					<view class="desc">
						可提现金额
					</view>
					<view class="amount">
						￥{{walletDetail.withdrawalAmount || 0}}
					</view>
				</view>
			</view>

		</view>
		<view class="footer_wrap">
			<view class="footer_tip">
				商品确认收货后可提现
			</view>
			<u-button color="#117ACD" :loading="submitLoading" :customStyle="payStyles" @click="handleWithdraw">
				提现
			</u-button>
		</view>

		<u-popup :show="showWithdrawPopup" mode="bottom" @close="this.showWithdrawPopup = false">
			<view class="popup-content">
				<view class="popup-title">请输入提现金额</view>
				<u--input v-model="withdrawAmount" type="digit" placeholder="请输入金额"
					:customStyle="{ margin: '20rpx 0' }" />
				<u-button type="primary" @click="submitWithdraw">确认提现</u-button>
			</view>
		</u-popup>

	</view>

</template>

<script>
	import {
		data
	} from '../../uni_modules/uview-ui/libs/mixin/mixin';
	import {
		getMyWallet,
		applyWithdraw,
		withdrawalCancel
	} from '@/api/user.js';
	export default {
		data() {
			return {
				walletDetail: {},
				submitLoading: false,
				showWithdrawPopup: false,
				withdrawAmount: '',
				withdrawalRecordId: ''
			}
		},
		computed: {
			payStyles() {
				return {
					height: '98rpx',
					fontWeight: '600',
					fontSize: '36rpx',
					color: '#FFFFFF',
					borderRadius: '14rpx'
				}
			}
		},
		onShow() {
			this.initData();
		},
		methods: {
			handleRecord() {
				uni.$u.route('/pages/withdrawRecord/index');
			},
			handleWithdraw() {
				this.withdrawAmount = '';
				this.showWithdrawPopup = true;
				// uni.$u.toast('功能正在开发中');
				// return
				// uni.$u.route('/pages/withdraw/index');
			},
			async submitWithdraw() {
				const reg = /^(0|[1-9]\d*)(\.\d{1,2})?$/;
				const withdrawalAmount = Number(this.withdrawAmount);
				const limitAmount = 200;
				const maxAmount = this.walletDetail.withdrawalAmount || 0;

				if (!reg.test(withdrawalAmount)) {
				  return uni.$u.toast('请输入有效的金额，最多两位小数');
				}

				if (!withdrawalAmount || withdrawalAmount <= 0) {
					return uni.$u.toast('请输入有效金额');
				}

				if (withdrawalAmount > maxAmount) {
					return uni.$u.toast('金额超过可提现余额');
				}
				
				if (withdrawalAmount > 200) {
					return uni.$u.toast('单笔提现金额不能超过200元');
				}



				this.submitLoading = true;

				const res = await applyWithdraw({
					withdrawalAmount
				});
				if (res.code === 200 && res.data) {
					console.log("data", res.data);
					const mchId = res.data?.mchId;
					const appId = res.data?.appId;
					const packageInfo = res.data?.packageInfo;

					console.log("appid:", appId);
					console.log("mchId:", mchId);
					console.log("packageInfo:", packageInfo);
					this.withdrawalRecordId = res.data?.withdrawalRecordId;
					this.showWithdrawPopup = false;

					// 调用微信收款界面
					uni.requestMerchantTransfer({
						mchId: mchId,
						appId: appId,
						package: packageInfo,
						success: (res) => {
							console.log('res: ', res)
							uni.$u.toast('提现成功');

							this.initData(); // 刷新钱包
						},
						fail: (err) => {
							let errMsg = err.errMsg;
							if (errMsg == "requestMerchantTransfer:fail cancel") {
								errMsg = "取消转账";
							}
							uni.showToast({
								title: errMsg,
								icon: 'none'
							});
							withdrawalCancel({
								withdrawalRecordId: this.withdrawalRecordId
							});
							console.error('提现失败：', err);
						},
						complete: () => {
							this.submitLoading = false;
						}
					});
				} else {
					this.submitLoading = false;
					uni.$u.toast(res.msg || '提现失败');
				}
			},
			initData() {
				uni.showLoading({
					title: '加载中'
				});
				getMyWallet().then(res => {
					this.walletDetail = res.code === 200 ? res.data : {};
					uni.hideLoading();
				}).catch(() => {
					uni.hideLoading();
				})
			}
		},

	}
</script>

<style lang="scss" scoped>
	.page_wrap {
		background-color: #F8F9F9;
		min-height: 100vh;
		padding: 0 20rpx;


		.wallet_wrap {
			background-color: #117ACD;
			border-radius: 20rpx;
			padding: 0 0 40rpx 0;
			margin-top: 60rpx;
			color: #fff;

			.record {
				text-align: right;
				padding: 20rpx;
				font-size: 24rpx;
			}

			.wallet_list {
				display: flex;

				.wallet_item {
					flex: 1;
					text-align: center;

					.desc {
						font-size: 40rpx;
					}

					.amount {
						margin-top: 10rpx;
						font-size: 34rpx;
					}
				}
			}

		}

		.footer_wrap {

			box-sizing: border-box;
			width: 100%;
			margin-top: 180rpx;

			.footer_tip {
				text-align: center;
				font-weight: 500;
				font-size: 26rpx;
				color: rgba(51, 51, 51, 0.4);
				margin-bottom: 28rpx;
			}
		}

	}

	.popup-content {
		padding: 40rpx 30rpx;
		background: #fff;
		border-radius: 20rpx;
		text-align: center;

		.popup-title {
			font-size: 32rpx;
			font-weight: 600;
			margin-bottom: 20rpx;
		}
	}
</style>