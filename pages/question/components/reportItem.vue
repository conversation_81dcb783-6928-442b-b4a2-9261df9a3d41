<template>
	<view class="questionnaire_item">
		<view class="item_content" @click="handlePreview">
			<u--image class="icon_img" width="70rpx" height="86rpx" :showLoading="true" mode="aspectFit"
				:src="`${baseUrl}/detectActivation/pdf.png`"></u--image>

			<view class="left_wrap">
				<view class="title">{{ item.questionnaireName }}</view>
				<view class="time">{{ item.createTime }}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		previewPdf,
	} from '@/util/tools.js';

	export default {
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data(){
			return {
				baseUrl: process.env.VUE_APP_STATIC_FILE_URL
			}
		},
		methods: {
			handlePreview() {
				previewPdf(this.item.reportUrl);
			}
		},
	}
</script>

<style lang="scss" scoped>
	.questionnaire_item {
		background: #fff;
		margin-top: 20rpx;
		padding-left: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;

		.header_wrap {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 28rpx 0;

			.orderNum {
				font-weight: 500;
				font-size: 30rpx;
				color: #142635;
			}

			.status {
				font-weight: 500;
				font-size: 30rpx;
			}
		}


		.item_content {
			padding: 30rpx 0;
			display: flex;
			position: relative;

			.label {
				position: absolute;
				top: 0;
				right: 0;
				font-weight: 600;
				font-size: 28rpx;
				color: #FFFFFF;
				padding: 0 45rpx;
				background-color: #117ACD;
			}

			.label::before {
				content: ' ';
				position: absolute;
				left: 0;
				top: 0;
				z-index: 0;
				width: 45rpx;
				height: 100%;
				background: #fff url('https://ma.aimeyear.com/staticFile/images/detectActivation/label.png') no-repeat;
				background-size: auto 100%;
				background-position: left top;
			}

			.label::after {
				content: ' ';
				position: absolute;
				right: 0;
				top: 0;
				z-index: 0;
				width: 45rpx;
				height: 100%;
				background: #fff url('https://ma.aimeyear.com/staticFile/images/detectActivation/label.png') no-repeat;
				background-size: auto 100%;
				background-position: right top;
			}

			.left_wrap {
				padding-left: 20rpx;
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-around;

				.title {
					font-weight: 500;
					font-size: 26rpx;
					color: #333333;
				}

				.time {
					font-weight: 500;
					font-size: 26rpx;
					color: rgba(51, 51, 51, 0.6);
				}

				.price_con {
					display: flex;

					align-items: center;
					justify-content: space-between;

					.price {
						display: flex;
						align-items: baseline;

						.price_icon {
							font-weight: bold;
							font-size: 20rpx;
							color: #FD3232;
						}

						.amount {
							font-weight: bold;
							font-size: 36rpx;
							color: #FD3232;
						}
					}

					.count {
						font-weight: bold;
						font-size: 36rpx;
						color: #333333;
					}
				}
			}


		}

		.footer_wrap {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx 0;

			.money_wrap {
				font-weight: bold;
				font-size: 20rpx;
				color: #FD3232;
				display: flex;
				align-items: baseline;

				.unit {
					font-size: 20rpx;
				}

				.money {
					font-size: 36rpx;
				}
			}

			.action_wrap {
				display: flex;
				align-items: center;
			}
		}


	}
</style>