<template>
  <view class="survey-container  ww-survey-container">
    <page-nav navigatorType="switchTab" url="pages/user/index" bgColor="transparent" leftIconColor="#fff"
      :placeholder="false" :border="false"></page-nav>
    <!-- 顶部banner -->
    <view class="banner"> </view>
    <view class="content">
      <u--form :model="form" :rules="rules" ref="uForm" labelWidth="200rpx">
        <view v-for="(item, index) in questionList" :key="index" class="section">
          <view class="section-title">{{ item.categoryName }}</view>
          <u-form-item v-for="question in filteredQuestions(item)" :key="question.questionId"
            :required="question.isRequired === 1" :label="question.questionTitle"
            v-if="item.categoryName === '生活习惯' ? question.show : true"
            :labelPosition="question.questionTitle.length > 9 ? 'top' : 'left'" :labelWidth="question.questionTitle.length > 9
              ? 'initial'
              : `${question.questionTitle.length * 40}rpx`
              " :prop="question.questionId">
            <!-- input - 填空 radio - 单选 mulSelect- 多选 singleSelect - 下拉 disabled - 不可编辑  pickDate -日期选择 -->
            <!-- 填空 -->
            <u-input v-if="question.questionType === 'input'" v-model="form[question.questionId]" border="none"
              inputAlign="right" placeholder="请输入" readonly @change="inputChange(question.questionId)" />
            <!-- 单选 -->
            <block slot="right">
              <u-radio-group v-if="question.questionType === 'radio'" v-model="form[question.questionId]"
                placement="row" disabled>
                <u-radio :customStyle="{ marginLeft: '20rpx', color: '' }" v-for="option in question.options"
                  :key="option.value" activeColor="#117ACD" :name="option.value" :label="option.label"></u-radio>
              </u-radio-group>
            </block>
            <!-- 多选 -->
            <view v-if="question.questionType === 'mulSelect'">
              <u--textarea :value="getOptionLabels(question)" border="none" readonly placeholder="请选择"
                :autoHeight="true" :disabledColor="'#ffffff'" />
            </view>

            <!-- 日期选择 -->
            <pick-date v-if="question.questionType === 'pickDate'" readonly v-model="form[question.questionId]"
              @change="validateField(question.questionId)"></pick-date>
            <!-- 时间选择 -->
            <pick-date v-if="question.questionType === 'pickTime'" readonly mode="time"
              v-model="form[question.questionId]" @change="validateField(question.questionId)"></pick-date>
            <!-- 下拉 -->
            <block slot="right">
              <d-picker v-if="question.questionType === 'singleSelect'" readonly v-model="form[question.questionId]"
                valueName="value" labelName="label" :options="question.options"
                @change="pickChange(question.questionId)"></d-picker>
            </block>
          </u-form-item>
        </view>
      </u--form>
    </view>
    <!-- <view class="footer_wrap">
      <u-button
        color="#117ACD"
        :loading="submitLoading"
        :customStyle="payStyles"
        @click="handleSubmit"
      >
        提交
      </u-button>
    </view> -->
  </view>
</template>

<script>
import { getQuestion, commitQuestion } from "@/api/question";
import config from "@/common/config";
import { mapState } from "vuex";
export default {
  name: "SurveyPage",
  data() {
    return {
      submitLoading: false,
      questionList: [],
      form: {},
      rules: {},
    };
  },
  onLoad(options) {
    this.questionId = options.questionId;
    if (this.questionId) {
      this.initData();
    }
  },
  onUnload() {
    console.log("onUnload");
    this.submitQuestion();
  },
  onHide() {
    console.log("onHide");
    this.submitQuestion();
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.User.userInfo,
    }),
  },
  methods: {
    // 处理多选框改变事件
    checkboxChange(selectedOptions) {
      console.log("Selected options:", selectedOptions);
    },
    changeBmi(questionId) {
      const weight = this.form[config.question.weightId];
      const height = this.form[config.question.heightId];
      // 如果是身高和体重，则计算BMI
      if (
        [config.question.weightId, config.question.heightId].includes(questionId) &&
        weight &&
        height
      ) {
        this.form[config.question.bmiId] = (weight / (height * height)).toFixed(2);
      }
    },
    inputChange(questionId) {
      // 自动计算BMI
      this.changeBmi(questionId);
    },

    autoFillSmokeFrom(questionId) {
      if (config.question.smokingHabitId === questionId) {
        // 没有吸烟习惯
        if (
          this.form[config.question.smokingHabitId] ===
          config.question.smokingHabitNeverId
        ) {
          // 后面问题自动选择
          this.form[config.question.smokingPerDayId] =
            config.question.smokingPerDayZeroId;
          this.form[config.question.smokingYearsId] =
            config.question.smokingYearsNonSmokingId;
        }
      }
    },

    pickChange(questionId) {
      this.validateField(questionId);
      this.autoFillSmokeFrom(questionId);
    },

    validateField(questionId) {
      console.log("questionId", questionId);
      this.$refs.uForm.validateField(questionId);
    },
    setForm(questionList) {
      let rules = {};
      let form = {};
      questionList.forEach((item) => {
        item.questions.forEach((question) => {
          if (question.questionType === "mulSelect") {
            form[question.questionId] = question.userAnswer
              ? question.userAnswer.split(",")
              : [];
          } else {
            form[question.questionId] = question.userAnswer;
            // 联系方式如果没有，自动从个人信息中获取
            // if (
            //   question.questionId === config.question.phoneNumberId &&
            //   !question.userAnswer
            // ) {
            //   form[question.questionId] = this.userInfo.phone;
            // } else {
            //   form[question.questionId] = question.userAnswer;
            // }
          }
          let rule = {
            required: true,
            message: "值不能为空",
            trigger: ["blur", "change"],
          };
          if (question.questionType === "mulSelect") {
            rule.type = "array";
          }
          rules[question.questionId] = question.isRequired === 1 ? [rule] : [];
        });
      });
      this.form = form;
      this.rules = rules;
      this.$refs.uForm.setRules(this.rules);
    },
    initData() {
      uni.showLoading({
        title: "加载中",
      });
      getQuestion(this.questionId).then((res) => {
        console.log("getQuestion", res);
        uni.hideLoading();
        if (res.code === 200) {
          this.questionList = res.data?.questionCategories || [];
          this.setForm(this.questionList);
        }
      });
    },
    submitQuestion(callback) {
      if (this.isSubmit) {
        return;
      }
      let params = {
        questionnaireId: this.questionId,
        answers: Object.keys(this.form).map((key) => {
          // 如果是多选框，将数组转为逗号分隔的字符串
          let answerContent = this.form[key];
          if (Array.isArray(answerContent)) {
            answerContent = answerContent.join(",");
          }
          return {
            questionId: key,
            answerContent: answerContent,
          };
        }),
      };

      this.submitLoading = true;
      commitQuestion(params).then((res) => {
        this.submitLoading = false;
        if (res.code === 200) {
          if (callback) {
            uni.$u.toast("提交成功");

            callback();
          }
        } else {
          if (callback) {
            uni.$u.toast(res.msg);
          }
        }
      });
    },
    // 处理表单提交
    handleSubmit() {
      console.log("this.form", this.form);
      this.$refs.uForm
        .validate()
        .then(async () => {
          this.submitQuestion(() => {
            this.isSubmit = true;
            uni.$u.route({
              url: "pagesA/healthManage/index",
            });
          });
        })
        .catch((errors) => {
          uni.$u.toast("请完善表单信息");
        });
    },
    // 添加获取选项文字的方法
    getOptionLabels(question) {
      const selectedValues = this.form[question.questionId] || [];
      if (!selectedValues.length) return '';

      return question.options
        .filter(opt => selectedValues.includes(opt.value))
        .map(opt => opt.label)
        .join('、');
    },
    // 过滤问题列表
    filteredQuestions(category) {
      const questions = category.questions || [];
      if (!questions.length) return [];

      if (['吸烟习惯', '饮酒习惯'].includes(category.categoryName)) {
        const firstQuestionAnswer = this.form[questions[0].questionId];
        const firstQuestion = questions[0];
        const option = firstQuestion.options.find(opt => opt.value === firstQuestionAnswer);
        const answerLabel = option ? option.label : '';

        // 如果没有选择答案或选择了"从不"，只显示第一个问题
        if (!firstQuestionAnswer || answerLabel === '从不') {
          return [questions[0]];
        }
      }
      // 处理生活习惯分类
      if (category.categoryName === '生活习惯') {
        const defecationOptions = ['腹泻', '便秘', '腹泻与便秘交替', '既不腹泻也不便秘'];

        // 找到排便相关的问题和答案
        const defecationQuestion = questions.find(q => {
          const selectedValue = this.form[q.questionId];
          const selectedOption = q.options?.find(opt => opt.value === selectedValue);
          return defecationOptions.includes(selectedOption?.label);
        });

        if (defecationQuestion) {
          const selectedValue = this.form[defecationQuestion.questionId];
          const selectedOption = defecationQuestion.options?.find(opt => opt.value === selectedValue);
          const selectedLabel = selectedOption?.label;

          return questions.map((question, index) => {
            // 序号0-3的问题默认显示
            if (index >= 0 && index <= 3) {
              return {
                ...question,
                show: true
              };
            }

            let show = false;
            switch (selectedLabel) {
              case '腹泻':
                // 只处理序号6之后的显示
                show = index >= 6;
                break;
              case '便秘':
                // 序号4-5和16之后的显示
                show = (index >= 4 && index <= 5) || index >= 16;
                break;
              case '腹泻与便秘交替':
                // 显示序号5之后的问题
                show = index >= 4;
                break;
              case '既不腹泻也不便秘':
                // 显示序号16之后的问题
                show = index >= 16;
                break;
              default:
                show = true;
            }

            return {
              ...question,
              show
            };
          });
        }

        // 如果没有选择任何选项，显示所有问题
        return questions.map(question => ({
          ...question,
          show: true
        }));
      }


      // 其他分类的问题都显示
      return questions.map(question => ({
        ...question,
        show: true
      }));
    },
  },
};
</script>

<style lang="scss">
.survey-container {
  background-color: #fff;
  min-height: 100vh;
  padding-bottom: 30rpx;
}

.banner {
  width: 100%;
  height: 640rpx;
  background: url("https://millenia.oss-cn-wuhan-lr.aliyuncs.com/2025-03-10/1741597533549_feb7e198.png") no-repeat;
  background-size: 100% 100%;
}

.content {
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  margin: -360rpx 20rpx 0 20rpx;
}

.section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 0 20rpx 20rpx 20rpx;
}

.section-title {
  font-weight: 600;
  font-size: 36rpx;
  color: #333333;
  line-height: 42rpx;
  padding: 10rpx 0;
  width: fit-content;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 12rpx;
    width: 100%;
    height: 12rpx;
    background: linear-gradient(90deg, #117ACD 0%, rgba(17, 122, 205, 0) 100%);
    border-radius: 0rpx 0rpx 0rpx 0rpx;
  }
}

.ww-survey-container {

  .step-section,
  .section {
    .u-form-item {
      background: #F1F7FA;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      margin: 20rpx 0;
    }
  }

  .u-form-item__body__left {
    padding: 0 30rpx 20rpx 30rpx;
    width: auto !important;
  }

  .u-form-item__body__right {
    padding: 0 20rpx;
  }

  .u-form-item__body__right__message {
    padding: 0 30rpx;
    line-height: 40rpx !important;
  }

  .section {
    .u-textarea {
      background-color: transparent;
      padding: 0;
    }

    .u-textarea__value {
      font-size: 28rpx;
      color: #333;
    }
  }
}
</style>
