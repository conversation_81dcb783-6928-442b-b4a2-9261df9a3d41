<template>
  <view class="survey-container">
    <page-nav
      navigatorType="switchTab"
      url="pages/user/index"
      bgColor="transparent"
      leftIconColor="#fff"
      :placeholder="false"
      :border="false"
    ></page-nav>
    <!-- 顶部banner -->
    <view class="banner"> </view>
    <view class="content">
      <u--form :model="form" :rules="rules" ref="uForm" labelWidth="200rpx">
        <view v-for="(item, index) in questionList" :key="index" class="section">
          <view class="section-title">{{ item.categoryName }}</view>
          <u-form-item
            v-for="question in item.questions"
            :label="question.questionTitle"
            :labelPosition="question.questionTitle.length > 9 ? 'top' : 'left'"
            :labelWidth="
              question.questionTitle.length > 9
                ? 'initial'
                : `${question.questionTitle.length * 40}rpx`
            "
            :prop="question.questionId"
          >
            <!-- input - 填空 radio - 单选 mulSelect- 多选 singleSelect - 下拉 disabled - 不可编辑  pickDate -日期选择 -->
            <!-- 填空 -->
            <u-input
              v-if="question.questionType === 'input'"
              v-model="form[question.questionId]"
              border="none"
              inputAlign="right"
              readonly
            />
            <!-- 单选 -->
            <block slot="right">
              <u-radio-group
                v-if="question.questionType === 'radio'"
                v-model="form[question.questionId]"
                placement="row"
              >
                <u-radio
                  :customStyle="{ marginLeft: '20rpx', color: '' }"
                  v-for="option in question.options"
                  :key="option.value"
                  activeColor="#117ACD"
                  :name="option.value"
                  :label="option.label"
                ></u-radio>
              </u-radio-group>
            </block>
            <!-- 日期选择 -->
            <pick-date
              v-if="question.questionType === 'pickDate'"
              v-model="form[question.questionId]"
              readonly
              @change="validateField(question.questionId)"
            ></pick-date>
            <!-- 时间选择 -->
            <pick-date
              v-if="question.questionType === 'pickTime'"
              mode="time"
              v-model="form[question.questionId]"
              readonly
              @change="validateField(question.questionId)"
            ></pick-date>
            <!-- 下拉 -->
            <block slot="right">
              <d-picker
                v-if="question.questionType === 'singleSelect'"
                v-model="form[question.questionId]"
                valueName="value"
                labelName="label"
                :options="question.options"
                readonly
                @change="validateField(question.questionId)"
              ></d-picker>
            </block>
          </u-form-item>
        </view>
      </u--form>
    </view>
    <!-- <view class="footer_wrap">
      <u-button
        color="#117ACD"
        :loading="submitLoading"
        :customStyle="payStyles"
        @click="handleSubmit"
      >
        提交
      </u-button>
    </view> -->
  </view>
</template>

<script>
import { getQuestion, commitQuestion } from "@/api/question";
export default {
  name: "SurveyPage",
  data() {
    return {
      questionList: [],
      form: {},
      rules: {},
    };
  },
  onLoad(options) {
    this.questionId = options.questionId;
    if (this.questionId) {
      this.initData();
    }
  },
  methods: {
    setForm(questionList) {
      let rules = {};
      let form = {};
      questionList.forEach((item) => {
        item.questions.forEach((question) => {
          form[question.questionId] = question.userAnswer;
          rules[question.questionId] =
            question.isRequired === 1
              ? [
                  {
                    required: true,
                    message: "值不能为空",
                    trigger: ["blur", "change"],
                  },
                ]
              : [];
        });
      });
      this.form = form;
      this.rules = rules;
      this.$refs.uForm.setRules(this.rules);
    },
    initData() {
      uni.showLoading({
        title: "加载中",
      });
      getQuestion(this.questionId).then((res) => {
        console.log("getQuestion", res);
        uni.hideLoading();
        if (res.code === 200) {
          this.questionList = res.data?.questionCategories || [];
          this.setForm(this.questionList);
        }
      });
    },
  
  },
};
</script>

<style lang="scss">
.survey-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 50rpx;
}

.banner {
  width: 100%;
  height: 314rpx;
  background: url("https://millenia.oss-cn-wuhan-lr.aliyuncs.com/2025-02-18/1739847577919_77e21794.png")
    no-repeat;
  background-size: 100% 100%;
}

.content {
  border-radius: 12rpx 12rpx 0 0;
  overflow: hidden;
  margin-top: -92rpx;
}

.section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 0 32rpx 20rpx 32rpx;
}
.section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  padding: 20rpx 32rpx;
  width: fit-content;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8rpx;
    height: 32rpx;
    background: linear-gradient(180deg, #0691ff 0%, #0047ff 100%);
    border-radius: 4rpx;
  }
}

// .footer_wrap {
//   box-sizing: border-box;
//   position: fixed;
//   left: 0;
//   bottom: 0;
//   width: 100%;
//   padding: 20rpx 20rpx 50rpx 20rpx;
//   background-color: #fff;
//   z-index: 10;
// }
</style>
