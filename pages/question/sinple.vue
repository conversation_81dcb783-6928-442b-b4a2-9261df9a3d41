<template>
  <view class="survey-container ww-survey-container">
    <page-nav
      navigatorType="switchTab"
      url="pages/user/index"
      bgColor="transparent"
      leftIconColor="#fff"
      :placeholder="false"
      :border="false"
    ></page-nav>
    <!-- 顶部banner -->
    <view class="banner"> </view>

    <view class="content">
      <u--form :model="form" :rules="rules" ref="uForm" labelWidth="200rpx">
        <!-- 只渲染当前步骤的分类 -->
        <view :class="['section', { 'step-section': currentStep !== 0 }]">
          <view class="progress-wrap">
            <view class="progress-title">调查进度</view>
            <progress
              border-radius="10"
              :percent="progressPercentage"
              stroke-width="10"
              activeColor="#117ACD"
              backgroundColor="#F1F7FA"
            />
            <view class="progress-text">
              <view class="progress-left">{{ currentStep + 1 }}</view>
              /
              <view class="progress-right">{{ questionList.length }}</view>
            </view>
          </view>
          <view class="section-title">{{ currentCategory.categoryName }}</view>
          <u-form-item
            v-for="(question, index) in filteredQuestions"
            :key="question.questionId"
            :required="question.isRequired === 1"
            :label="question.questionTitle"
            labelPosition="top"
            v-if="currentCategory.categoryName === '生活习惯' ? question.show : true"
            labelWidth="initial"
            :prop="question.questionId"
          >
            <!-- input - 填空 radio - 单选 mulSelect- 多选 singleSelect - 下拉 disabled - 不可编辑  pickDate -日期选择 -->
            <!-- 填空 -->
            <u-input
              v-if="question.questionType === 'input'"
              v-model="form[question.questionId]"
              border="none"
              inputAlign="right"
              placeholder="请输入"
              :readonly="question.disabled"
              @change="inputChange(question.questionId)"
            />
            <!-- 单选 -->
            <block slot="right">
              <u-radio-group
                v-if="question.questionType === 'radio'"
                v-model="form[question.questionId]"
                placement="row"
              >
                <u-radio
                  :customStyle="{ marginLeft: '20rpx', color: '' }"
                  v-for="option in question.options"
                  :key="option.value"
                  activeColor="#117ACD"
                  :name="option.value"
                  :label="option.label"
                ></u-radio>
              </u-radio-group>
            </block>
            <!-- 多选 -->
            <view
              v-if="question.questionType === 'mulSelect'"
              @click="showMulSelect(question)"
            >
              <u--textarea
                :value="getOptionLabels(question)"
                border="none"
                readonly
                placeholder="请选择"
                :autoHeight="true"
                :disabledColor="'#ffffff'"
              />
            </view>

            <!-- 日期选择 -->
            <pick-date
              v-if="question.questionType === 'pickDate'"
              v-model="form[question.questionId]"
              @change="validateField(question.questionId)"
            ></pick-date>
            <!-- 时间选择 -->
            <pick-date
              v-if="question.questionType === 'pickTime'"
              mode="time"
              v-model="form[question.questionId]"
              @change="validateField(question.questionId)"
            ></pick-date>
            <!-- 下拉 -->
            <block slot="right">
              <d-picker
                v-if="question.questionType === 'singleSelect'"
                v-model="form[question.questionId]"
                valueName="value"
                labelName="label"
                :options="question.options"
                @change="pickChange(question.questionId)"
              ></d-picker>
            </block>
          </u-form-item>
        </view>
      </u--form>
    </view>

    <!-- 底部按钮区域 -->
    <view class="footer_wrap">
      <view class="btn-group">
        <get-phone
          v-if="currentStep > 0"
          :customStyle="nextBtnStyles"
          @onSuccess="handlePrev"
        >
          <u-button
            color="#D8EBFA"
            class="step-btn"
            @click="handlePrev"
            :loading="submitLoading"
            :customStyle="nextBtnStyles"
          >
            <view style="color: #117acd">上一步</view>
          </u-button>
        </get-phone>
        <get-phone :customStyle="nextBtnStyles" @onSuccess="handleStepAction">
          <u-button
            color="#117ACD"
            class="step-btn"
            :loading="submitLoading"
            :customStyle="nextBtnStyles"
            @click="handleStepAction"
          >
            {{ isLastStep ? "提交" : "下一步" }}
          </u-button>
        </get-phone>
      </view>
    </view>

    <!-- 将弹窗移到这里 -->
    <u-popup :show="showPopup" @close="closePopup" mode="bottom">
      <view class="popup-content">
        <view class="popup-header">
          <text>请选择</text>
          <text @click="closePopup">确定</text>
        </view>
        <scroll-view scroll-y class="popup-body">
          <view class="options-list">
            <view
              v-for="option in currentOptions"
              :key="option.value"
              class="option-item"
              :class="{ 'option-item-selected': tempSelected.includes(option.value) }"
              @click="toggleSelect(option.value)"
            >
              {{ option.label }}
            </view>
          </view>
          <!-- <view class="none-option" @click="handleNoneSelect">
            以上都不包含
          </view> -->
        </scroll-view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getQuestion, commitQuestion } from "@/api/question";
import config from "@/common/config";
import { mapState } from "vuex";
import { getShareAndScanQuery } from "@/util/tools.js";

export default {
  name: "SurveyPage",
  // components: {
  //   'u-grid': () => import('uview-ui/components/u-grid/u-grid.vue'),
  //   'u-grid-item': () => import('uview-ui/components/u-grid-item/u-grid-item.vue')
  // },
  data() {
    return {
      submitLoading: false,
      questionList: [],
      form: {},
      rules: {},
      showPopup: false,
      tempSelected: [], // 临时存储选中的值
      currentOptions: [], // 当前问题的选项
      currentQuestion: null, // 当前问题对象
      currentQuestionId: "", // 添加当前问题ID
      currentStep: 0, // 当前步骤
      formCache: {}, // 缓存所有步骤的表单数据
      storageKey: "surveyFormData", // 添加storage key
    };
  },
  onLoad(options) {
    console.log("options===", uni.getLaunchOptionsSync());
    const query = getShareAndScanQuery({
      query: options,
      scene: uni.getLaunchOptionsSync().scene,
    });
    console.log("query参数", query);
    console.log("options参数", options);

    if (query.questionnaireId) {
      this.questionId = query.questionnaireId;
    } else {
      this.questionId = options.questionId;
    }
    console.log("questionId：", this.questionId);
    if (this.questionId) {
      this.initData();

      this.restoreFromStorage();
    }
  },
  onUnload() {
    console.log("onUnload");
    this.submitQuestion();
  },
  onHide() {
    console.log("onHide");
    this.submitQuestion();
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.User.userInfo,
    }),
    currentSelectedLabels() {
      if (!this.currentQuestionId) return "";
      return this.getSelectedLabels(this.currentQuestionId);
    },
    currentCategory() {
      return this.questionList[this.currentStep] || {};
    },
    // 按钮样式
    prevBtnStyles() {
      return {
        marginRight: "20rpx",
        width: "200rpx",
      };
    },
    nextBtnStyles() {
      return {
        borderRadius: "16rpx !important",
        // width: this.currentStep > 0 ? "45%" : "100%",
      };
    },
    isLastStep() {
      return this.currentStep === this.questionList.length - 1;
    },
    progressPercentage() {
      return Math.round((this.currentStep / (this.questionList.length - 1)) * 100);
    },
    // 过滤问题列表
    filteredQuestions() {
      const questions = this.currentCategory.questions || [];
      if (!questions.length) return [];
      if (["吸烟习惯", "饮酒习惯"].includes(this.currentCategory.categoryName)) {
        // 获取第一个问题的答案
        const smokingAnswer = this.form[questions[0].questionId];

        // 获取第一个问题的选项
        const firstQuestion = questions[0];
        const option = firstQuestion.options.find((opt) => opt.value === smokingAnswer);
        const answerLabel = option ? option.label : "";

        if (!smokingAnswer || answerLabel === "从不") {
          return [questions[0]];
        }

        return questions;
      }

      // 处理生活习惯分类
      if (this.currentCategory.categoryName === "生活习惯") {
        const defecationOptions = ["腹泻", "便秘", "腹泻与便秘交替", "既不腹泻也不便秘"];

        // 找到排便相关的问题和答案
        const defecationQuestion = questions.find((q) => {
          const selectedValue = this.form[q.questionId];
          const selectedOption = q.options?.find((opt) => opt.value === selectedValue);
          return defecationOptions.includes(selectedOption?.label);
        });

        if (defecationQuestion) {
          const selectedValue = this.form[defecationQuestion.questionId];
          const selectedOption = defecationQuestion.options?.find(
            (opt) => opt.value === selectedValue
          );
          const selectedLabel = selectedOption?.label;

          return questions.map((question, index) => {
            // 序号0-3的问题默认显示
            if (index >= 0 && index <= 3) {
              return {
                ...question,
                show: true,
              };
            }

            let show = false;
            switch (selectedLabel) {
              case "腹泻":
                // 只处理序号6之后的显示
                show = index >= 6;
                break;
              case "便秘":
                // 序号4-5和16之后的显示
                show = (index >= 4 && index <= 5) || index >= 16;
                break;
              case "腹泻与便秘交替":
                // 显示序号5之后的问题
                show = index >= 4;
                break;
              case "既不腹泻也不便秘":
                // 显示序号16之后的问题
                show = index >= 16;
                break;
              default:
                show = true;
            }

            return {
              ...question,
              show,
            };
          });
        }

        // 如果没有选择任何选项，显示所有问题
        return questions.map((question) => ({
          ...question,
          show: true,
        }));
      }

      // 其他分类的问题都显示
      return questions.map((question) => ({
        ...question,
        show: true,
      }));
    },
  },
  methods: {
    // 处理多选框改变事件
    checkboxChange(selectedOptions) {
      console.log("Selected options:", selectedOptions);
    },
    changeBmi(questionId) {
      const weight = this.form[config.question.weightId];
      const height = this.form[config.question.heightId];
      // 如果是身高和体重，则计算BMI
      if (
        [config.question.weightId, config.question.heightId].includes(questionId) &&
        weight &&
        height
      ) {
        this.form[config.question.bmiId] = (
          weight /
          ((height / 100) * (height / 100))
        ).toFixed(2);
      }
    },
    inputChange(questionId) {
      // 原有的 BMI 计算逻辑
      this.changeBmi(questionId);
    },

    autoFillSmokeFrom(questionId) {
      if (config.question.smokingHabitId === questionId) {
        // 没有吸烟习惯
        if (
          this.form[config.question.smokingHabitId] ===
          config.question.smokingHabitNeverId
        ) {
          // 后面问题自动选择
          this.form[config.question.smokingPerDayId] =
            config.question.smokingPerDayZeroId;
          this.form[config.question.smokingYearsId] =
            config.question.smokingYearsNonSmokingId;
        }
      }
    },

    pickChange(questionId) {
      // 获取当前问题对象
      const currentQuestion = this.currentCategory.questions.find(
        (q) => q.questionId === questionId
      );
      if (currentQuestion) {
        // 获取选中的值
        const selectedValue = this.form[questionId];
        // 找到对应的选项
        const selectedOption = currentQuestion.options.find(
          (opt) => opt.value === selectedValue
        );
        // 获取选项的文字
        const selectedLabel = selectedOption ? selectedOption.label : "";

        console.log(
          "question",
          this.currentCategory.categoryName,
          questionId,
          selectedLabel
        );

        if (this.currentCategory.categoryName === "生活习惯") {
          this.$forceUpdate();
        }
      }

      this.validateField(questionId);
      // 保存到storage
      this.saveToStorage();
    },

    validateField(questionId) {
      console.log("questionId", questionId);
      // 找到当前问题
      const currentQuestion = this.currentCategory.questions.find(
        (q) => q.questionId === questionId
      );
      if (currentQuestion?.questionTitle.includes("手机号")) {
        // 手机号字段立即触发验证
        this.$refs.uForm.validateField(questionId, true);
      } else {
        this.$refs.uForm.validateField(questionId);
      }
    },
    setForm(questionList) {
      let rules = {};
      let form = {};
      questionList.forEach((item) => {
        item.questions.forEach((question) => {
          if (question.questionType === "mulSelect") {
            form[question.questionId] = question.userAnswer
              ? question.userAnswer.split(",")
              : [];
          } else {
            // 如果是手机号输入框，从缓存获取
            if (question.questionTitle.includes("手机号")) {
              const weixinPhone = uni.getStorageSync("weixin")?.phone;
              form[question.questionId] = weixinPhone || question.userAnswer;
            } else {
              form[question.questionId] = question.userAnswer;
            }
          }

          let rule = {
            required: true,
            message: "值不能为空",
            trigger: ["blur", "change"],
          };

          if (question.questionType === "mulSelect") {
            rule.type = "array";
          }

          rules[question.questionId] = question.isRequired === 1 ? [rule] : [];
        });
      });
      this.form = form;
      this.rules = rules;
      this.$refs.uForm.setRules(this.rules);
    },
    initData() {
      uni.showLoading({
        title: "加载中",
      });
      getQuestion(this.questionId).then((res) => {
        console.log("getQuestion", res);
        uni.hideLoading();
        if (res.code === 200) {
          this.questionList = res.data?.questionCategories || [];
          this.setForm(this.questionList);
        }
      });
    },
    submitQuestion(callback) {
      if (this.isSubmit) {
        return;
      }
      let params = {
        operationType: callback ? 1 : 0,
        questionnaireId: this.questionId,
        answers: Object.keys(this.form).map((key) => {
          // 如果是多选框，将数组转为逗号分隔的字符串
          let answerContent = this.form[key];
          if (Array.isArray(answerContent)) {
            answerContent = answerContent.join(",");
          }
          return {
            questionId: key,
            answerContent: answerContent,
          };
        }),
      };

      this.submitLoading = true;
      commitQuestion(params).then((res) => {
        this.submitLoading = false;
        if (res.code === 200) {
          if (callback) {
            uni.$u.toast("提交成功");

            callback();
          }
        } else {
          if (callback) {
            uni.$u.toast(res.msg);
          }
        }
      });
    },
    // 处理步骤操作
    async handleStepAction() {
      if (this.isLastStep) {
        await this.handleSubmit();
      } else {
        // 如果是第一步，检查手机号、身份证、身高和体重
        if (this.currentStep === 0) {
          // 找到手机号字段
          // const phoneQuestion = this.currentCategory.questions.find((q) =>
          //   q.questionTitle.includes("手机号")
          // );

          // if (phoneQuestion) {
          //   const phoneValue = this.form[phoneQuestion.questionId];
          //   const weixinPhone = uni.getStorageSync("weixin")?.phone;

          //   if (weixinPhone && phoneValue !== weixinPhone) {
          //     uni.$u.toast("请输入与微信绑定的手机号");
          //     return;
          //   }
          // }

          // 检查身份证号码
          const idCardQuestion = this.currentCategory.questions.find((q) =>
            q.questionTitle.includes("身份证号码")
          );

          if (idCardQuestion) {
            const idCard = this.form[idCardQuestion.questionId];
            // 身份证号码正则表达式
            const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;

            if (idCard && !idCardReg.test(idCard)) {
              uni.$u.toast("请输入正确的身份证号码");
              return;
            }
          }

          // 检查身高
          const heightQuestion = this.currentCategory.questions.find((q) =>
            q.questionTitle.includes("身高")
          );

          if (heightQuestion) {
            const height = this.form[heightQuestion.questionId];
            // 身高正则表达式：100-250cm
            const heightReg = /^1\d{2}$|^2[0-4]\d$|^250$/;

            if (height) {
              if (!heightReg.test(height)) {
                uni.$u.toast("请输入正确的身高（100-250cm）");
                return;
              }
            }
          }

          // 检查体重
          const weightQuestion = this.currentCategory.questions.find((q) =>
            q.questionTitle.includes("体重")
          );

          if (weightQuestion) {
            const weight = this.form[weightQuestion.questionId];
            // 体重正则表达式：20-300kg，支持一位小数
            const weightReg = /^(2\d|[3-9]\d|1\d{2}|2\d{2}|300)(\.\d)?$/;

            if (weight) {
              if (!weightReg.test(weight)) {
                uni.$u.toast("请输入正确的体重（20-300kg）");
                return;
              }
            }
          }
        }

        await this.handleNext();
      }
    },
    // 获取选项的显示文字
    getOptionLabels(question) {
      const selectedValues = this.form[question.questionId] || [];
      if (!selectedValues.length) return "";

      return question.options
        .filter((opt) => selectedValues.includes(opt.value))
        .map((opt) => opt.label)
        .join("、");
    },

    showMulSelect(question) {
      this.currentQuestion = question;
      this.currentOptions = question.options;
      // 确保 form 中有这个字段的初始值
      if (!this.form[question.questionId]) {
        this.$set(this.form, question.questionId, []);
      }
      this.tempSelected = [...this.form[question.questionId]];
      this.showPopup = true;
    },

    closePopup() {
      // if (this.currentQuestion) {
      //   // 检查选中数据
      //   // if (this.tempSelected.length > 0) {
      //   //   // 查找是否包含"无"选项
      //   //   const noneOption = this.currentQuestion.options.find(opt =>
      //   //     opt.label.includes('无') && this.tempSelected.includes(opt.value)
      //   //   );

      //   //   if (noneOption) {
      //   //     uni.$u.toast('包含"无"选项时不能选择其他选项');
      //   //     return;
      //   //   }
      //   // }

      //   // // 更新表单数据
      //   this.$set(this.form, this.currentQuestion.questionId, [...this.tempSelected]);
      //   this.checkboxChange(this.form[this.currentQuestion.questionId]);
      // }

      this.showPopup = false;
      this.currentOptions = [];
      this.tempSelected = [];
      // 保存到storage
      this.saveToStorage();
    },

    handleNoneSelect() {
      this.tempSelected = [];
      this.closePopup();
    },

    // 获取已选择选项的文字描述
    getSelectedLabels(questionId) {
      const selected = this.form[questionId] || [];
      const question = this.findQuestion(questionId);
      if (!question || !question.options) return "";

      return question.options
        .filter((opt) => selected.includes(opt.value))
        .map((opt) => opt.label)
        .join("、");
    },

    // 查找问题对象
    findQuestion(questionId) {
      for (const category of this.questionList) {
        const question = category.questions.find((q) => q.questionId === questionId);
        if (question) return question;
      }
      return null;
    },

    // 处理上一步
    handlePrev() {
      this.cacheCurrentForm();
      this.currentStep--;
      this.restoreFormCache();
      // 保存到storage
      this.saveToStorage();
    },

    // 处理下一步
    async handleNext() {
      try {
        await this.$refs.uForm.validate();
        this.cacheCurrentForm();
        this.currentStep++;
        this.restoreFormCache();
        // 保存到storage
        this.saveToStorage();
      } catch (errors) {
        uni.$u.toast("请完善表单信息");
      }
    },

    // 缓存当前表单数据
    cacheCurrentForm() {
      const categoryId = this.currentCategory.categoryId;
      this.formCache[categoryId] = {};
      // 只缓存当前分类的问题答案
      this.currentCategory.questions.forEach((question) => {
        if (this.form[question.questionId] !== undefined) {
          this.formCache[categoryId][question.questionId] = this.form[
            question.questionId
          ];
        }
      });
    },

    // 恢复缓存的表单数据
    restoreFormCache() {
      const categoryId = this.currentCategory.categoryId;
      if (this.formCache[categoryId]) {
        Object.keys(this.formCache[categoryId]).forEach((questionId) => {
          this.$set(this.form, questionId, this.formCache[categoryId][questionId]);
        });
      }
    },

    // 保存数据到storage
    saveToStorage() {
      const storageData = {
        form: this.form,
        currentStep: this.currentStep,
        formCache: this.formCache,
        questionId: this.questionId,
      };
      uni.setStorageSync(this.storageKey, storageData);
    },

    // 从storage恢复数据
    restoreFromStorage() {
      const storageData = uni.getStorageSync(this.storageKey);
      if (storageData && storageData.questionId === this.questionId) {
        this.form = storageData.form || {};
        this.currentStep = storageData.currentStep || 0;
        this.formCache = storageData.formCache || {};
      } else {
        // 如果是新的问卷，清除旧的缓存
        uni.removeStorageSync(this.storageKey);
      }
    },

    // 修改 handleSubmit 方法
    async handleSubmit() {
      try {
        await this.$refs.uForm.validate();
        this.cacheCurrentForm();
        const allFormData = {};
        Object.values(this.formCache).forEach((categoryData) => {
          Object.assign(allFormData, categoryData);
        });
        this.form = allFormData;

        // 提交成功后清除缓存
        this.submitQuestion(() => {
          this.isSubmit = true;
          console.log("==========");
          uni.removeStorageSync(this.storageKey);
          uni.$u.route({
            url: "pagesA/healthManage/index",
          });
        });
      } catch (errors) {
        uni.$u.toast("请完善表单信息");
      }
    },

    // 同时修改 toggleSelect 方法，在选择时就进行校验
    toggleSelect(value) {
      const option = this.currentQuestion.options.find((opt) => opt.value === value);
      const isNoneOption = option.label.includes("无");

      if (isNoneOption) {
        // 如果选择的是"无"选项，清空其他选择
        this.tempSelected = [value];
      } else {
        // 如果选择的不是"无"选项
        const index = this.tempSelected.indexOf(value);
        if (index > -1) {
          this.tempSelected.splice(index, 1);
        } else {
          // 检查是否已选中了"无"选项
          const hasNoneOption = this.currentQuestion.options.some(
            (opt) => opt.label.includes("无") && this.tempSelected.includes(opt.value)
          );

          if (hasNoneOption) {
            // 如果已选中"无"选项，清除它
            this.tempSelected = this.tempSelected.filter((v) => {
              const opt = this.currentQuestion.options.find((o) => o.value === v);
              return !opt.label.includes("无");
            });
          }
          this.tempSelected.push(value);
        }
      }

      // 更新 form 中的值
      this.$set(this.form, this.currentQuestion.questionId, [...this.tempSelected]);
      // 保存到storage
      this.saveToStorage();
    },
  },
};
</script>

<style lang="scss">
.survey-container {
  background-color: #fff;
  min-height: 100vh;
  padding-bottom: 150rpx;
}

.progress-wrap {
  display: flex;
  align-items: center;
  padding: 20rpx 0;

  progress {
    width: 100%;
  }

  .progress-title {
    width: 200rpx;
    margin-bottom: 0;
    font-size: 28rpx;
  }

  .progress-text {
    display: flex;
    align-items: baseline;
    margin-left: 20rpx;
  }

  .progress-left {
    color: #117acd;
    font-size: 40rpx;
  }

  .progress-right {
    font-size: 30rpx;
    color: #333;
  }
}

.banner {
  width: 100%;
  height: 640rpx;
  background: url("https://millenia.oss-cn-wuhan-lr.aliyuncs.com/2025-03-10/1741597533549_feb7e198.png")
    no-repeat;
  // background: url('../../static/images/common/health.png') no-repeat;
  background-size: 100% 100%;
}

.content {
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  // margin-top: -92rpx;
  margin: -360rpx 20rpx 0 20rpx;
}

.section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 0 20rpx 20rpx 20rpx;
}

.section-title {
  font-weight: 600;
  font-size: 36rpx;
  color: #333333;
  line-height: 42rpx;
  padding: 10rpx 0;
  width: fit-content;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 12rpx;
    // top: 50%;
    // transform: translateY(-50%);
    width: 100%;
    height: 12rpx;
    background: linear-gradient(90deg, #117acd 0%, rgba(17, 122, 205, 0) 100%);
    border-radius: 0rpx 0rpx 0rpx 0rpx;
  }
}

.footer_wrap {
  box-sizing: border-box;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 20rpx 20rpx 50rpx 20rpx;
  background-color: #fff;
  z-index: 10;

  .step-btn {
    height: 98rpx !important;
    font-size: 36rpx !important;
  }
}

.btn-group {
  display: flex;
  gap: 20rpx;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.ww-survey-container {
  // .section {
  //   .u-form-item {

  //     border-bottom: 2rpx solid #F2F2F2;
  //     ;
  //   }
  // }

  .step-section,
  .section {
    .u-form-item {
      background: #f1f7fa;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      margin: 20rpx 0;
    }
  }

  .u-form-item__body__left {
    padding: 0 30rpx 20rpx 30rpx;
    width: auto !important;
  }

  .u-form-item__body__right {
    padding: 0 20rpx;
  }

  .u-form-item__body__right__message {
    padding: 0 30rpx;
    line-height: 40rpx !important;
  }

  .section {
    .u-textarea {
      background-color: transparent;
      padding: 0;
    }

    .u-textarea__value {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.popup-content {
  max-height: 80vh;
  background-color: #fff;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1px solid #eee;

    text:last-child {
      color: #117acd;
    }
  }

  .popup-body {
    // padding: 30rpx;
    max-height: calc(80vh - 100rpx);

    .options-list {
      display: flex;
      flex-wrap: wrap;
      margin: -8rpx;
      padding: 10rpx;
    }

    .option-item {
      width: calc(50% - 16rpx);
      margin: 8rpx;
      height: 88rpx;
      background: #f7f8fa;
      border-radius: 12rpx;
      font-size: 28rpx;
      color: #333;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
    }

    .option-item-selected {
      background: #e5f1ff;
      color: #117acd;
    }

    .none-option {
      margin-top: 30rpx;
      width: 100%;
      height: 88rpx;
      background: #f7f8fa;
      border-radius: 4rpx;
      color: #117acd;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
