<template>
	<view class="service-button" @click="onClick">
		<view class="wrap">
			<view>{{ firstTxt }}</view>
			<view v-if="otherTxt">{{ otherTxt }}</view>
		</view>

	</view>
</template>

<script>
	export default {
		name: '<PERSON>Button',
		props: {
			text: {
				type: String,
				required: true
			}
		},
		computed: {
			firstTxt() {
				return this.text.slice(0, 4)
			},
			otherTxt() {
				return this.text.slice(5)
			}
		},
		methods: {
			onClick() {
				this.$emit('click')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.service-button {
		background-color: transparent;
		border-radius: 50rpx;
		text-align: center;
		// margin: 10rpx;
		display: inline-block;
		border: 2rpx solid #9E8052;

		// min-width: 198rpx;
		// padding: 15rpx;
		width: 200rpx;
		height: 60rpx;

		.wrap {
			display: flex;
			flex-direction: column;
			height: 100%;
			align-items: center;
			justify-content: center;
			font-size: 22rpx;
			color: #595757;
			line-height: 24rpx;
		}

		// text {
		// 	font-size: 22rpx;
		// 	color: #595757;
		// }

		&:active {
			// background-color: rgba(227, 201, 151, 0.6);
		}
	}
</style>