<!-- 普通模式演示(vue) -->
<template>
    <view class="product-item">
		<!-- height="90px" -->
        <u--image :showLoading="true" src="https://cdn.uviewui.com/uview/album/1.jpg" width="120px" height="120px"
            radius="10" @click="click"></u--image>
        <view class="product-item-info">
            <view class="u-line-2">影响重症患者病死率的危险因素之一就是营养不良，有效、安全的肠内营养支持与患者的恢复和</view>
            <view class="product-item-price">
                <u-text :text="`¥100`" size="32rpx" color="#f56c6c" bold></u-text>
                <view class="product-item-detail">
                    <view>查看详情</view>
                    <u-icon name="arrow-right" size="24rpx" color="#007AFF"></u-icon>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {

        }
    },
    methods: {

    }
}
</script>

<style scoped lang="scss">
.product-item {
    padding: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .product-item-info {
        margin-left: 30rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    .product-item-price {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 30rpx;
    }
    .product-item-detail {
        display: flex;
        align-items: center;
        color: #007AFF;
    }
}
</style>