<template>
	<view :class="['service-card', customClass]">
		<view class="service-title">
			<text>{{ title }}</text>
		</view>
		<view class="service-content">
			<view class="service-wrap">
				<slot></slot>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'ServiceCard',
		props: {
			title: {
				type: String,
				required: true
			},
			customClass: {
				type: [String, Array, Object],
				default: ''
			}
		}
	}
</script>

<style lang="scss" scoped>
	.service-card {
		width: 100%;
		margin-bottom: 20rpx;
		border-radius: 20rpx;
		position: relative;

		&.health-odd {
			background: linear-gradient(to bottom, #EAC891, #EED3A5, #F2DFBC);
			display: flex;
			flex-direction: column;
			// justify-content: space-between;
			// align-items: baseline;
		}

		&.health-even {
			background: linear-gradient(to bottom, #EBC09B, #F3D0B0, #FCE2C7);
			display: flex;
			flex-direction: column;
		}

		&::before {
			content: "AIMEYEAR";
			position: absolute;
			font-size: 60rpx;
			color: rgba(255, 255, 255, 0.3);
			top: -20rpx;
			left: 8rpx;
			white-space: nowrap;
			letter-spacing: 6rpx;
			z-index: 0;
		}

		.service-title {
			font-weight: 500;
			font-size: 40rpx;
			color: #835D31;
			position: relative;
			padding: 30rpx 20rpx 0 20rpx;

			z-index: 1;
		}

		.service-content {
			position: relative;
			z-index: 1;
			padding: 0 20rpx;
			margin-bottom: 28rpx;
			display: flex;
			justify-content: flex-end;
			.service-wrap {
				width: 410rpx;
				display: flex;
				justify-content: flex-end;
				overflow: hidden;
				gap: 10rpx;
				flex-wrap: wrap;
			}
		}
	}
</style>