<template>
  <view class="service-button-group">
    <view class="group-title" v-if="title">{{ title }}</view>
    <view class="buttons-container">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ServiceButtonGroup',
  props: {
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.service-button-group {
  margin-bottom: 20rpx;
  
  .group-title {
    font-size: 30rpx;
    color: #333;
    margin-bottom: 10rpx;
    font-weight: 500;
  }
  
  .buttons-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
  }
}
</style> 