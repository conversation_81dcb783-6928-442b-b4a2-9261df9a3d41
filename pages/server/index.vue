<!-- 普通模式演示(vue) -->
<template>
	<view class="content">
		<view class="fixed-header">
			<view class="header">
				<view class="title">全生命周期服务</view>
			</view>
		</view>

		<scroll-view scroll-y class="service-container">
			<view class="service-wrapper">
				<FakeSearchBox placeholder="搜索服务-全生命周期服务" url="/pages/shop/index" />
				<!-- <SearchBox
					v-model="searchKeyword"
					@search="handleSearch"
				/> -->

				<!-- 健康档案 -->
				<ServiceCard title="健康档案" customClass="health-odd">

					<get-phone :customStyle="{ width: 'fit-content', color: '' }" @onSuccess="handleQuestion">
						<ServiceButton text="健康问卷" @click="handleQuestion" />
					</get-phone>
					<get-phone :customStyle="{ width: 'fit-content', color: '' }" @onSuccess="handleQuestionReport">
						<ServiceButton text="问卷报告" @click="handleQuestionReport" />
					</get-phone>
					<!-- <ServiceButton text="用药记录" @click="handleServiceClick('用药记录')" /> -->

				</ServiceCard>
				<!-- <ServiceCard title="健康档案" customClass="health-odd">
					<view class="button-row odd">
						<get-phone :customStyle="{ width: 'fit-content', color: '' }" @onSuccess="handleQuestion">
							<ServiceButton text="健康问卷" @click="handleQuestion" />
						</get-phone>
						<ServiceButton text="用药记录" @click="handleServiceClick('用药记录')" />
					</view>
				</ServiceCard> -->

				<!-- 健康体检 -->
				<!-- <ServiceCard title="健康体检" customClass="health-even">
          <view class="health-even-content">
            <view class="row-title">
              <text>体检套餐</text>
            </view>
            <view class="button-row even">
              <ServiceButton text="中青年" @click="handleServiceClick('中青年')" />
              <ServiceButton text="关爱家人" @click="handleServiceClick('关爱家人')" />
              <ServiceButton text="男士套餐" @click="handleServiceClick('男士套餐')" />
              <ServiceButton text="女士套餐" @click="handleServiceClick('女士套餐')" />
            </view>
          </view>
          <view class="health-even-content">
            <view class="row-title">
              <text>专项体检</text>
            </view>
            <view class="button-row even">
              <ServiceButton text="肠道菌群" @click="handleServiceClick('肠道菌群')" />
              <ServiceButton text="基因检测" @click="handleServiceClick('基因检测')" />
              <ServiceButton text="心理健康" @click="handleServiceClick('心理健康')" />
              <ServiceButton text="胃肠专项" @click="handleServiceClick('胃肠专项')" />
            </view>
          </view>
          <view class="health-even-content">
            <view class="row-title">
              <text>私人定制</text>
            </view>
            <view class="button-row even">
              <ServiceButton text="菌群移植" @click="handleServiceClick('菌群移植')" />
              <ServiceButton text="体检定制" @click="handleServiceClick('体检定制')" />
            </view>
          </view>
        </ServiceCard> -->

				<!-- 就医绿通 -->
				<!-- <ServiceCard title="就医绿通" customClass="health-odd">
          <view class="button-row odd">
            <ServiceButton text="专家绿通" @click="handleServiceClick('专家绿通')" />
            <ServiceButton text="就医陪诊" @click="handleServiceClick('就医陪诊')" />
          </view>
        </ServiceCard> -->

				<!-- 健康管理 -->
				<ServiceCard title="健康管理" customClass="health-even">
					<ServiceButton text="轻享检后 健康管理包" @click="handleServiceClick('轻享检后 健康管理包')" />
					<ServiceButton text="臻享检后 健康管理包" @click="handleServiceClick('臻享检后 健康管理包')" />
				</ServiceCard>
				<!-- 菌群移植 -->
				<ServiceCard title="菌群移植" customClass="health-odd">
					<ServiceButton text="经典焕活" @click="handleServiceClick('经典焕活')" />
					<ServiceButton text="标准优享" @click="handleServiceClick('标准优享')" />
					<ServiceButton text="精准智配" @click="handleServiceClick('精准智配')" />
					<ServiceButton text="超级衡益" @click="handleServiceClick('超级衡益')" />

				</ServiceCard>
				<!-- <ServiceCard title="健康管理" customClass="health-even">
          <view class="health-even-content">
            <view class="row-title" style="padding-left: 16rpx">
              <text>专项管理包</text>
            </view>
			
            <view class="button-row even">
              <ServiceButton text="代谢管理" @click="handleServiceClick('代谢管理')" />
              <ServiceButton text="血糖管理" @click="handleServiceClick('血糖管理')" />
              <ServiceButton
                text="心理健康"
                @click="handleServiceClick('心理健康管理')"
              />
              <ServiceButton text="睡眠健康" @click="handleServiceClick('睡眠健康')" />
              <ServiceButton text="尿酸管理" @click="handleServiceClick('尿酸管理')" />
              <ServiceButton text="体重管理" @click="handleServiceClick('体重管理')" />
            </view>
          </view>
        </ServiceCard> -->
				<view class="poster">
					<image class="poster-img"
						src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/fwtp.png"
						mode="widthFix"></image>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	// import ZPMixin from '@/uni_modules/z-paging/components/z-paging/js/z-paging-mixin'
	import ProductItem from "../home/<USER>/ProductCard.vue";
	import {
		getCategoryList,
		getPackageList
	} from "@/api/home";
	import SearchBox from "../home/<USER>/SearchBox.vue";
	import ServiceCard from "./components/ServiceCard.vue";
	import ServiceButton from "./components/ServiceButton.vue";
	import ServiceButtonGroup from "./components/ServiceButtonGroup.vue";
	import FakeSearchBox from "@/pages/user/components/FakeSearchBox.vue";
	import {
		question
	} from "@/common/config";
	export default {
		// mixins: [ZPMixin],
		components: {
			ProductItem,
			SearchBox,
			ServiceCard,
			ServiceButton,
			ServiceButtonGroup,
			FakeSearchBox,
		},
		data() {
			return {
				// v-model绑定的这个变量不要在分页请求结束中自己赋值！！！
				dataList: [],
				tabList: [],
				tabObj: {},
				baseUrl: process.env.VUE_APP_STATIC_FILE_URL,
				searchKeyword: "",
			};
		},
		onLoad() {
			// this.getCategory();
		},
		methods: {
			handleQuestion() {
				uni.$u.route({
					url: `pages/question/detail?questionId=${question.singleId}`,
					params: {
						sourcePage: "pages/server/index",
					},
				});
			},
			handleQuestionReport() {
				uni.$u.route({
					url: `pages/question/report`,
					params: {
						sourcePage: "pages/server/index",
					},
				});
			},
			handleSearch() {
				// 处理搜索逻辑
				uni.showToast({
					title: "搜索: " + this.searchKeyword,
					icon: "none",
				});
			},
			handleServiceClick(serviceName) {
				// 处理服务点击
				// uni.showToast({
				//   title: "选择服务: " + serviceName,
				//   icon: "none",
				// });
			},
			getCategory() {
				getCategoryList().then((res) => {
					console.log("res.rows", res.rows);
					this.tabList = [{
							id: "all",
							name: "全部",
						},
						...res.rows,
					];
					this.tabObj = this.tabList[0];
				});
			},
			tabsChange(obj) {
				console.log("ff", obj);
				this.tabObj = obj;
				// 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
				this.$refs.paging.reload();
			},
			queryList(pageNo, pageSize) {
				// 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
				// 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
				// 模拟请求服务器获取分页数据，请替换成自己的网络请求
				// if (!this.tabObj.id) {
				// 	this.$refs.paging.complete([]);
				// 	return;
				// }
				const cate =
					this.tabObj.id === "all" ?
					null : {
						categoryId: this.tabObj.id,
					};
				const params = {
					pageNum: pageNo,
					pageSize: pageSize,
					reasonable: false,
					...cate,
				};
				getPackageList(params)
					.then((res) => {
						// 将请求的结果数组传递给z-paging
						this.$refs.paging.complete(res.rows);
					})
					.catch((res) => {
						// 如果请求失败写this.$refs.paging.complete(false);
						// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
						// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
						this.$refs.paging.complete(false);
					});
			},
			itemClick(item, index) {
				uni.$u.route({
					type: "navigateTo",
					url: "/pages/prdDetail/index",
					params: {
						id: item.id,
					},
				});
			},
		},
	};
</script>

<style scoped lang="scss">
	.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background: linear-gradient(to bottom,
				#e3c997,
				#ebd7b1,
				#f7edda,
				#fbf5ea,
				#fbf6ed,
				#faf7f5);
		position: relative;

		.fixed-header {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			z-index: 100;
		}

		.header {
			padding: 30rpx;
			padding-top: 100rpx;
			padding-bottom: 20rpx;

			.title {
				font-size: 36rpx;
				font-weight: 600;
				color: #333;
				text-align: center;
			}
		}

		.service-container {
			flex: 1;
			box-sizing: border-box;
			width: 100%;
			margin-top: 180rpx;
			/* 调整此值以匹配header的高度 */
			height: calc(100vh - 180rpx);
			/* 调整此值以匹配header的高度 */
		}

		.service-wrapper {
			padding: 0 30rpx 30rpx;

			.health-even-content {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.row-title {
					padding-left: 44rpx;
					width: 195rpx;
					font-size: 28rpx;
					font-weight: 600;
					color: #835d31;
				}
			}
		}

		.button-row {
			flex: 1;
			display: flex;
			flex-wrap: wrap;

			&.even {
				justify-content: flex-end;
			}
		}

		.poster {
			width: 100%;
			height: 467rpx;
			margin-top:35rpx;
			.poster-img {
				display: block;
				width: 100%;
				height: 100%;
			}
		}
	}
</style>