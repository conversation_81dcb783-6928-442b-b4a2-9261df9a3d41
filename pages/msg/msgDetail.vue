<template>
  <view class="msg-detail-page">
    <page-nav title="消息详情" url="pages/msg/index" :border="false"></page-nav>
    <!-- 消息详情内容 -->
    <view class="msg-content">
      <view class="msg-title">{{ msgDetail.title }}</view>
      <view class="msg-time">{{ msgDetail.createTime }}</view>
      <view class="msg-body">{{ msgDetail.content }}</view>
    </view>
  </view>
</template>

<script>
import { getMessageDetail } from '@/api/user.js';

export default {
  data() {
    return {
      msgDetail: {}, // 存储消息详情
    };
  },
  onLoad(options) {
    console.log("options:", options);
    // if (options.msgData) {
    //   this.msgDetail = JSON.parse(decodeURIComponent(options.msgData)); // 解析数据
    //   console.log("解析后的消息详情:", this.msgDetail);
    // }
    if (options.id) {
      this.getMsgDetail(options.id);
    }
  },
  methods: {
    getMsgDetail(msgId) {
      getMessageDetail(msgId).then((res) => {
        this.msgDetail = res.data
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.msg-detail-page {
  background: #f2f5f9;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

// 消息内容容器
.msg-content {
  height: 100%;
  background: #fff;
  padding: 30rpx;
  // margin: 20rpx;
  border-radius: 12rpx;
  // box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.msg-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.msg-time {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.msg-body {
  font-size: 30rpx;
  color: #444;
  line-height: 1.6;
  padding-bottom: 60rpx;
}
</style>
