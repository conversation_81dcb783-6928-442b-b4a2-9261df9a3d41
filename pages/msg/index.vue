<template>
  <view class="msg-page ww-msg-page">
    <page-nav title="消息提醒" navigatorType="switchTab" url="pages/user/index" :border="false"></page-nav>
    <!-- Tabs组件 -->
    <u-tabs :list="tabs" @change="handleTabChange" :scrollable="false" lineColor="#117ACD" lineWidth="60" :activeStyle="{
      color: '#117ACD',
      fontWeight: 'bold'
    }" :inactiveStyle="{
      color: '#333333',
    }"></u-tabs>

    <!-- 消息列表，只保留一个v-for -->
    <scroll-view scroll-y :style="{ height: `${contentHeight}rpx` }" class="msg-scroll">
      <view v-for="(item, index) in list" :key="index" class="msg-item" @click="goDetail(item)">
        <view>
          <view class="msg-title">
            <view>{{ item.title }}</view>
            <view class="dot" v-if="item.readStatus == 0"></view>
          </view>
          <view class="msg-time">{{ item.createTime }}</view>
        </view>
        <view><u-icon name="arrow-right" color="#142635" size="15"></u-icon></view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getMessageList } from '@/api/user.js'
export default {
  data() {
    return {
      // 标签列表
      tabs: [
        { name: '全部' },
        { name: '已读' },
        { name: '未读' }
      ],
      list: []
    };
  },
  computed: {
    // 内容区域高度可根据实际情况计算（例如减去导航高度等）
    contentHeight() {
      // 假设去掉导航栏后剩余高度为 1000rpx，仅做示例
      return 1000;
    },
  },
  methods: {
    // 切换标签
    handleTabChange(tab) {
      console.log("tab:", tab);
      let params = {};
      if (tab.index === 1) {
        params = { readStatus: 1 }; // 已读
      } else if (tab.index === 2) {
        params = { readStatus: 0 }; // 未读
      }
      getMessageList(params).then((res) => {
        // console.log("res:", res);
        this.list = res.data
      })
    },
    // 点击消息跳转详情
    goDetail(item) {
      // 根据业务需求跳转消息详情
      // 例如:
      uni.$u.route({
        url: '/pages/msg/msgDetail',
        params: { ...item }
      });
      console.log("item", item);
    },
  },
  // 页面加载时默认请求“全部”消息
  onLoad() {
    this.handleTabChange({ index: 0 });
  },
};
</script>

<style lang="scss" scoped>
.msg-page {
  background: #F8F9F9;
  height: 100vh; // 或者 min-height: 100vh;
  display: flex;
  flex-direction: column;
}

// 消息列表滚动区
.msg-scroll {
  flex: 1;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

// 单条消息
.msg-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.msg-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;

  .dot {
    width: 16rpx;
    height: 16rpx;
    background-color: #FA4E4E;
    border-radius: 50%;
    margin-left: 12rpx;
  }
}

.msg-time {
  font-size: 24rpx;
  color: #999;
}

.u-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .u-tabs__wrapper {
    width: 100%;
  }
}

.u-tabs .u-tabs__item {
  flex: 1;
  text-align: center;
}
</style>
<style lang="scss">
.ww-msg-page {
  .u-tabs {
    background-color: #fff;

    .u-tabs__wrapper {
      width: 100%;
    }
  }
}
</style>
