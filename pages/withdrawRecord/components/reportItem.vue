<template>
	<view class="order_item">
		<view class="item_content">
			<view class="left">
				<u-icon name="rmb-circle-fill" color="orange" size="24"></u-icon>
				<view class="left_con">
					{{item.withdrawalAmount}}
				</view>
			</view>
			<view class="right">
				{{ item.requestDate }}
			</view>

		</view>
	</view>
</template>

<script>
	import {
		previewPdf
	} from '@/util/tools.js';

	export default {
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		methods: {
		
		}

	}
</script>

<style lang="scss" scoped>
	.order_item {
		background: #fff;
		margin-top: 20rpx;
		padding-left: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;



		.item_content {
			padding: 30rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			position: relative;

			.left {
				display: flex;
				align-items: center;

				.left_con {
					margin-left: 10rpx;
				}
			}

			.right {
				font-weight: 500;
				font-size: 26rpx;
				color: rgba(51, 51, 51, 0.6);
			}
		}
	}
</style>