<template>
  <view class="health-card">
    <text class="title">血压脉搏</text>
    <view class="data-container">
      <view class="data-item">
        <view class="data-item-label">收缩压</view>
        <view class="data-content">
          <view class="data-item-value">{{ dataSource.systolic }} </view>
          <view class="data-item-unit">mmHg</view>
        </view>
      </view>
      <view class="data-item">
        <view class="data-item-label">舒张压</view>
        <view class="data-content">
          <view class="data-item-value">{{ dataSource.diastolic }} </view>
          <view class="data-item-unit">mmHg</view>
        </view>
      </view>
      <view class="data-item">
        <view class="data-item-label">心率</view>
        <view class="data-content">
          <view class="data-item-value">{{ dataSource.heartrate }} </view>
          <view class="data-item-unit">次/分</view>
        </view>
      </view>
    </view>
    <comment :record="dataSource.healthRemark" />
  </view>
</template>

<script>
import Comment from "./comment.vue";
export default {
  name: "HealthCard",
  components: {
    Comment,
  },
  props: {
    dataSource: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="scss">
// 定义颜色数组
$colors: [ #b0b2f0, #a6dc95, #ff9170, #adb5f9, #89d3e1, #fcafe5 ];
.health-card {
  margin-top: 20rpx;
  padding: 20px;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  .title {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    padding: 20rpx 32rpx;
    width: fit-content;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8rpx;
      height: 32rpx;
      background: linear-gradient(180deg, #0691ff 0%, #0047ff 100%);
      border-radius: 4rpx;
    }
  }

  .data-container {
    display: grid; /* 修改为 grid 布局 */
    grid-template-columns: repeat(2, 1fr); /* 两列布局 */
    gap: 10px; /* 列间距 */
    margin-top: 30rpx;
    @for $i from 1 through length($colors) {
      .data-item:nth-child(#{$i}) {
        .data-item-label {
          background: if(
            $i > length($colors),
            nth($colors, 1),
            nth($colors, $i)
          ); // 超出范围时取第一个颜色
        }
      }
    }
    .data-item {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      height: 144rpx;
      background: #f6f7fb;
      border-radius: 22rpx;
      overflow: hidden;

      .data-item-label {
        border-radius: 22rpx 0 22rpx 0; /* 圆角 */
        padding: 4rpx 20rpx; /* 内边距 */
        font-weight: 600;
        font-size: 22rpx;
        color: #ffffff;
        display: inline-block; /* 使背景适应内容 */
        position: absolute; /* 绝对定位 */
        top: 0; /* 距离顶部的距离 */
        left: 0; /* 距离左侧的距离 */
      }
      .data-content {
        display: flex;
        align-items: baseline;
        justify-content: center;
        .data-item-value {
          font-weight: 800;
          font-size: 46rpx;
          color: #6a7184;
        }
        .data-item-unit {
          margin-left: 7rpx;
          font-weight: 800;
          font-size: 24rpx;
          color: #6a7184;
        }
      }
    }
  }
}
</style>
