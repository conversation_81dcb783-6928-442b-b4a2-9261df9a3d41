<template>
  <view class="health-card">
    <text class="title">运动打卡</text>
    <view class="data-container">
      <block v-for="(item, index) in dataSource.sportChecks" :key="index">
        <view class="data-item data-item-right">
          <view class="data-item-label">{{ item.sportType }}</view>
        </view>
        <view class="data-item data-item-left" :style="{ background: hexToRgb(bgColor) }">
          <view class="data-item-value" :style="{ color: bgColor }">{{
            item.sportTime
          }}</view>
          <view class="data-item-unit" :style="{ color: bgColor }">h</view>
        </view>
       
      </block>
    </view>

    <comment :record="dataSource.healthRemark" />
  </view>
</template>

<script>
import Comment from "./comment.vue";
export default {
  name: "HealthCard",
  props: {
    dataSource: {
      type: Object,
      default: () => {},
    },

    bgColor: {
      type: String,
      default: "#e6f7ff",
    },
  },
  components: {
    Comment,
  },
  methods: {
    hexToRgb(hex) {
      // 去掉前面的 #
      hex = hex.replace(/^#/, "");

      // 将十六进制字符串分成红、绿、蓝三部分
      let r = parseInt(hex.substring(0, 2), 16);
      let g = parseInt(hex.substring(2, 4), 16);
      let b = parseInt(hex.substring(4, 6), 16);

      return `rgba(${r}, ${g}, ${b}, 0.14)`;
    },
  },
};
</script>

<style lang="scss">
.health-card {
  margin-top: 20rpx;
  padding: 20px;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  .title {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    padding: 20rpx 32rpx;
    width: fit-content;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8rpx;
      height: 32rpx;
      background: linear-gradient(180deg, #0691ff 0%, #0047ff 100%);
      border-radius: 4rpx;
    }
  }

  .data-container {
    display: grid; /* 使用 Grid 布局 */
    grid-template-columns: repeat(2, 1fr); /* 两列布局 */
    gap: 10rpx; /* 列间距 */
    margin-top: 30rpx;
    .data-item-left {
      background: #e6f7ff; /* 浅蓝色背景 */
      border-radius: 10rpx;
      padding: 60rpx 0;
      display: flex;
      align-items: baseline;
      justify-content: center;
      .data-item-value {
        font-weight: 800;
        font-size: 46rpx;
        color: #13ac79;
      }

      .data-item-unit {
        margin-left: 6rpx;
        font-weight: 800;
        font-size: 24rpx;
        color: #13ac79;
      }
    }
    .data-item-right {
      background: #f6f7fb;
      border-radius: 10rpx;
      padding: 60rpx 0;
      text-align: center;

      .data-item-label {
        font-weight: 800;
        font-size: 38rpx;
        color: #6a7184;
      }
    }
  }
}
</style>
