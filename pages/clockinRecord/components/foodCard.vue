<template>
  <view class="health-card">
    <text class="title">餐食打卡</text>
    <view class="meal-card-container">
      <view class="meal-card">
        <image class="meal-image" :src="dataSource.breakfastImage" />
        <view class="meal-info">
          <view class="meal-title">早餐进餐时间</view>
          <view class="meal-time">{{dataSource.breakfastTime}}</view>
        </view>
      </view>
      <view class="meal-card">
        <image class="meal-image" :src="dataSource.lunchImage" />
        <view class="meal-info">
          <view class="meal-title">午餐进餐时间</view>
          <view class="meal-time">{{dataSource.lunchTime}}</view>
        </view>
      </view>
      <view class="meal-card">
        <image class="meal-image" :src="dataSource.dinnerImage" />
        <view class="meal-info">
          <view class="meal-title">晚餐进餐时间</view>
          <view class="meal-time">{{dataSource.dinnerTime}}</view>
        </view>
      </view>
    </view>
    <comment :record="dataSource.healthRemark" />
  </view>
</template>

<script>
import Comment from "./comment.vue";
export default {
  name: "HealthCard",
  components: {
    Comment,
  },
  props: {
    dataSource: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="scss">
.health-card {
  margin-top: 20rpx;
  padding: 20px;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  .title {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    padding: 20rpx 32rpx;
    width: fit-content;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8rpx;
      height: 32rpx;
      background: linear-gradient(180deg, #0691ff 0%, #0047ff 100%);
      border-radius: 4rpx;
    }
  }

  .meal-card-container {
    display: grid; /* 修改为 grid 布局 */
    grid-template-columns: repeat(2, 1fr); /* 两列布局 */
    gap: 10px; /* 列间距 */
    margin-top: 30rpx;

    .meal-card {
      background: #f6f7fb;
      overflow: hidden;
      border-radius: 22rpx;

      .meal-image {
        width: 100%;
        height: 217rpx;
      }

      .meal-info {
        padding: 30rpx 20rpx;
        .meal-title {
          font-weight: 500;
          font-size: 28rpx;
          color: rgba(45, 54, 66, 0.6);

        }

        .meal-time {
          margin-top: 20rpx;
          font-weight: 600;
          font-size: 28rpx;
          color: #2d3642;
        }
      }
    }
  }
}
</style>
