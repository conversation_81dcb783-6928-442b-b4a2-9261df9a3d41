<template>
  <view class="evaluation">
    <view class="evaluation-label">健康师点评</view>
    <view class="evaluation-content">{{ record || "暂无点评" }}</view>
  </view>
</template>

<script>
export default {
  name: "Comment",
  props: {
    record: {
      type: String,
      default: "",
    },
  },
};
</script>

<style lang="scss">
.evaluation {
  background: #f6f7fb;
  border-radius: 22rpx;
  overflow: hidden;
  margin-top: 20rpx;
  position: relative;
  padding: 64rpx 20rpx 20rpx 20rpx;

  .evaluation-label {
    border-radius: 22rpx 0 22rpx 0; /* 圆角 */
    padding: 4rpx 20rpx; /* 内边距 */
    font-weight: 600;
    font-size: 22rpx;
    background: #0691ff;
    color: #ffffff;
    display: inline-block; /* 使背景适应内容 */
    position: absolute; /* 绝对定位 */
    top: 0; /* 距离顶部的距离 */
    left: 0; /* 距离左侧的距离 */
  }
  .evaluation-content {
    font-weight: 600;
    font-size: 28rpx;
    color: #6a7184;
  }
}
</style>
