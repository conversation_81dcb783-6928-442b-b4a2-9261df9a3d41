<template>
  <view class="container">
    <page-nav title="打卡记录" url="pagesA/healthManage/index" :border="false"></page-nav>
    <view class="search-container">
      <!-- 日期选择 -->
      <pick-date
        v-model="form.date"
        :readonly="false"
        inputAlign="left"
        placeholder="请选择日期"
        @change="initData"
      >
        <template slot="suffix">
          <u-icon name="arrow-down-fill" size="30rpx" color="#2D3642"></u-icon>
        </template>
      </pick-date>
    </view>

    <view v-if="noEmpty" class="card-container">
      <!-- 血压脉搏打卡 -->
      <health-card
        v-if="healthList.bloodPressureRecord"
        :dataSource="formatData('bloodPressureRecord')"
      ></health-card>
      <!-- 餐食打卡 -->
      <food-card
        v-if="healthList.mealsRecord"
        :dataSource="formatData('mealsRecord')"
      ></food-card>
      <!-- 运动打卡 -->
      <sport-card
        v-if="healthList.sportRecord"
        bgColor="#40BDFD"
        :dataSource="formatData('sportRecord')"
      ></sport-card>
      <!-- 体重打卡 -->
      <other-card
        v-if="healthList.weightRecord"
        title="体重打卡"
        :value="formatData('weightRecord').weight"
        :content="formatData('weightRecord').bmi"
        unit="kg"
        bgColor="#42CBE5"
        :healthRemark="formatData('weightRecord').healthRemark"
      ></other-card>
      <!-- 睡眠打卡 -->
      <other-card
        v-if="healthList.sleepRecord"
        title="睡眠打卡"
        :content="formatData('sleepRecord').sleepQualit"
        :value="formatData('sleepRecord').sleepTime"
        :healthRemark="formatData('sleepRecord').healthRemark"
        unit="h"
        bgColor="#13AC79"
      ></other-card>
      <!-- 饮酒打卡 -->
      <other-card
        v-if="healthList.drinkRecord"
        title="饮酒打卡"
        :content="formatData('drinkRecord').drinkType"
        :value="formatData('drinkRecord').drinkIntake"
        unit="两"
        bgColor="#7242E0"
        :healthRemark="formatData('drinkRecord').healthRemark"
      ></other-card>
      <!-- 排便打卡 -->
      <other-card
        v-if="healthList.defecaRecord"
        title="排便打卡"
        :content="formatData('defecaRecord').defecaShape"
        :value="formatData('defecaRecord').defecaNumber"
        unit="次"
        bgColor="#42CBE5"
        :healthRemark="formatData('defecaRecord').healthRemark"
      ></other-card>
      <!-- 血糖打卡 -->
      <other-card
        v-if="healthList.bloodSugarRecord"
        title="血糖打卡"
        :value="formatData('bloodSugarRecord').fastBloodGluco"
        unit="mmol/L"
        bgColor="#FC61CD"
        :healthRemark="formatData('bloodSugarRecord').healthRemark"
      ></other-card>
	  <!-- 健康心率打卡 -->
	  <other-card
	    v-if="healthList.heartRateRecord"
	    title="健康心率打卡"
	    :value="formatData('heartRateRecord').restHeartRate"
	    unit="次/min"
	    bgColor="#aaaaff"
	    :healthRemark="formatData('heartRateRecord').healthRemark"
	  ></other-card>
	  <!-- 健康血氧打卡 -->
	  <other-card
	    v-if="healthList.oximetryRecord"
	    title="健康血氧打卡"
	    :value="formatData('oximetryRecord').oximetry"
	    unit="%"
	    bgColor="#508c85"
	    :healthRemark="formatData('oximetryRecord').healthRemark"
	  ></other-card>
    </view>
    <u-empty v-else mode="list" marginTop="100rpx"> </u-empty>
  </view>
</template>

<script>
import { getClockinList } from "@/api/healthManage";
import HealthCard from "./components/healthCard.vue";
import FoodCard from "./components/foodCard.vue";
import OtherCard from "./components/otherCard.vue";
import SportCard from "./components/sportCard.vue";
import dayjs from "dayjs";
export default {
  components: {
    HealthCard,
    FoodCard,
    OtherCard,
    SportCard,
  },
  data() {
    return {
      form: {
        // date: "2025-02-19",
        date: dayjs().format("YYYY-MM-DD"),
      },
      healthList: {},
    };
  },
  onLoad() {
    this.initData();
  },
  computed: {
    noEmpty() {
      return Object.keys(this.healthList).some((key) => {
        return this.healthList[key];
      });
    },
  },
  methods: {
    formatData(field) {
      let data = this.healthList[field];
      if (!data) return undefined;
      return data[0];
    },
    initData() {
      getClockinList(this.form).then((res) => {
        console.log("res", res);
        this.healthList = res.data || {};
      });
    },
  },
};
</script>

<style lang="scss">
.container {
  padding: 20rpx;
  background: #f6f7fb;
  min-height: 100vh;
  .search-container {
    background: #ffffff;
    border-radius: 22rpx;
    padding: 20rpx 30rpx;
  }
}
</style>
