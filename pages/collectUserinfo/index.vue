<template>
	<view class="container">
		<page-nav title="基本信息" navigatorType="switchTab" url="pages/detectActivation/index" :border="false"></page-nav>
		<view class="tip">
			请完善用户身体数据,方便我们更准确的评估
		</view>
		<view class="content">
			<u--form :model="form" :rules="rules" ref="uForm" labelWidth="200rpx">
				<view class="block_wrap">
					<u-form-item :labelWidth="0" borderBottom labelWidth="100%">
						<view class="title_wrap">
							<text>基本信息</text>
							<text class="title_desc">(必填)</text>
						</view>
					</u-form-item>
					<u-form-item label="姓名" prop="nickname">
						<u-input v-model="form.nickname" border="none" inputAlign="right" placeholder="请输入姓名" />
					</u-form-item>
					<u-form-item label="手机号" prop="phoneNumber">
						<u-input v-model="form.phoneNumber" :disabled="activationType === 'self'" border="none"
							inputAlign="right" placeholder="请输入手机号" />
					</u-form-item>
					<u-form-item label="性别" prop="gender">
						<u-radio-group slot="right" v-model="form.gender" placement="row">
							<u-radio :customStyle="{marginLeft: '20rpx', color:''}" v-for="item in sexEnums"
								:key="item.value" activeColor="#117ACD" :name="item.value"
								:label="item.label"></u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item label="出生日期" prop="birthday">
						<pick-date v-model="form.birthday" @change="$refs.uForm.validateField('birthday')"
							slot="right"></pick-date>
					</u-form-item>
				</view>
				<view class="block_wrap">
					<u-form-item :labelWidth="0" borderBottom labelWidth="100%">
						<view class="title_wrap">
							<text>其他信息</text>
							<text class="title_desc">(必填)</text>
						</view>
					</u-form-item>
					<u-form-item label="身高(cm)" prop="height">
						<u-input v-model="form.height" border="none" inputAlign="right" placeholder="请输入身高" />
					</u-form-item>
					<u-form-item label="体重(kg)" prop="weight">
						<u-input v-model="form.weight" border="none" inputAlign="right" placeholder="请输入体重" />
					</u-form-item>
				</view>
			</u--form>

			<view class="footer_wrap">
				<view class="footer_tip">
					注意事项：编码激活后无法修改，请确认后再激活
				</view>
				<u-button color="#117ACD" :loading="submitLoading" :customStyle="payStyles" @click="handleNext">
					下一步
				</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex';
	import {
		sexEnums
	} from '@/util/enums.js'
	import {
		updateBaseInfo
	} from '@/api/user.js';
	export default {
		data() {
			return {
				submitLoading: false,
				// 激活类型 self:给自己激活  other:给他人激活
				activationType: null,
				sexEnums,
				form: {
					nickname: '',
					phoneNumber: '',
					gender: '',
					birthday: '',
					height: '',
					weight: '',
				},
				rules: {
					nickname: [{

							type: 'string',
							required: true,
							message: '请填写姓名',
							trigger: ['blur', 'change']
						},
						{
							pattern: /^(?:[\u4e00-\u9fa5·]{2,16})$/,
							message: '姓名格式不正确',
							trigger: ['blur', 'change']
						},
					],
					phoneNumber: [{
							required: true,
							message: '请输入手机号',
							trigger: ['change', 'blur'],
						},
						{
							validator: (rule, value, callback) => {
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							trigger: ['change', 'blur'],
						}
					],
					gender: {
						type: 'string',
						max: 1,
						required: true,
						message: '请选择男或女',
						trigger: ['blur', 'change']
					},
					birthday: {
						type: 'string',
						required: true,
						message: '请选择出生日期',
						trigger: ['blur', 'change']
					},
					height: [{
							type: 'string',
							required: true,
							message: '请输入身高',
							trigger: ['blur', 'change']
						},
						{
							pattern: /^\d{1,3}$/,
							message: '请输入正确身高',
							trigger: ['blur', 'change']
						},
					],
					weight: [{
							type: 'string',
							required: true,
							message: '请输入体重',
							trigger: ['blur', 'change']
						},
						{
							pattern: /^\d+(\.\d{1,2})?$/,
							message: '请输入正确体重',
							trigger: ['blur', 'change']
						},
					],
				}

			}
		},
		onReady() {
			//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
			this.$refs.uForm.setRules(this.rules)
		},
		onLoad(options) {
			console.log('options', options);
			let {
				activationType
			} = options;
			this.activationType = activationType
			// 给自己激活要填充用户的基本信息
			if (activationType === 'self') {
				this.setUserInfo()
			}

		},
		computed: {
			...mapState({
				userInfo: (state) => state.User.userInfo,
			}),
			payStyles() {
				return {
					height: '98rpx',
					fontWeight: '600',
					fontSize: '36rpx',
					color: '#FFFFFF',
					borderRadius: '14rpx'
				}
			}
		},
		methods: {
			...mapActions(['getUserInfo']),
			setUserInfo() {
				let {
					userInfo
				} = this;
				this.form = {
					nickname: userInfo.name,
					phoneNumber: userInfo.phone,
					gender: userInfo.gender,
					birthday: userInfo.birthDate,
					height: userInfo.height,
					weight: userInfo.weight,
				}
			},
			updateUserInfo() {
				return new Promise((resolve, reject) => {
					let {
						userInfo,
						form
					} = this;
					let params = {
						id: userInfo.id,
						name: form.nickname,
						phone: form.phoneNumber,
						gender: form.gender,
						birthDate: form.birthday,
						height: form.height,
						weight: form.weight,
					}
					this.submitLoading = true
					updateBaseInfo(params).then(res => {
						this.submitLoading = false
						if (res.code == 200) {
							this.getUserInfo()
							resolve(res)
						} else {
							uni.$u.toast(res.msg || '保存用户信息失败')
							reject()
						}


					})
				})

			},
			handleNext() {
				this.$refs.uForm.validate().then(async res => {
					const jump = () => {
						uni.$u.route({
							url: "pages/scan/index",
							params: {
								baseInfo: encodeURIComponent(JSON.stringify(this.form))
							}
						});
					}
					if (this.activationType === 'self') {
						this.updateUserInfo().then(res => {
							jump();
						});
					} else {
						jump();
					}

				}).catch(errors => {
					uni.$u.toast('请完善表单信息')
				})


			}

		},
	}
</script>
<style>
	.u-form-item__body {
		padding: 20rpx 30rpx !important;
	}
</style>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background-color: #F8F9F9;

		.tip {
			width: 100%;
			height: 68rpx;
			line-height: 68rpx;
			background: rgba(17, 122, 205, 0.1);
			font-weight: 500;
			font-size: 26rpx;
			color: #117ACD;
			text-align: center;
		}

		.content {
			padding: 0 20rpx;

			.block_wrap {
				background-color: #fff;
				margin-top: 20rpx;
				border-radius: 12rpx;
				padding-bottom: 20rpx;

				.title_wrap {
					color: #303133;
					font-size: 30rpx;

					.title_desc {
						color: #ADADAD;
					}
				}
			}

			.footer_wrap {
				box-sizing: border-box;
				position: fixed;
				left: 0;
				bottom: 70rpx;
				width: 100%;
				padding: 0 20rpx;

				.footer_tip {
					text-align: center;
					font-weight: 500;
					font-size: 26rpx;
					color: rgba(51, 51, 51, 0.4);
					margin-bottom: 28rpx;
				}
			}
		}
	}
</style>