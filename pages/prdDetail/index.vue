<template>
    <view class="home">
        <!-- 固定部分 -->
        <view class="fixed-content">
            <!-- 轮播图 -->
            <u-icon name="arrow-left" color="#000" size="30" class="icon-left" @click="goBack"></u-icon>
            <!-- height="582rpx" -->
            <u-swiper :list="swiperList" :autoplay="true" :interval="3000" :circular="true" height="750rpx"
                @change="e => current = e.current" :indicatorStyle="{
                    right: '30rpx',
                    bottom: '30rpx',
                }">
                <view slot="indicator" class="indicator-num">
                    <text class="indicator-num__text">{{ current + 1 }}/{{ swiperList.length }}</text>
                </view>
            </u-swiper>
            <view class="logo">
                <u--image :src="`${baseUrl}/common/logo.png`" width="223rpx" height="80rpx"></u--image>
            </view>
        </view>
        <view class="content">
            <view class="prd-info-container">
                <view class="prd-info">
                    <view class="prd-info-price">
                        <view class="new-price">
                            ￥{{ prdObj.price }}
                        </view>
                        <view class="old-price" v-if="prdObj.originalPrice">
                            ￥{{ prdObj.originalPrice }}
                        </view>
                    </view>
                    <view v-if="prdObj.tagName" class="prd-info-tag">
                        {{ prdObj.tagName }}
                    </view>
                </view>
                <view class="prd-info-desc">
                    {{ prdObj.name }}
                </view>
                <u-divider></u-divider>
                <view class="prd-info-desc kc">
                    <view style="color: rgba(51,51,51,0.38); font-size: 28rpx;">库存</view>
                    <view>{{ prdObj.cardStock || 0 }}</view>
                </view>
            </view>
            <view class="prd-info-container">
                <view class="title">
                    套餐详情
                </view>
                <u-parse :content="prdObj.details"></u-parse>
            </view>
        </view>
        <view class="btn">
            <get-phone :customStyle="{ width: '45%', marginRight: '10rpx' }" @onSuccess="show = true">
                <u-button @click="clickShoppingCart" color="#117ACD"
                    customStyle="font-size: 32rpx;height:98rpx;">加入购物车</u-button>
            </get-phone>
            <get-phone :customStyle="{ width: '45%', marginLeft: '10rpx' }" @onSuccess="show = true">
                <u-button @click="clickPay" color="#117ACD" customStyle="font-size: 32rpx;height:98rpx;">立即购买</u-button>
            </get-phone>
        </view>
        <u-popup :show="show" @close="close" style="flex: 0" :round="20">
            <view class="pop-content">
                <view class="top">
                    <!-- height="150rpx" -->
                    <u--image width="200rpx" height="200rpx" :src="prdObj.image"></u--image>
                    <view class="right">
                        <view class="name">{{ prdObj.name }}</view>
                        <view class="price">￥{{ prdObj.price }}</view>
                        <view class="kc">
                            <view class="name">库存</view>
                            <view class="num">{{ prdObj.cardStock || 0 }}</view>
                        </view>
                    </view>
                </view>
                <view class="count">
                    <view>数量</view>
                    <u-number-box v-model="count" :max="prdObj.cardStock" :integer="true"
                        :disabledInput="true"></u-number-box>
                </view>

                <block v-if="state === 'pay' && prdObj.isVirtual !== '1'">
                    <u-divider></u-divider>
                    <view class="header">
                        收货地址
                    </view>
                    <view class="address-form">
                        <u--input v-model="receiverName" placeholder="收货人姓名" border="none" :customStyle="{
                            background: '#FAFAFA',
                            borderRadius: '12rpx',
                            marginBottom: '20rpx',
                            padding: '10rpx 20rpx'
                        }"></u--input>
                        <u--input v-model="receiverPhone" placeholder="收货人手机号" border="none" type="number"
                            maxlength="11" :customStyle="{
                                background: '#FAFAFA',
                                borderRadius: '12rpx',
                                marginBottom: '20rpx',
                                padding: '10rpx 20rpx'
                            }"></u--input>
                    </view>
                    <view class="address-select">
                        <u-picker ref="uPicker" :show="showAddressPicker" :columns="addressColumns"
                            @confirm="confirmAddress" @cancel="showAddressPicker = false" @change="changeAddress"
                            keyName="label" :defaultIndex="defaultIndex"></u-picker>
                        <view class="address-input" @click="showAddressPicker = true">
                            <text v-if="addressText">{{ addressText }}</text>
                            <text v-else class="placeholder">请选择省市区</text>
                            <u-icon name="arrow-right"></u-icon>
                        </view>
                    </view>
                    <u--textarea :style="{
                        background: '#FAFAFA',
                        borderRadius: '12rpx',
                        marginTop: '20rpx'
                    }" v-model="address" border="none" placeholder="请填写详细地址"></u--textarea>
                </block>
                <u-divider></u-divider>
                <view class="pay">
                    <view class="price">￥{{ totalPrice }}</view>
                    <u-button v-if="state === 'pay'" :loading="payLoading" @click="wxPay" color="#117ACD"
                        style="margin-right: 10px; border-radius: 14rpx;  width: 248rpx; height: 98rpx; font-size: 32rpx;">
                        <view>立即支付</view>
                    </u-button>
                    <u-button v-if="state === 'cart'" :loading="payLoading" @click="handleShoppingCart" color="#117ACD"
                        style="margin-right: 10px; border-radius: 14rpx;  width: 248rpx; height: 98rpx; font-size: 32rpx;">
                        <view>确认</view>
                    </u-button>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import {
    findGoogs,
    weixinPay
} from '@/common/home.js'
import {
    mul,
    getQueryByUrl
} from '@/util/tools.js'
import {
    addAddress,
    getAddressDetail
} from '@/api/order.js'
import {
    addToCart
} from '@/api/cart.js'
export default {
    data() {
        return {
            show: false,
            state: 'pay',
            payLoading: false,
            count: 1,
            address: '',
            current: 0,
            swiperList: [],
            products: [],
            prdObj: {},
            goodsId: '',
            showAddressPicker: false,
            addressColumns: [[], [], []],
            addressData: [],
            selectedAddress: [],
            addressObj: {},
            addressText: '',
            receiverName: '',
            receiverPhone: '',
            weixinInfo: {},
            defaultIndex: [],
            baseUrl: process.env.VUE_APP_STATIC_FILE_URL
        }
    },
    onLoad(options) {
        console.log('options', options);
        this.weixinInfo = uni.getStorageSync('weixin');
        if (this.weixinInfo && this.weixinInfo.phone) {
            this.receiverPhone = this.weixinInfo.phone;
        }
        let id = options.id;
        if (options.q) {
            // 获取扫码的参数
            const url = decodeURIComponent(options.q);
            const query = getQueryByUrl(url);
            id = query.prdId;
        }
        this.getProDetail(id);

        this.initAddressPicker();

    },
    computed: {
        totalPrice() {
            return mul(this.prdObj.price || 0, this.count || 0).toFixed(2)
        }
    },
    methods: {
        clickPay() {
            this.show = true;
            this.state = 'pay';
        },
        clickShoppingCart() {
            this.show = true;
            this.state = 'cart';
        },
        // 加入购物车
        async handleShoppingCart() {
            try {
                // 参数验证
                if (!this.prdObj.id) {
                    uni.showToast({
                        title: '商品信息异常',
                        icon: 'none'
                    });
                    return;
                }

                // 显示loading
                uni.showLoading({
                    title: '加入购物车中...'
                });

                // 构建请求参数
                const params = {
                    goodsId: this.prdObj.id,
                    quantity: this.count,
                    price: this.prdObj.price,
                    goodsName: this.prdObj.name,
                    goodsImage: this.prdObj.image,
                    originalPrice: this.prdObj.originalPrice || this.prdObj.price,
                };

                console.log('加入购物车参数：', params);

                // 调用接口
                const response = await addToCart(params);

                uni.hideLoading();

                if (response.code === 200) {
                    uni.showToast({
                        title: '加入购物车成功',
                        icon: 'success'
                    });

                    this.show = false;
                } else {
                    uni.showToast({
                        title: response.message || '加入购物车失败',
                        icon: 'none'
                    });
                }

            } catch (error) {
                uni.hideLoading();
                console.error('加入购物车失败：', error);

                // 处理不同类型的错误
                let errorMessage = '加入购物车失败';
                if (error.message) {
                    errorMessage = error.message;
                } else if (error.data && error.data.message) {
                    errorMessage = error.data.message;
                } else if (typeof error === 'string') {
                    errorMessage = error;
                }

                uni.showToast({
                    title: errorMessage,
                    icon: 'none'
                });
            }
        },

        processTree(nodes, level = 0) {
            nodes.forEach(node => {
                // 根据层级存储到不同的数组
                if (level === 0) {
                    this.addressColumns[0].push({
                        value: node.code,
                        label: node.name
                    });
                } else if (level === 1) {
                    this.addressColumns[1].push({
                        value: node.code,
                        label: node.name,
                        parentId: node.pid
                    });
                } else if (level === 2) {
                    this.addressColumns[2].push({
                        value: node.code,
                        label: node.name,
                        parentId: node.pid
                    });
                }

                // 如果有子节点，递归处理
                if (node.children && node.children.length > 0) {
                    this.processTree(node.children, level + 1);
                }
            });
        },
        getAddress(params) {
            getAddressDetail(params).then(res => {
                const detail = res.rows[0];
                this.receiverPhone = detail.receiverPhone;
                this.receiverName = detail.receiverName;
                this.address = detail.detailAddress;
                this.addressText = detail.province + ' ' + detail.city + ' ' + detail.district;
                this.addressObj = {
                    id: detail.id,
                    province: detail.province,
                    provinceCode: detail.provinceCode,
                    city: detail.city,
                    cityCode: detail.cityCode,
                    district: detail.district,
                    districtCode: detail.districtCode
                }

                this.addressColumns[0].forEach((e, index) => {
                    if (e.value === detail.provinceCode) {
                        this.defaultIndex[0] = index;
                    }
                })
                this.addressData[this.defaultIndex[0]].children.forEach((e, index) => {
                    if (e.code === detail.cityCode) {
                        this.defaultIndex[1] = index;
                    }
                })
                this.addressData[this.defaultIndex[0]].children[this.defaultIndex[1]].children.forEach((e, index) => {
                    if (e.code === detail.districtCode) {
                        this.defaultIndex[2] = index;
                    }
                })

                this.addressColumns = [
                    this.addressData.map(e => { return { label: e.name, value: e.code } }),
                    this.addressData[this.defaultIndex[0]].children.map(e => { return { label: e.name, value: e.code } }),
                    this.addressData[this.defaultIndex[0]].children[this.defaultIndex[1]].children.map(e => { return { label: e.name, value: e.code } })
                ];
            })
        },
        getProDetail(id) {
            this.goodsId = id;
            uni.showLoading({
                title: '加载中'
            });
            findGoogs(id).then(res => {
                this.prdObj = res.data;
                this.swiperList = [res.data.image]
                this.getAddress({
                    userId: this.weixinInfo.id
                });
                uni.hideLoading();
            })
        },
        close() {
            this.show = false
        },
        goBack() {
            let pages = getCurrentPages();
            console.log(pages[pages.length - 2]);
            if (pages[pages.length - 2]) {
                uni.navigateBack({
                    delta: 1
                });
            } else {
                uni.$u.route({
                    type: 'switchTab',
                    url: 'pages/home/<USER>',
                })
            }

        },
        async wxPay() {
            if (this.prdObj.isVirtual !== '1') {
                if (!this.receiverName) {
                    uni.$u.toast('请填写收货人姓名');
                    return;
                }
                if (!this.receiverPhone) {
                    uni.$u.toast('请填写收货人手机号');
                    return;
                }
                // 验证手机号格式
                if (!/^1[3-9]\d{9}$/.test(this.receiverPhone)) {
                    uni.$u.toast('请输入正确的手机号');
                    return;
                }
                if (!this.addressText) {
                    uni.$u.toast('请选择省市区');
                    return;
                }
                if (!this.address) {
                    uni.$u.toast('请填写详细地址');
                    return;
                }
            }
            const addressParams = {
                ...this.addressObj,
                userId: this.weixinInfo.id,
                receiverName: this.receiverName,
                receiverPhone: this.receiverPhone,
                detailAddress: this.address,

            }
            const res = await addAddress(addressParams)
            if (res.code === 200) {
                this.getAddress({
                    userId: this.weixinInfo.id
                })
            }
            const params = {
                goodsItemList: [
                    {
                        goodsId: this.prdObj.id,
                        num: this.count,
                    }
                ],
                receiverName: this.receiverName,
                receiverPhone: this.receiverPhone,
                address: `${this.addressText} ${this.address}`
            }
            this.payLoading = true;
            weixinPay(params).then(payData => {
                console.log('fffffff', payData);
                this.payLoading = false;
                if (payData.code === 500) {
                    uni.$u.toast(payData.msg);
                    return
                }
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: payData.timeStamp,
                    nonceStr: payData.nonceStr,
                    package: payData.packageValue,
                    paySign: payData.paySign,
                    signType: payData.signType,
                    success: res => {
                        console.log('ppppppppp', res);
                        if (res.errMsg === 'requestPayment:ok') {
                            uni.$u.route({
                                url: `/pages/paySuccess/paySuccess?orderSpecial=${payData.orderSpecial || ''}`,
                            });
                        }
                        // this.queryWXXCXpayResultData()
                    },
                    fail: () => {
                        uni.$u.toast('取消支付');
                        findGoogs(this.goodsId).then(res => {
                            this.prdObj = res.data;
                            this.swiperList = [res.data.image]
                        })

                    }
                })
            })
        },
        initAddressPicker() {
            const addressLists = uni.getStorageSync('addressLists');
            if (addressLists) {
                this.addressData = addressLists;
                this.processTree(addressLists);
                // this.addressColumns[0] = this.addressData.map(e => { return e.name })
                console.log('addressColumns', this.addressColumns);
            }
        },
        changeAddress(e) {
            const {
                columnIndex,
                index,
                indexs,
                picker = this.$refs.uPicker
            } = e
            if (columnIndex === 0) {
                let children1 = this.addressData[index].children.map(e => { return { label: e.name, value: e.code } })

                picker.setColumnValues(1, children1)
                let children2 = this.addressData[index].children[indexs[1]].children.map(e => { return { label: e.name, value: e.code } })
                picker.setColumnValues(2, children2)
            }
            if (columnIndex === 1) {
                let children3 = this.addressData[indexs[0]].children[indexs[1]].children.map(e => { return { label: e.name, value: e.code } })
                picker.setColumnValues(2, children3)
            }
        },
        confirmAddress(e) {
            console.log('e', e);
            const values = e.value;
            this.addressText = values.map(e => e.label).join(' ');
            this.showAddressPicker = false;
            this.addressObj = {
                ...this.addressObj,
                province: values[0].label,
                provinceCode: values[0].value,
                city: values[1].label,
                cityCode: values[1].value,
                district: values[2].label,
                districtCode: values[2].value
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.indicator-num {
    padding: 2px 0;
    background-color: rgba(0, 0, 0, 0.35);
    border-radius: 100px;
    width: 35px;
    justify-content: center;
    display: flex;

    &__text {
        color: #FFFFFF;
        font-size: 12px;
    }
}

.home {
    display: flex;
    flex-direction: column;
    height: 100vh;
    // padding: 20rpx 20rpx 0 20rpx;
    background: #fff;
    overflow-x: hidden;
}

.icon-left {
    position: absolute;
    left: 20rpx;
    top: 100rpx;
    z-index: 22;
}

.fixed-content {
    flex-shrink: 0;
    position: relative;

    .logo {
        position: absolute;
        top: 90rpx;
        left: 30rpx;
        width: 100%;
        height: 100%;
    }
}

.content {
    // padding: 30rpx;
    background: #F3F5F9;
    flex: 1;
    padding-bottom: 170rpx;

    .prd-info-container {
        background: #fff;
        padding: 30rpx;
        // border-radius: 20rpx;
        margin-bottom: 20rpx;

        .title {
            font-weight: 600;
            font-size: 32rpx;
            color: #333333;
            margin-bottom: 20rpx;
        }
    }

    .prd-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .prd-info-price {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .new-price {
                font-weight: bold;
                font-size: 52rpx;
                color: #FD3232;
                margin-right: 20rpx;
            }

            .old-price {
                font-weight: 500;
                font-size: 20rpx;
                color: #CCCBCB;
                text-decoration: line-through;
                margin-top: 20rpx;
            }
        }

        .prd-info-tag {
            color: #fff;
            background: #FFDE97;
            border-radius: 34rpx;
            padding: 10rpx 20rpx;
        }

    }

    .prd-info-desc {
        font-weight: 500;
        font-size: 28rpx;
        color: #000000;

        &.kc {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 20rpx;
        }
    }
}

.btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40rpx 0;

    .btn-text {
        font-size: 32rpx;
        background: #117ACD;
        color: #fff;
        padding: 24rpx 270rpx;
        font-weight: 600;
        font-size: 36rpx;
    }
}

.pop-content {
    padding: 20rpx;
    // background: #fff;
    margin-top: 20rpx;

    .header {
        font-weight: 600;
        font-size: 34rpx;
        color: #333333;
        line-height: 40rpx;
        padding-bottom: 30rpx;
    }

    .top {
        display: flex;
        margin-bottom: 30rpx;

        .right {
            flex: 1;
            margin-left: 30rpx;

            .name {
                font-weight: 500;
                font-size: 30rpx;
                color: #333333;
            }

            .price {
                font-weight: bold;
                font-size: 28rpx;
                color: #FD3131;
                padding: 15rpx 0;
            }
        }

    }

    .kc {
        display: flex;
        justify-content: space-between;

        .name {
            font-weight: 500;
            font-size: 30rpx;
            color: rgba(51, 51, 51, 0.6);
        }

        .num {
            font-weight: 500;
            font-size: 30rpx;
            color: #333333;
        }
    }

    .count {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .pay {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .price {
            font-weight: bold;
            font-size: 36rpx;
            color: #FD3232;
            flex: 1;
        }

        .btn {
            color: #fff;
            // background: #117ACD;
            padding: 10rpx;
        }
    }
}

.address-form {
    margin-bottom: 20rpx;
}

.address-select {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .address-input {
        background: #FAFAFA;
        padding: 10rpx 20rpx;
        border: none;
        border-radius: 12rpx;
        width: 100%;
        margin-bottom: 20rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .placeholder {
            color: #999;
        }
    }
}
</style>