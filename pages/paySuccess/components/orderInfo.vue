<template>
	<u-cell-group :border="false" :customStyle="cellGroupStyle">
		<u-cell :border="false">
			<view slot="title" class="card_title">
				订单信息
			</view>
		</u-cell>
		<u-cell :border="false">
			<view slot="title" class="cell_title">
				订单编号
			</view>
			<view slot="value" class="cell_value">
				23667890023
			</view>
		</u-cell>
		<u-cell :border="false">
			<view slot="title" class="cell_title">
				购买者
			</view>
			<view slot="value" class="cell_value">
				张三
			</view>
		</u-cell>
		<u-cell :border="false">
			<view slot="title" class="cell_title">
				购买时间
			</view>
			<view slot="value" class="cell_value">
				2024-10-18 17:28
			</view>
		</u-cell>
	</u-cell-group>
</template>

<script>
	export default {
		props: {
			orderInfo: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		computed: {
			cellGroupStyle() {
				let styles = {
					background: '#fff',
					marginTop: '20rpx',
					padding: '20rpx 0 30rpx 0',
					borderRadius: '16rpx',
					overflow: 'hidden',
					fontSize: 'inherit'
				}
				return styles
			},
			validityTime() {
				let {
					startTime,
					endTime
				} = this.orderInfo
				return `${startTime?uni.$u.timeFormat(startTime, 'yyyy-mm-dd'):'---'}至${endTime?uni.$u.timeFormat(endTime, 'yyyy-mm-dd'):  '---'}`
			}
		},
		methods: {
			handleServiceDetail() {
				uni.$u.route('/pages/serviceDetail/serviceDetail');
			}
		},
	}
</script>

<style lang="scss" scoped>
	// ::v-deep .u-cell__body {
	// 	padding: 30rpx 0rpx;
	// }
	.card_title {
		font-weight: 600;
		font-size: 40rpx;
		color: #333333;
	}

	.cell_title {
		font-weight: 400;
		font-size: 26rpx;
		color: #999999;
	}

	.cell_value {
		font-weight: 400;
		font-size: 26rpx;
		color: #333333;
	}

	.validity_con {
		display: flex;
		align-items: center;
		font-size: 26rpx;

		.validity_desc {
			color: #A1A1A1;
			margin-right: 10rpx;
		}

		.validity_btn {
			color: #F96F1F;
		}
	}
</style>