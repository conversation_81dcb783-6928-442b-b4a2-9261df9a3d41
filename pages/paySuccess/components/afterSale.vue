<template>
	<u-cell-group :border="false" :customStyle="cellGroupStyle">
		<u-cell :border="false">
			<view slot="title" class="card_title">
				售后信息
			</view>
		</u-cell>
		<u-cell :border="false">
			<view slot="title" class="cell_title">
				售后状态
			</view>
			<view slot="value" class="status">
				退款驳回
			</view>
		</u-cell>
		<u-cell :border="false">
			<view slot="title" class="cell_title">
				退款驳回原因
			</view>
			<view slot="value" class="cell_value">
				信息不对
			</view>
		</u-cell>
		<view class="action_wrap">
			<u-button color="#117ACD" :customStyle="payStyles" @click="handleGoPay">
				申请退款
			</u-button>
		</view>
	
	</u-cell-group>
</template>

<script>
	export default {
		props: {
			orderInfo: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		computed: {
			payStyles() {
				let styles = {
					width: '100%',
					height: '98rpx',
					borderRadius: '14rpx',
					fontWeight: '500',
					fontSize: '30rpx',
					color: '#FFFFFF'
				}
				return styles
			},
			cellGroupStyle() {
				let styles = {
					background: '#fff',
					marginTop: '20rpx',
					padding: '20rpx 0 30rpx 0',
					borderRadius:'16rpx',
					overflow:'hidden',
					fontSize: 'inherit'
				}
				return styles
			},
			validityTime() {
				let {
					startTime,
					endTime
				} = this.orderInfo
				return `${startTime?uni.$u.timeFormat(startTime, 'yyyy-mm-dd'):'---'}至${endTime?uni.$u.timeFormat(endTime, 'yyyy-mm-dd'):  '---'}`
			}
		},
		methods: {
			handleServiceDetail() {
				uni.$u.route('/pages/serviceDetail/serviceDetail');
			}
		},
	}
</script>

<style lang="scss" scoped>
	.action_wrap{
		padding:30rpx 30rpx 0 30rpx;
	}
	.status{
		background: rgba(253,49,49,0.1);
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		font-weight: 400;
		font-size: 30rpx;
		color: #FD3131;
		padding:8rpx 15rpx;
	}
	.card_title {
		font-weight: 600;
		font-size: 40rpx;
		color: #333333;
	}

	.cell_title {
		font-weight: 400;
		font-size: 26rpx;
		color: #999999;
	}

	.cell_value {
		font-weight: 400;
		font-size: 26rpx;
		color: #333333;
	}

	.validity_con {
		display: flex;
		align-items: center;
		font-size: 26rpx;

		.validity_desc {
			color: #A1A1A1;
			margin-right: 10rpx;
		}

		.validity_btn {
			color: #F96F1F;
		}
	}
</style>