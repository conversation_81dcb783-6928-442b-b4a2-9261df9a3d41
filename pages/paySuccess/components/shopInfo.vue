<template>
	<view class="shop_info">
		<view class="item_content" @click="handleDetail">
			<u--image class="icon_img" radius="16rpx" width="108rpx" height="108rpx" :showLoading="true"
				:src="require('@/static/images/common/productImg.jpg')"></u--image>
			<view class="left_wrap">
				<view class="title">改善护理行动，体验畅通无阻</view>
				<view class="price_con">
					<view class="price">
						<view class="price_icon">￥</view>
						<view class="amount">589.90</view>
					</view>
					<view class="count">
						x1
					</view>
				</view>
			</view>
		</view>
		<u-line></u-line>
		<view class="footer_wrap">
			<view class="footer_left">
				实付款
			</view>
			<view class="footer_right">
				<view class="unit">￥</view>
				<view class="money">1000</view>
			</view>

		</view>
	</view>
</template>

<script>
	import {
		groupTypeEnums,
		cycleUnitEnums
	} from '@/util/enums.js';
	export default {
		props: {
			orderInfo: {
				type: Object,
				default () {
					return {

					}
				}
			}
		},
		computed: {
			cellGroupStyle() {
				let styles = {
					background: '#fff',
					padding: '40rpx 30rpx',
					marginTop: '20rpx'
				}
				return styles
			},
			title() {
				let {
					goodGroupName,
					orderSource
				} = this.orderInfo;
				return `${goodGroupName||''}${orderSource === '2'?'(服务续订)':''}`
			},
			label() {
				let {
					groupType,
					cycleType,
					cycleNum
				} = this.orderInfo;
				let groupTypeRow = groupTypeEnums.find(item => item.value === groupType) || groupTypeEnums[0];
				let cycleTypeRow = cycleUnitEnums.find(item => item.value === cycleType) || cycleUnitEnums[0];
				return `${groupTypeRow?.label||''}·${cycleNum||0}${cycleTypeRow?.label||''}`
			}
		},
		methods: {},
	}
</script>

<style lang="scss" scoped>
	.shop_info {
		margin-top:-118rpx;
		// transform: translateY(-50%);
		background: #fff;
		padding: 0 30rpx;
		border-radius: 16rpx;
		overflow: hidden;

		.item_content {
			padding: 30rpx 0;
			display: flex;

			.left_wrap {
				padding-left: 20rpx;
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-around;

				.title {
					font-weight: 500;
					font-size: 26rpx;
					color: #333333;
				}

				.time {
					font-weight: 500;
					font-size: 26rpx;
					color: rgba(51, 51, 51, 0.6);
				}

				.price_con {
					display: flex;

					align-items: center;
					justify-content: space-between;

					.price {
						display: flex;
						align-items: baseline;

						.price_icon {
							font-weight: bold;
							font-size: 20rpx;
							color: #FD3232;
						}

						.amount {
							font-weight: bold;
							font-size: 36rpx;
							color: #FD3232;
						}
					}

					.count {
						font-weight: bold;
						font-size: 36rpx;
						color: #333333;
					}
				}
			}


		}

		.footer_wrap {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx 0;

			.footer_left {
				font-weight: 600;
				font-size: 28rpx;
				color: #FD3232;
			}

			.footer_right {
				font-weight: bold;
				font-size: 20rpx;
				color: #FD3232;
				display: flex;
				align-items: baseline;

				.unit {
					font-size: 20rpx;
				}

				.money {
					font-size: 36rpx;
				}
			}

		}


	}
</style>