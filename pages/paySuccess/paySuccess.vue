<template>
	<view class="page_wrap">
		<view class="header_wrap">
			<view class="header_con">
				<u--image class="icon_img" width="165rpx" height="129rpx"
					:src="`${baseUrl}/common/paySuccess.png`"></u--image>
				<view class="desc">
					支付成功！
				</view>
			</view>
		</view>
		<view class="content" v-if="showQrCode">
			<view class="tip">
				扫码添加官方客服
				<br>
				开启健康管理之旅
			</view>
			<view class="code_wrap">
				<u--image width="618rpx" height="618rpx" :src="`${baseUrl}/common/qcode.png`"></u--image>
			</view>

		</view>
		<u-button color="#117ACD" :customStyle="payStyles" @click="handleGoHome">
			返回首页
		</u-button>
	</view>
</template>

<script>
	import {
		getOrderDetail
	} from '@/common/api.js';
	export default {

		data() {
			return {
				orderId: '',
				orderDetail: {},
				showQrCode: false,
				baseUrl: process.env.VUE_APP_STATIC_FILE_URL
			}
		},
		onLoad(options) {
			console.log('options', options);
			this.orderId = options.orderId;
			if (this.orderId) {
				this.getOrderDetail();
			}
			this.showQrCode = options.orderSpecial === "1";

		},
		computed: {
			payStyles() {
				let styles = {
					width: '650rpx',
					height: '98rpx',
					borderRadius: '14rpx',
					fontWeight: '500',
					fontSize: '36rpx',
					color: '#FFFFFF',
					marginTop:'64rpx'
				}
				return styles;
			},

		},
		methods: {
			getOrderDetail() {
				uni.showLoading({
					title: '加载中'
				});
				getOrderDetail(this.orderId).then(res => {
					uni.hideLoading();
					this.orderDetail = res.code === 200 ? res.data : {};
				}).catch(() => {
					uni.hideLoading();
				})
			},
			handleGoHome() {
				uni.$u.route({
					type: 'switchTab',
					url: '/pages/home/<USER>'
				});
			},

		},
	}
</script>

<style lang="scss" scoped>
	.page_wrap {
		background-color: #F8F9F9;
		min-height: 100vh;

		.header_wrap {
			width: 100%;
			height: 457rpx;
			background: #117ACD;
			padding-top: 220rpx;
			box-sizing: border-box;

			.header_con {
				display: flex;
				align-items: center;
				justify-content: center;

				.icon_img {}

				.desc {
					font-weight: 600;
					font-size: 48rpx;
					color: #FFFFFF;
				}
			}

		}

		.content {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-top: -50rpx;

			.code_wrap {
				box-sizing: border-box;
				width: 678rpx;
				height: 690rpx;
				padding: 46rpx;
				background: linear-gradient(180deg, #AFDCFF 0%, #EDF7FF 5%, #FFFFFF 8%, #FFFFFF 100%);
				border-radius: 4rpx 4rpx 4rpx 4rpx;
				display: flex;
				align-items: center;
				justify-content: center;

			}
			.tip{
				margin-top:60rpx;
				margin-bottom: 20rpx;
				font-weight: 500;
				font-size: 35rpx;
				color: #333333;
			}
		}


	}
</style>