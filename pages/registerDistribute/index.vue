<template>
	<view class="container">
		<page-nav title="注册分销商" navigatorType="switchTab" url="pages/user/index" :border="false"></page-nav>
		<u-alert v-if="distributeInfo.auditStatus === '3'" :title="`驳回原因：${distributeInfo.rejectCause}`"
			type="warning"></u-alert>
		<view class="content">
			<u--form :model="form" :rules="rules" ref="uForm" labelWidth="200rpx">
				<view class="block_wrap">
					<u-form-item label="真实姓名" prop="realName">
						<u-input v-model="form.realName" border="none" inputAlign="right" placeholder="请输入真实姓名" />
					</u-form-item>
					<u-form-item label="员工编号" prop="employeeNumber">
						<u-input v-model="form.employeeNumber" border="none" inputAlign="right" placeholder="请输入员工编号" />
					</u-form-item>
					<u-form-item label="公司" prop="companyId">
						<d-picker v-model="form.companyId" url="/app/distributionCompany/list" labelName="companyName"
							@change="companyChange" slot="right"></d-picker>
					</u-form-item>
					<u-form-item label="地区">
						<u-input v-model="form.districtName" border="none" readonly inputAlign="right"
							placeholder="选择公司后会带出地区" />
					</u-form-item>
					<u-form-item label="手机号">
						<u-input v-model="userInfo.phone" readonly border="none" inputAlign="right"
							placeholder="请输入手机号" />
					</u-form-item>
					<u-form-item label="验证码" prop="code">
						<verify-code v-model="form.code" :phone="userInfo.phone"></verify-code>
					</u-form-item>
				</view>
			</u--form>

			<view class="footer_wrap">
				<u-button color="#117ACD" :loading="submitLoading" :customStyle="payStyles" @click="handleNext">
					提交
				</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		registerDistribute,
		getDistributeDetail
	} from '@/api/user.js'
	import {
		mapState,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				submitLoading: false,
				distributeInfo: {},
				form: {
					companyId: '',
					districtName: '',
					code: '',
					employeeNumber: '',
					realName: '',
				},
				rules: {
					companyId: [{

						type: 'string',
						required: true,
						message: '请填写选择公司',
						trigger: ['blur', 'change']
					}],
					code: [{
						required: true,
						message: '请输入验证码',
						trigger: ['change', 'blur'],
					}],
					realName: [{
						required: true,
						message: '请输入真实姓名',
						trigger: ['change', 'blur'],
					}],
					employeeNumber: [{
						required: true,
						message: '请输入员工编号',
						trigger: ['change', 'blur'],
					}],

				}

			}
		},
		onReady() {
			//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
			this.$refs.uForm.setRules(this.rules)
		},
		onShow() {
			this.initData();

		},
		computed: {
			...mapState({
				userInfo: (state) => state.User.userInfo,
			}),
			payStyles() {
				return {
					height: '98rpx',
					fontWeight: '600',
					fontSize: '36rpx',
					color: '#FFFFFF',
					borderRadius: '14rpx'
				}
			}
		},
		methods: {
			...mapActions(['getUserInfo']),
			initData() {
				let {
					distributor,
					id
				} = this.userInfo;
				if (distributor && distributor.id) {
					uni.showLoading({
						title: '加载中'
					});
					getDistributeDetail(id).then(res => {
						uni.hideLoading();
						if (res.code === 200) {
							this.distributeInfo = res.data.distributor || {};
							this.form = {
								...this.distributeInfo
							}
						}
					}).catch(() => {
						uni.hideLoading();
					})
				}
			},
			companyChange(row) {
				this.$refs.uForm.validateField('companyId')
				this.form.districtName = row.address;
			},
			handleNext() {
				this.$refs.uForm.validate().then(async res => {
					let params = {
						...this.form,
						phoneNumber: this.userInfo.phone
					}
					this.submitLoading = true;
					registerDistribute(params).then(res => {
						this.submitLoading = false;
						if (res.code === 200) {
							uni.$u.toast('操作成功');
							this.getUserInfo();
							setTimeout(() => {
								uni.$u.route({
									type: 'switchTab',
									url: "pages/user/index"
								});
							}, 1000)

						} else {
							uni.$u.toast(res.msg || '操作失败')
						}
					})
				}).catch(errors => {
					uni.$u.toast('请完善表单信息')
				})
			}

		},
	}
</script>
<style>
	.u-form-item__body {
		padding: 20rpx 30rpx !important;
	}
</style>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background-color: #F8F9F9;

		.tip {
			width: 100%;
			height: 68rpx;
			line-height: 68rpx;
			background: rgba(17, 122, 205, 0.1);
			font-weight: 500;
			font-size: 26rpx;
			color: #117ACD;
			// text-align: center;
		}

		.content {
			padding: 0 20rpx;

			.block_wrap {
				background-color: #fff;
				margin-top: 20rpx;
				border-radius: 12rpx;
				padding-bottom: 20rpx;

				.title_wrap {
					color: #303133;
					font-size: 30rpx;

					.title_desc {
						color: #ADADAD;
					}
				}
			}

			.footer_wrap {
				box-sizing: border-box;
				position: fixed;
				left: 0;
				bottom: 70rpx;
				width: 100%;
				padding: 0 20rpx;

				.footer_tip {
					text-align: center;
					font-weight: 500;
					font-size: 26rpx;
					color: rgba(51, 51, 51, 0.4);
					margin-bottom: 28rpx;
				}
			}
		}
	}
</style>