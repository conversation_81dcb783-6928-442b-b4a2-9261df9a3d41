<template>
	<view class="item">
		<view class="left">
			<view class="icon_wrap">
				<u--image height="118rpx" width="118rpx" mode="aspectFit" :showLoading="true"
					:src="item.icon"></u--image>
			</view>
			<view class="left_wrap">
				<view class="title">
					{{item.title}}
				</view>
				<view class="desc">
					{{item.desc}}
				</view>
			</view>
		</view>
		<view class="right">
			<u-icon color="#C5C6C6" name="arrow-right" size="19"></u-icon>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.item {
		margin-bottom:62rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 28rpx 56rpx 28rpx 20rpx;
		background: rgba(83, 93, 121, 0.04);
		border-radius: 24rpx;

		.left {
			flex: 1;
			display: flex;
			align-items: center;

			.icon_wrap {
				width: 118rpx;
				height: 118rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.left_wrap {
				flex: 1;
				padding-left: 22rpx;
				color: #333333;

				.title {
					font-weight: 600;
					font-size: 40rpx;
				}

				.desc {
					font-weight: 400;
					font-size: 24rpx;
				}
			}
		}
	}
</style>