<template>
	<view class="container">
		<page-nav title="检测激活" :isLeft="false" bgColor="inherit" :border="false"></page-nav>
		<view class="content">
			<view class=".main_title">
				选择您要激活的方式
			</view>
			<view class="main_desc">
				根据选择为您提供精准服务
			</view>
			<view class="list">
				<block v-for="item in list" :key="item.type">
					<get-phone v-if="item.type === 'self'" @onSuccess="handleType(item)">
						<list-item :item="item" @click.native="handleType(item)"></list-item>
					</get-phone>
					<list-item v-else :item="item" @click.native="handleType(item)"></list-item>
				</block>

			</view>
		</view>
	</view>
</template>

<script>
	import listItem from './components/list-item.vue'
	export default {
		components: {
			listItem
		},
		data() {
			return {
				list: [{
						type: 'self',
						title: '为自己激活',
						desc: '生成适用于自己的编码',
						icon: `${process.env.VUE_APP_STATIC_FILE_URL}/detectActivation/self.png`
					},
					{
						type: 'other',
						title: '替别人激活',
						desc: '生成适用于别人的编码',
						icon: `${process.env.VUE_APP_STATIC_FILE_URL}/detectActivation/other.png`
					},
				],
				baseUrl: process.env.VUE_APP_STATIC_FILE_URL
			}
		},
		methods: {
			handleType(item) {
				uni.$u.route({
					url: "pages/collectUserinfo/index",
					params: {
						activationType: item.type
					}
				});
			}
		},
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #fff;

		.content {
			padding-top: 82rpx;

			.main_title {
				font-weight: 600;
				font-size: 48rpx;
				color: #333333;
				text-align: center;
			}

			.main_desc {
				font-weight: 500;
				font-size: 28rpx;
				color: #333333;
				margin-top: 10rpx;
				text-align: center;
			}

			.list {
				margin-top: 72rpx;
				padding: 0 58rpx;
			}
		}
	}
</style>