<template>
  <view class="user-card">
    <view class="avatar-container">
      <image class="avatar" :src="avatar || defaultAvatar" mode="aspectFill"></image>
    </view>
    <view class="user-info">
      <text class="username">{{ username || "用户_" + userId }}</text>
      <get-phone :customStyle="{ width: 'fit-content' }" @onSuccess="onSettingsClick">
        <view class="settings-icon" @click="onSettingsClick">
          <image
            src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/sz.png"
            mode="aspectFit"
          ></image>
        </view>
      </get-phone>
    </view>
  </view>
</template>

<script>
export default {
  name: "UserCard",
  props: {
    avatar: {
      type: String,
      default: "",
    },
    username: {
      type: String,
      default: "",
    },
    userId: {
      type: String,
      default: "000000",
    },
  },
  data() {
    return {
      defaultAvatar: "",
    };
  },
  methods: {
    onSettingsClick() {
      this.$emit("settings-click");
    },
  },
};
</script>

<style lang="scss" scoped>
.user-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  .avatar-container {
    width: 160rpx;
    height: 160rpx;
    border-radius: 80rpx;
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 20rpx;

    .avatar {
      width: 100%;
      height: 100%;
    }
  }

  .user-info {
    display: flex;
    align-items: center;

    .username {
      font-size: 39rpx;
      color: #000;
      font-weight: 800;
    }

    .settings-icon {
      margin-left: 20rpx;
      width: 40rpx;
      height: 40rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
