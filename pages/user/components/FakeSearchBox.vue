<template>
  <view class="fake-search-box" @click="handleClick">
    <view class="fake-search">
      <u-icon :name="icon" :size="iconSize" :color="iconColor"></u-icon>
      <text class="search-placeholder">{{ placeholder }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FakeSearchBox',
  props: {
    placeholder: {
      type: String,
      default: '搜索商品-好货等你'
    },
    icon: {
      type: String,
      default: 'search'
    },
    iconSize: {
      type: [Number, String],
      default: 28
    },
    iconColor: {
      type: String,
      default: '#999'
    },
    url: {
      type: String,
      default: '/pages/home/<USER>'
    },
    switchTab: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleClick() {
      if (this.switchTab) {
        uni.switchTab({
          url: this.url
        });
      } else {
        uni.navigateTo({
          url: this.url
        });
      }
      this.$emit('click');
    }
  }
}
</script>

<style lang="scss" scoped>
.fake-search-box {
  margin-bottom: 40rpx;
  .fake-search {
    background-color: #fff;
    height: 70rpx;
    border-radius: 35rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    
    .search-placeholder {
      margin-left: 10rpx;
      color: #999;
      font-size: 24rpx;
    }
  }
}
</style> 