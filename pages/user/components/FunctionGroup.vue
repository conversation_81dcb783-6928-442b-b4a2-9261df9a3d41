<template>
  <view class="function-group">
    <view class="group-header">
      <text class="title">{{ title }}</text>
      <view v-if="showMore" class="more">
        <get-phone @onSuccess="onMoreClick">
          <view class="more-content" @click="onMoreClick">
            <text>{{ moreText }}</text>
            <u-icon name="arrow-right" color="#929292" size="14"></u-icon>
          </view>
        </get-phone>
      </view>
    </view>
    <view class="group-content">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: "FunctionGroup",
  props: {
    title: {
      type: String,
      required: true,
    },
    showMore: {
      type: Boolean,
      default: false,
    },
    moreText: {
      type: String,
      default: "全部",
    },
  },
  methods: {
    onMoreClick() {
      this.$emit("more-click");
    },
  },
};
</script>

<style lang="scss" scoped>
.function-group {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx 0;
  overflow: hidden;

  .group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    border-bottom: 1px solid #f5f5f5;

    .title {
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
    }

    .more {
      font-size: 24rpx;
      color: #999;
      display: flex;
      align-items: center;
      .more-content {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .group-content {
    // display: flex;
    // justify-content: space-around;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    padding: 10rpx;
  }
}
</style>
