<template>
    <view :class="['service-card', customClass]" @click="handleClick">
      <view class="service-title">
        <text>{{ title }}</text>
      </view>
      <view class="service-content">
        <image class="button-icon" :src="icon" mode="aspectFit"></image>
      </view>
    </view>
  </template>
  
  <script>
  export default {
    name: 'ServiceCard',
    props: {
      title: {
        type: String,
        required: true
      },
      customClass: {
        type: [String, Array, Object],
        default: ''
      },
      icon: {
        type: String,
        required: true
      }
    },
    methods: {
      handleClick() {
        this.$emit('click')
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .service-card {
    width: 100%;
    height: 195rpx;
    margin-bottom: 20rpx;
    border-radius: 20rpx;
    position: relative;
    &.health-odd {
      background: linear-gradient(to bottom, #EAC891, #EED3A5,#F2DFBC);
      display: flex;
      justify-content: space-between;
      align-items: baseline;
    }
    &.health-even {
      background: linear-gradient(to bottom, #EBC09B, #F3D0B0, #FCE2C7);
      display: flex;
    }
    
    &::before {
      content: "AIMEYEAR";
      position: absolute;
      font-size: 56rpx;
      color: rgba(255, 255, 255, 0.3);
      top: -16rpx;
      left: 8rpx;
      white-space: nowrap;
      letter-spacing: 6rpx;
      z-index: 0;
    }
    
    .service-title {
      font-weight: 500;
      font-size: 35rpx;
      color: #835D31;
      position: relative;
      padding: 30rpx 20rpx 0 20rpx;
  
      z-index: 1;
    }
    
    .service-content {
      z-index: 1;
      padding: 0 20rpx;
      margin-bottom: 28rpx;
      .button-icon {
        position: absolute;
        right: 0;
        bottom: 0;
        // transform: translateY(-50%);
        width: 115rpx;
        height: 115rpx;
      }
    }
  }
  </style> 