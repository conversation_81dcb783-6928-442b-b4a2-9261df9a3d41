<template>
  <view class="function-item" @click="onClick">
    <view class="icon-container">
      <image :src="icon" mode="aspectFit" class="icon"></image>
    </view>
    <text class="text">{{ text }}</text>
  </view>
</template>

<script>
export default {
  name: 'FunctionItem',
  props: {
    icon: {
      type: String,
      required: true
    },
    text: {
      type: String,
      required: true
    }
  },
  methods: {
    onClick() {
      this.$emit('click')
    }
  }
}
</script>

<style lang="scss" scoped>
.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  
  .icon-container {
    width: 54rpx;
    height: 50rpx;
    margin-bottom: 10rpx;
    
    .icon {
      width: 100%;
      height: 100%;
    }
  }
  
  .text {
    margin-top: 10rpx;
    font-size: 22rpx;
    color: #222;
  }
}
</style> 