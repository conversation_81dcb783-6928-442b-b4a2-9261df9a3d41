<template>
    <view class="user_page">
      <view class="custom_navbar">
        <u-navbar placeholder fixed leftIcon=" " title="个人中心" bgColor="transparent" :titleStyle="titleStyle"></u-navbar>
        <view class="content_wrap">
          <view class="user_info">
            <u-avatar class="avatar_wrap" :src="userInfo.avater" :default-url="defaultUrl" :size="52"></u-avatar>
            <view class="user_info_content">
              <view class="user_name">{{ userInfo.nickName }}</view>
              <view class="user_card">{{ userInfo.card }}</view>
            </view>
            <get-phone :customStyle="{ width: 'fit-content', color: '' }" @onSuccess="handleHealthManage">
              <view class="user_info_right" @click="handleUserSetting">
                <u--image :showLoading="true" :src="`${baseUrl}/user/setting.png`" width="44rpx"
                  height="44rpx"></u--image>
                <!-- <u-icon name="arrow-right" color="#33333330" size="18"></u-icon> -->
              </view>
            </get-phone>
          </view>
          <view class="list_wrap">
            <get-phone :customStyle="{ width: '50%', color: '' }" @onSuccess="handleHealthManage">
              <view :class="['list_item']" :style="{
                background: `url(${baseUrl}/user/item2.png) no-repeat center/cover`,
              }" @click="handleHealthManage">
                <view class="item_title" :style="{ color: '#5d6d86ff' }">我的健康管理</view>
                <view class="item_sub_title" :style="{ color: '#5d6d8691' }">个人健康管理中心</view>
              </view>
            </get-phone>
  
            <!-- 添加我的消息功能项 -->
            <get-phone :customStyle="{ width: '50%', color: '' }" @onSuccess="handleMsg">
              <view class="list_item_wrapper" @click="handleMsg">
                <view :class="['list_item']" :style="{
                  background: `url(${baseUrl}/user/item1.png) no-repeat center/cover`,
                }" @click="handleMsg">
                  <view class="item_title" :style="{ color: '#5d6d86ff' }">我的消息</view>
                  <view class="item_sub_title" :style="{ color: '#5d6d8691' }">查看我的消息</view>
                </view>
                <!-- 未读消息红点，当数量大于 0 时显示 -->
                <view v-if="unreadMsgCount > 0" class="unread-badge">
                  {{ unreadMsgCount }}
                </view>
              </view>
            </get-phone>
          </view>
        </view>
      </view>
  
      <u-cell-group v-for="(item, index) in menuList" :key="index"
        :customStyle="{ background: '#fff', marginBottom: '20rpx', color: '' }" :border="false">
        <!-- 添加我的消息功能项 -->
        <get-phone v-for="(row, ind) in item" :key="ind" @onSuccess="itemClick(row)">
          <u-cell :title="row.title" isLink :border="false" @click="itemClick(row)">
            <u--image slot="icon" :showLoading="true" :src="row.icon" width="44rpx" height="44rpx"></u--image>
          </u-cell>
        </get-phone>
      </u-cell-group>
  
      <view class="call_wrap" @click="open"><u-avatar :src="`${baseUrl}/user/call.png`" :size="68"></u-avatar></view>
  
      <u-popup mode="center" bgColor="transparent" :show="show" @close="close">
        <view class="call_content">
          <view class="call_code"><u--image :showLoading="true" :src="src" width="400rpx" height="400rpx"></u--image>
          </view>
          <view class="call_close"><u--image :showLoading="true" :src="closeSrc" width="64rpx" height="64rpx"
              @click="close"></u--image></view>
        </view>
      </u-popup>
    </view>
  </template>
  
  <script>
  import { mapState } from "vuex";
  import { getDistributeDetail, getUnreadMessageTotal } from "@/api/user.js";
  import { getQuestion } from "@/api/question.js";
  import { question } from "@/common/config";
  export default {
    data() {
      return {
        src: process.env.VUE_APP_STATIC_FILE_URL + "/common/qcode.png",
        closeSrc: process.env.VUE_APP_STATIC_FILE_URL + "/user/call_close.png",
        titleStyle: {
          fontWeight: 600,
          fontSize: "36rpx",
          color: "#333",
        },
        baseUrl: process.env.VUE_APP_STATIC_FILE_URL,
        defaultUrl: process.env.VUE_APP_STATIC_FILE_URL + "/user/default_avatar.png",
  
        show: false,
        menuList: [
          [
            {
              title: "我的订单",
              icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/order.png",
              url: "pages/orderList/orderList",
            },
            {
              title: "我的卡密",
              icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/cdk.png",
              url: "pages/cdk/index",
            },
            {
              title: "我的钱包",
              icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/myWallet.png",
              url: "pages/myWallet/index",
            },
          ],
          [
            {
              title: "分销商管理",
              icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/distribute.png",
              handle: "handleDistribute",
            },
            {
              title: "我的分销订单",
              icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/distributeRecord.png",
              url: "pages/distributeRecord/index",
            },
          ],
          [
            {
              title: "查看问卷",
              icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/question.png",
              url: `pages/question/detail?questionId=${question.singleId}`,
            },
            {
              title: "我的检测管理",
              icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/reportListFather.png",
              url: "pages/reportListFather/index",
            },
            {
              title: "联系我们",
              icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/contact.png",
              handle: "open",
            },
          ],
        ],
        // 未读消息数量
        unreadMsgCount: 0
      };
    },
    // onLoad() {
    //   this.initData();
    // },
    onShow() {
      this.initData();
    },
    computed: {
      ...mapState({
        userInfo: (state) => state.User.userInfo,
      }),
    },
    methods: {
      /**
       * 点击分销商
       */
      async handleDistribute() {
        uni.showLoading({
          title: "加载中",
        });
        let res = await getDistributeDetail(this.userInfo.id);
        uni.hideLoading();
        let distributor = res.code === 200 ? res.data?.distributor : {};
        if (!distributor?.auditStatus || distributor?.auditStatus === "3") {
          // 如果是没有注册或已驳回的时候
          uni.$u.route({
            url: "pages/registerDistribute/index",
          });
        } else {
          uni.$u.route({
            url: "pagesA/distributeDetail/index",
          });
        }
      },
      /**
       * 我的消息
       */
      handleMsg() {
        // uni.$u.toast("正在研发中,敬请期待");
        uni.$u.route({
          url: "pages/msg/index",
        });
      },
      /**
       * 我的健康管理
       */
      handleHealthManage() {
        // uni.$u.route({
        //   url: "pages/question/sinple",
        //   params: {
        //     questionId: question.singleId,
        //   },
        // });
        // return;
        getQuestion(question.singleId).then((res) => {
          if (res.code === 200) {
            if (res.data.isCommit) {
              uni.$u.route({
                url: "pagesA/healthManage/index",
              });
            } else {
              uni.$u.route({
                url: "pages/question/sinple",
                params: {
                  questionId: question.singleId,
                },
              });
            }
          }
        });
      },
      itemClick({ id, url, handle }) {
        if (url) {
          return uni.$u.route({
            url,
          });
        }
        if (handle) {
          this[handle]();
          return;
        }
      },
      handleUserSetting() {
        uni.$u.route({
          url: "/pages/user/setting",
        });
      },
      close() {
        this.show = false;
      },
      open() {
        this.show = true;
      },
      initData() {
        getUnreadMessageTotal().then((res) => {
          this.unreadMsgCount = res.data
        })
      }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .user_page {
    min-height: 100vh;
    background: #f2f5f9;
  }
  
  .custom_navbar {
    position: relative;
    // height: 310rpx;
    background: #fff url("https://millenia.oss-cn-wuhan-lr.aliyuncs.com/2025-02-22/1740202070004_942be14f.png") no-repeat;
    background-size: cover;
    background-position: center;
    padding-bottom: 20rpx;
  }
  
  .content_wrap {
    position: relative;
    // margin-top: -112rpx;
    padding: 0 30rpx;
  
    .user_info {
      display: flex;
      align-items: center;
      // margin: 0 10rpx;
      // padding: 36rpx 30rpx;
      // border-radius: 16rpx;
      // background: linear-gradient(to bottom, #fff, #f6fbff);
      // box-shadow: inset 2rpx 2rpx 2rpx rgba(255, 255, 255, 0.78);
  
      .user_info_content {
        flex: auto;
      }
  
      .avatar_wrap {
        margin-right: 30rpx;
      }
  
      .user_name {
        margin-bottom: 16rpx;
        color: #2d3642;
        font-size: 32rpx;
        font-weight: 600;
      }
  
      .user_card {
        font-size: 28rpx;
        color: #2d3642;
      }
    }
  
    .list_wrap {
      display: flex;
      flex-wrap: wrap;
      margin-top: 20rpx;
  
      .list_item {
        // width: calc(50% - 20rpx);
        height: 164rpx;
        margin: 20rpx 10rpx 0;
        padding: 30rpx;
        box-sizing: border-box;
        background-size: cover;
  
        .item_title {
          margin-bottom: 16rpx;
          font-size: 32rpx;
        }
  
        .item_sub_title {
          font-size: 22rpx;
        }
      }
    }
  }
  
  .call_wrap {
    position: fixed;
    bottom: 80rpx;
    right: 30rpx;
  }
  
  .call_content {
    width: 590rpx;
    height: 770rpx;
    background: url("https://ma.aimeyear.com/staticFile/images/user/call_bg.png") no-repeat center/cover;
  
    .call_code {
      height: 518rpx;
      margin: 208rpx 41rpx 0;
      padding: 54rpx;
      background: #ffffff;
      border-radius: 24rpx 24rpx 24rpx 24rpx;
      box-sizing: border-box;
    }
  
    .call_close {
      display: flex;
      justify-content: center;
      margin-top: 108rpx;
    }
  }
  
  .list_item_wrapper {
    position: relative;
  }
  
  .unread-badge {
    position: absolute;
    top: 5rpx;
    right: 5rpx;
    background-color: red;
    color: white;
    border-radius: 50%;
    width: 28rpx;
    height: 28rpx;
    line-height: 28rpx;
    text-align: center;
    font-size: 24rpx;
  }
  </style>
  