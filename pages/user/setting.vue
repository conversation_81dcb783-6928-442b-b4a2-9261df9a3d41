<template>
	<view class="setting">
		<page-nav slot="top" title="个人设置" leftIconColor="#333333FF" :border="false" url="/pages/user/index"
			navigatorType="switchTab"></page-nav>
		<u-cell-group :customStyle="cellWrap" :border="false">
			<u-cell title="头像">
				<button slot="value" class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
					<u-avatar :src="userInfo.avater" :size="30"></u-avatar>
				</button>
			</u-cell>

			<u-cell title="昵称" :value="userInfo.nickName" isLink @click="handleNick"> </u-cell>
		</u-cell-group>
		<view class="btn_wrap">
			<u-button :customStyle="btnStyles" text="保存" color="#117ACDFF" size="large" @click="saveUser"> </u-button>
		</view>
		<u-modal :show="show" title="修改昵称" :showCancelButton="true" @cancel="handleCancel" @confirm="handleConfirm">
			<view style="width: 100%;">
				<u--input placeholder="请输入内容" border="surround" v-model="nickName"></u--input>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations,
		mapActions
	} from 'vuex';
	import {
		ossUpload
	} from '@/js_sdk/jason-alioss-upload/oss.js'
	import {
		updateUser
	} from '@/common/home.js'
	export default {
		data() {
			return {
				show: false,
				nickName: '',
				cellWrap: {
					backgroundColor: '#fff',
					borderRadius: '16rpx',
					overflow: 'hidden',
				},
				suffixIconStyle: {
					fontSize: '16px',
					color: '#909399',
				},
				userInfo: {
					nickName: '',
					avater: '',
					id: ''
				},
				btnStyles: {
					borderRadius: '14rpx',
				},

			};
		},
		onShow() {
			this.userInfo = {
				...this.user
			}
		},
		computed: {
			...mapState({
				user: (state) => state.User.userInfo,
			})
		},
		methods: {
			...mapMutations(['SET_USERINFO']),
			...mapActions(['getUserInfo']),
			onChooseAvatar(e) {
				console.log("choose avatar: ", e.detail.avatarUrl);
				ossUpload(e.detail.avatarUrl, e.detail.avatarUrl, 'wx/').then(res => {
					const {
						data,
						success
					} = res;
					if (success) {
						console.log('dddd1', data);
						this.userInfo.avater = data;
					}
				})

			},
			handleNick() {
				this.show = true;
				this.nickName = this.userInfo.nickName;
			},
			handleCancel() {
				this.show = false;
			},
			handleConfirm() {
				this.show = false;
				this.userInfo.nickName = this.nickName;

			},
			saveUser() {
				updateUser(this.userInfo).then(res => {
					if (res.code === 200) {
						this.getUserInfo(this.userInfo.id)
						uni.showToast({
							title: '保存成功'
						})
					}
				})
			}
		},
	};
</script>

<style lang="scss" scoped>
	.setting {
		padding: 0 30rpx 0 30rpx;

		.btn_wrap {
			position: fixed;
			left: 0;
			bottom: 80rpx;
			width: 100%;
			padding: 0 30rpx;
			box-sizing: border-box;
		}

		.head_wrap {
			display: flex;
			align-items: center;
			border-radius: 50rpx;
			overflow: hidden;
		}

		/deep/ .u-input {
			background-color: #fff !important;
		}
	}

	.avatar-wrapper {
		padding-left: 0;
		padding-right: 0;
		background-color: transparent;
		border-color: transparent;
	}

	.avatar-wrapper::after {
		border: none;
	}
</style>