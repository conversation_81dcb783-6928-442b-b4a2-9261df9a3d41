<template>
  <view class="user-index">
    <view class="header"> </view>

    <view class="content">
      <UserCard
        :avatar="userInfo.avater"
        :username="userInfo.name ? userInfo.name : userInfo.nickName"
        :userId="userInfo.id"
        @settings-click="handleUserSetting"
      />
      <view class="health-cards">
        <view class="health-card" style="margin-right: 10rpx">
          <get-phone @onSuccess="handleHealthManage">
            <HealthInfoCard
              title="我的健康管理"
              icon="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/wdjk.png"
              customClass="health-odd"
              @click="handleHealthManage"
            />
          </get-phone>
        </view>
        <view class="health-card" style="margin-left: 10rpx">
          <get-phone @onSuccess="handleMsg">
            <HealthInfoCard
              title="我的消息"
              icon="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/wdxx.png"
              customClass="health-even"
              @click="handleMsg"
            >
              <!-- 未读消息红点，当数量大于 0 时显示 -->
              <view v-if="unreadMsgCount > 0" class="unread-badge">
                {{ unreadMsgCount }}
              </view>
            </HealthInfoCard>
          </get-phone>
        </view>
      </view>

      <FunctionGroup
        title="我的订单"
        showMore
        moreText="全部订单"
        @more-click="viewAllOrders"
      >
        <block v-for="(item, index) in orderItems">
          <get-phone :key="index" @onSuccess="handleOrderItemClick(item)">
            <FunctionItem
              :icon="item.icon"
              :text="item.text"
              @click="handleOrderItemClick(item)"
            />
          </get-phone>
        </block>
      </FunctionGroup>

      <FunctionGroup title="管理工具">
        <block v-for="(item, index) in toolItems">
          <get-phone :key="index" @onSuccess="handleToolItemClick(item)">
            <FunctionItem
              :icon="item.icon"
              :text="item.text"
              @click="handleToolItemClick(item)"
            />
          </get-phone>
        </block>
      </FunctionGroup>
    </view>
    <u-popup mode="center" bgColor="transparent" :show="show" @close="close">
      <view class="call_content">
        <view class="call_code">
          <u--image
            :showLoading="true"
            :src="src"
            width="400rpx"
            height="400rpx"
          ></u--image>
        </view>
        <view class="call_close">
          <u--image
            :showLoading="true"
            :src="closeSrc"
            width="64rpx"
            height="64rpx"
            @click="close"
          ></u--image>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import UserCard from "./components/UserCard.vue";
import HealthInfoCard from "./components/HealthInfoCard.vue";
import FunctionGroup from "./components/FunctionGroup.vue";
import FunctionItem from "./components/FunctionItem.vue";
import { mapState } from "vuex";
import { getDistributeDetail, getUnreadMessageTotal } from "@/api/user.js";
import { getQuestion } from "@/api/question.js";
import { question } from "@/common/config";

export default {
  name: "UserIndex",
  components: {
    UserCard,
    HealthInfoCard,
    FunctionGroup,
    FunctionItem,
  },
  data() {
    return {
      src: process.env.VUE_APP_STATIC_FILE_URL + "/common/qcode.png",
      closeSrc: process.env.VUE_APP_STATIC_FILE_URL + "/user/call_close.png",
      titleStyle: {
        fontWeight: 600,
        fontSize: "36rpx",
        color: "#333",
      },
      baseUrl: process.env.VUE_APP_STATIC_FILE_URL,
      defaultUrl: process.env.VUE_APP_STATIC_FILE_URL + "/user/default_avatar.png",
      show: false,
      unreadMsgCount: 10,
      orderItems: [
        {
          icon: "https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/dfk.png",
          text: "待付款",
          type: "payment",
        },
        {
          icon: "https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/dfh.png",
          text: "待发货",
          type: "delivery",
        },
        {
          icon: "https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/dsh.png",
          text: "待收货",
          type: "receiving",
        },
        {
          icon:
            "https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/tksh.png",
          text: "退款/售后",
          type: "refund",
        },
      ],
      toolItems: [
        {
          icon:
            "https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/fxzx.png",
          text: "分销中心",
          handle: "handleDistribute",
        },
        {
          icon:
            "https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/user-cdk.png",
          text: "我的卡密",
          url: "pages/cdk/index",
        },
        {
          icon: "https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/user-wallet.png",
          text: "我的钱包",
          url: "pages/myWallet/index",
        },
        // {
        //   icon:
        //     "https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/user-distributerecord.png",
        //   text: "分销订单",
        //   url: "pages/distributeRecord/index",
        // },
        // {
        //   icon: "https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/yhj.png",
        //   text: "优惠券",
        //   url: "pages/coupon/index",
        // },
        // {
        //   icon:
        //     "https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/fpgl.png",
        //   text: "发票管理",
        //   url: "pages/invoice/index",
        // },
        {
          icon:
            "https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/lxwm.png",
          text: "联系我们",
          handle: "open",
        },
      ],
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.User.userInfo,
    }),
  },
  onShow() {
    this.initData();
  },
  methods: {
    initData() {
      getUnreadMessageTotal().then((res) => {
        this.unreadMsgCount = res.data;
      });
    },
    handleUserSetting() {
      uni.$u.route({
        url: "/pages/user/setting",
      });
    },
    /**
     * 我的消息
     */
    handleMsg() {
      uni.$u.route({
        url: "pages/msg/index",
      });
    },
    /**
     * 我的健康管理
     */
    handleHealthManage() {
      getQuestion(question.singleId).then((res) => {
        if (res.code === 200) {
          if (res.data.isCommit) {
            uni.$u.route({
              url: "pagesA/healthManage/index",
            });
          } else {
            uni.$u.route({
              url: "pages/question/sinple",
              params: {
                questionId: question.singleId,
              },
            });
          }
        }
      });
    },
    /**
     * 点击分销商
     */
    async handleDistribute() {
      uni.showLoading({
        title: "加载中",
      });
      let res = await getDistributeDetail(this.userInfo.id);
      uni.hideLoading();
      let distributor = res.code === 200 ? res.data?.distributor : {};
      if (!distributor?.auditStatus || distributor?.auditStatus === "3") {
        // 如果是没有注册或已驳回的时候
        uni.$u.route({
          url: "pages/registerDistribute/index",
        });
      } else {
        uni.$u.route({
          url: "pagesA/distributeDetail/index",
        });
      }
    },
    /**
     * 查看全部订单
     */
    viewAllOrders() {
      uni.$u.route({
        url: "pages/orderList/orderList?type=all",
      });
    },
    /**
     * 处理订单项点击
     */
    handleOrderItemClick(item) {
      uni.$u.route({
        url: `pages/orderList/orderList?type=${item.type}`,
      });
    },
    /**
     * 处理工具项点击
     */
    handleToolItemClick(item) {
      if (item.url) {
        return uni.$u.route({
          url: item.url,
        });
      }
      if (item.handle) {
        this[item.handle]();
        return;
      }
    },
    close() {
      this.show = false;
    },
    open() {
      this.show = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.user-index {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(
    to bottom,
    #e3c997,
    #ebd7b1,
    #f7edda,
    #fbf5ea,
    #fbf6ed,
    #faf7f5
  );

  .header {
    padding: 30rpx;
    padding-top: 100rpx;
    background: url("https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/wd.png")
      no-repeat center/cover;
    width: 100%;
    height: 330rpx;
    background-size: cover;
    background-position: center;
  }

  .content {
    flex: 1;
    padding: 0 30rpx;
    position: relative;
    top: -110rpx;

    .health-cards {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30rpx;

      .health-card {
        flex: 1;
        position: relative;

        .unread-badge {
          position: absolute;
          top: 5rpx;
          right: 5rpx;
          background-color: red;
          color: white;
          border-radius: 50%;
          width: 28rpx;
          height: 28rpx;
          line-height: 28rpx;
          text-align: center;
          font-size: 24rpx;
          z-index: 2;
        }
      }
    }
  }

  .call_wrap {
    position: fixed;
    bottom: 80rpx;
    right: 30rpx;
  }

  .call_content {
    width: 590rpx;
    height: 770rpx;
    background: url("https://ma.aimeyear.com/staticFile/images/user/call_bg.png")
      no-repeat center/cover;

    .call_code {
      height: 518rpx;
      margin: 208rpx 41rpx 0;
      padding: 54rpx;
      background: #ffffff;
      border-radius: 24rpx 24rpx 24rpx 24rpx;
      box-sizing: border-box;
    }

    .call_close {
      display: flex;
      justify-content: center;
      margin-top: 108rpx;
    }
  }
}
</style>
