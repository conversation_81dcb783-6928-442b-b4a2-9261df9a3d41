<template>
  <view class="user_page">
    <view class="custom_navbar">
      <u-navbar
        placeholder
        fixed
        leftIcon=" "
        title="个人中心"
        bgColor="transparent"
        :titleStyle="titleStyle"
      ></u-navbar>
      <view class="content_wrap">
        <view class="user_info">
          <u-avatar
            class="avatar_wrap"
            :src="userInfo.avater"
            :default-url="defaultUrl"
            :size="52"
          ></u-avatar>
          <view class="user_info_content">
            <view class="user_name">{{ userInfo.nickName }}</view>
            <view class="user_card">{{ userInfo.card }}</view>
          </view>
          <view class="user_info_right" @click="handleUserSetting">
            <u-icon name="arrow-right" color="#33333330" size="18"></u-icon>
          </view>
        </view>
        <view class="list_wrap">
          <get-phone :customStyle="{ width: '50%', color: '' }">
            <view
              :class="['list_item']"
              :style="{
                background: `url(${baseUrl}/user/item2.png) no-repeat center/cover`,
              }"
              @click="itemClick(item)"
            >
              <view class="item_title" :style="{ color: '#5d6d86ff' }">我的健康管理</view>
              <view class="item_sub_title" :style="{ color: '#5d6d8691' }"
                >个人健康管理中心</view
              >
            </view>
          </get-phone>

          <!-- 添加我的消息功能项 -->
          <get-phone
            :customStyle="{ width: '50%', color: '' }"
            @onSuccess="itemClick(item)"
          >
            <view
              :class="['list_item']"
              :style="{
                background: `url(${baseUrl}/user/item1.png) no-repeat center/cover`,
              }"
            >
              <view class="item_title" :style="{ color: '#5d6d86ff' }">我的消息</view>
              <view class="item_sub_title" :style="{ color: '#5d6d8691' }"
                >查看我的消息</view
              >
            </view>
          </get-phone>
        </view>
      </view>
    </view>

    <u-cell-group
      v-for="(item, index) in menuList"
      :key="index"
      :customStyle="{ background: '#fff' }"
      :border="false"
    >
      <u-cell
        v-for="(row, ind) in item"
        :key="ind"
        :title="row.title"
        isLink
        :border="false"
      >
        <u--image
          slot="icon"
          :showLoading="true"
          :src="row.icon"
          width="44rpx"
          height="44rpx"
        ></u--image>
      </u-cell>
    </u-cell-group>

    <!-- 功能列表 -->
    <!-- <view class="list_wrap">
        <get-phone
          :customStyle="{ width: '50%', color: '' }"
          v-for="item in list"
          :key="item.id"
          @onSuccess="itemClick(item)"
        >
          <view
            :class="['list_item', `list_item${item.id}`]"
            :style="{ background: `url(${item.icon}) no-repeat center/cover` }"
            @click="itemClick(item)"
          >
            <view class="item_title" :style="{ color: item.titleColor }">{{
              item.title
            }}</view>
            <view class="item_sub_title" :style="{ color: item.subTitleColor }">{{
              item.subTitle
            }}</view>
          </view>
        </get-phone>
      </view>
    </view> -->

    <view class="call_wrap" @click="open"
      ><u-avatar :src="`${baseUrl}/user/call.png`" :size="68"></u-avatar
    ></view>

    <u-popup mode="center" bgColor="transparent" :show="show" @close="close">
      <view class="call_content">
        <view class="call_code"
          ><u--image
            :showLoading="true"
            :src="src"
            width="400rpx"
            height="400rpx"
          ></u--image
        ></view>
        <view class="call_close"
          ><u--image
            :showLoading="true"
            :src="closeSrc"
            width="64rpx"
            height="64rpx"
            @click="close"
          ></u--image
        ></view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { mapState } from "vuex";
import { getDistributeDetail } from "@/api/user.js";
import { getQuestion } from "@/api/question.js";
import { question } from "@/common/config";
export default {
  data() {
    return {
      src: process.env.VUE_APP_STATIC_FILE_URL + "/common/qcode.png",
      closeSrc: process.env.VUE_APP_STATIC_FILE_URL + "/user/call_close.png",
      titleStyle: {
        fontWeight: 600,
        fontSize: "36rpx",
        color: "#333",
      },
	  baseUrl: process.env.VUE_APP_STATIC_FILE_URL,
      defaultUrl: process.env.VUE_APP_STATIC_FILE_URL + "/user/default_avatar.png",
      list: [
        {
          id: 1,
          title: "我的检测激活记录",
          titleColor: "#916f72ff",
          subTitle: "快速查看检测激活信息",
          subTitleColor: "#916f7291",
          icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/item1.png",
          url: "pages/reportListFather/index",
        },
        // {
        // 	id: 1,
        // 	title: "检测激活",
        // 	titleColor: '#916f72ff',
        // 	subTitle: "快速激活检测",
        // 	subTitleColor: '#916f7291',
        // 	icon: require('@/static/images/user/item1.png')
        // },
        {
          id: 2,
          title: "我的健康管理",
          titleColor: "#5d6d86ff",
          subTitle: "个人健康管理中心",
          subTitleColor: "#5d6d8691",
          icon: process.env.VUE_APP_STATIC_FILE_URL + "
          // url: "pagesA/healthManage/index",
          // url: "pages/question/sinple",
        },
        {
          id: 3,
          title: "我的卡密",
          titleColor: "#5e897dff",
          subTitle: "获取卡密码",
          subTitleColor: "#5e897d91",
          icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/item3.png",
          url: "pages/cdk/index",
        },
        {
          id: 4,
          title: "我的订单",
          titleColor: "#918860ff",
          subTitle: "个人订单管理中心",
          subTitleColor: "#91886091",
          icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/item4.png",
          url: "pages/orderList/orderList",
        },
        {
          id: 6,
          title: "分销商管理",
          titleColor: "#918860ff",
          subTitle: "注册或查看分销商信息",
          subTitleColor: "#91886091",
          icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/item4.png",
          // url: "pages/registerDistribute/index",
        },
        {
          id: 7,
          title: "我的分销订单",
          titleColor: "#918860ff",
          subTitle: "查看分销的订单",
          subTitleColor: "#91886091",
          icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/item4.png",
          url: "pages/distributeRecord/index",
        },
        {
          id: 8,
          title: "我的钱包",
          titleColor: "#918860ff",
          subTitle: "查看我的钱包",
          subTitleColor: "#91886091",
          icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/item4.png",
          url: "pages/myWallet/index",
        },
        {
          id: 5,
          title: "联系我们",
          titleColor: "#6e97bcff",
          subTitle: "快速联系客服",
          subTitleColor: "#6e97bc91",
          icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/item5.png",
        },
        {
          id: 9,
          title: "查看问卷",
          titleColor: "#918860ff",
          subTitle: "查看问卷",
          subTitleColor: "#91886091",
          icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/item4.png",
          url: `pages/question/detail?questionId=${question.singleId}`,
        },
      ],
      show: false,
      menuList: [
        [
          {
            title: "我的订单",
            icon: process.env.VUE_APP_STATIC_FILE_URL + "/user/order.png",
            url: "pages/reportListFather/index",
          },
        ],
      ],
    };
  },

  computed: {
    ...mapState({
      userInfo: (state) => state.User.userInfo,
    }),
  },
  methods: {
    /**
     * 点击分销商
     */
    async handleDistribute() {
      uni.showLoading({
        title: "加载中",
      });
      let res = await getDistributeDetail(this.userInfo.id);
      uni.hideLoading();
      let distributor = res.code === 200 ? res.data?.distributor : {};
      if (!distributor?.auditStatus || distributor?.auditStatus === "3") {
        // 如果是没有注册或已驳回的时候
        uni.$u.route({
          url: "pages/registerDistribute/index",
        });
      } else {
        uni.$u.route({
          url: "pagesA/distributeDetail/index",
        });
      }
    },
    handleHealthManage() {
      getQuestion(question.singleId).then((res) => {
        if (res.code === 200) {
          if (res.data.isCommit) {
            uni.$u.route({
              url: "pagesA/healthManage/index",
            });
          } else {
            uni.$u.route({
              url: "pages/question/sinple",
              params: {
                questionId: question.singleId,
              },
            });
          }
        }
      });
    },
    itemClick({ id, url }) {
      if (url) {
        return uni.$u.route({
          url,
        });
      }
      if (id === 2) {
        this.handleHealthManage();
        return;
      }

      if (id === 6) {
        // 如果点击的是分销商
        this.handleDistribute();
        return;
      }

      if ([1].includes(id)) {
        return uni.$u.toast("正在研发中,敬请期待");
      }

      if (id === 5) {
        return this.open();
      }
    },
    handleUserSetting() {
      uni.$u.route({
        url: "/pages/user/setting",
      });
    },
    close() {
      this.show = false;
    },
    open() {
      this.show = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.user_page {
  min-height: 100vh;
  background: #f2f5f9;
}

.custom_navbar {
  position: relative;
  // height: 310rpx;
  background: #fff
    url("https://millenia.oss-cn-wuhan-lr.aliyuncs.com/2025-02-22/1740202070004_942be14f.png")
    no-repeat;
  background-size: cover;
  background-position: center;
  padding-bottom: 20rpx;
}

.content_wrap {
  position: relative;
  // margin-top: -112rpx;
  padding: 0 30rpx;
  .user_info {
    display: flex;
    align-items: center;
    // margin: 0 10rpx;
    // padding: 36rpx 30rpx;
    // border-radius: 16rpx;
    // background: linear-gradient(to bottom, #fff, #f6fbff);
    // box-shadow: inset 2rpx 2rpx 2rpx rgba(255, 255, 255, 0.78);

    .user_info_content {
      flex: auto;
    }

    .avatar_wrap {
      margin-right: 30rpx;
    }

    .user_name {
      margin-bottom: 16rpx;
      color: #2d3642;
      font-size: 32rpx;
      font-weight: 600;
    }

    .user_card {
      font-size: 28rpx;
      color: #2d3642;
    }
  }

  .list_wrap {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20rpx;

    .list_item {
      // width: calc(50% - 20rpx);
      height: 164rpx;
      margin: 20rpx 10rpx 0;
      padding: 30rpx;
      box-sizing: border-box;
      background-size: cover;

      .item_title {
        margin-bottom: 16rpx;
        font-size: 32rpx;
      }

      .item_sub_title {
        font-size: 22rpx;
      }
    }
  }
}

.call_wrap {
  position: fixed;
  bottom: 80rpx;
  right: 30rpx;
}

.call_content {
  width: 590rpx;
  height: 770rpx;
  background: url("https://ma.aimeyear.com/staticFile/images/user/call_bg.png") no-repeat center/cover;

  .call_code {
    height: 518rpx;
    margin: 208rpx 41rpx 0;
    padding: 54rpx;
    background: #ffffff;
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    box-sizing: border-box;
  }

  .call_close {
    display: flex;
    justify-content: center;
    margin-top: 108rpx;
  }
}
</style>
