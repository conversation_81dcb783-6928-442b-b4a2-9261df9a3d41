<template>
	<view class="content">
		<z-paging ref="paging" v-model="dataList" @query="queryList" :use-page-scroll="true">
			<view slot="top">
				<page-nav title="菌群报告"  url="pages/reportListFather/index" :border="false"></page-nav>
			</view>
			<view class="list">
				<view class="item" v-for="item in dataList" :key="item.id">
					<report-item :key="item.id" :item="item" @onCancel="handleCancel(item.orderId)"></report-item>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
	import ZPMixin from '@/uni_modules/z-paging/components/z-paging/js/z-paging-mixin'
	import ReportItem from './components/reportItem.vue';
	import {
		getReportPDF
	} from '@/api/activation.js';
	import {
		mapState
	} from 'vuex';

	export default {
		mixins: [ZPMixin],
		components: {
			ReportItem
		},
		data() {
			return {
				dataList: [],
				sampleCode: ''
			}
		},
		onLoad(option) {
			this.sampleCode = option.sampleCode;
		},
		computed:{
			...mapState({
				userInfo: (state) => state.User.userInfo,
			}),
		},
		methods: {
			queryList(pageNum, pageSize) {
				if (!this.sampleCode) return;
				getReportPDF(this.sampleCode,{
					pageNum,
					pageSize
				}).then(res => {
					// 将请求结果通过complete传给z-paging处理，同时也代表请求结束，这一行必须调用
					this.$refs.paging.complete(res.rows);
				}).catch(res => {
					// 如果请求失败写this.$refs.paging.complete(false)，会自动展示错误页面
					// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
					// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
					this.$refs.paging.complete(false);
				})
			}
		},
	}
</script>

<style lang="scss" scoped>
	.content {
		background-color: #F9F9F9;

		.list {
			padding: 0 20rpx;
		}
	}
</style>