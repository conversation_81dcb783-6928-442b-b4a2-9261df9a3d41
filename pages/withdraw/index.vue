<template>
	<view class="page_wrap">
		<page-nav title="提现" navigatorType="switchTab" url="pages/user/index" :border="false"></page-nav>
		<view class="wallet_wrap">
			<view class="tip">
				提现到微信的零钱
			</view>
			<view class="form_wrap">

				<view class="form_item">
					<view class="label">
						提现金额
					</view>
					<view class="item_con">
						<u--input type="digit" :customStyle="{padding:'12rpx 0'}"  prefixIcon="rmb" placeholder="请输入提现金额" border="bottom"
							v-model="withdrawAmount"></u--input>
					</view>
					<view class="item_tip">
						当前可提现金额：{{ walletDetail.withdrawalAmount }}
					</view>
				</view>
			</view>
		</view>
		<view class="footer_wrap">
			<u-button color="#117ACD" :loading="submitLoading" :customStyle="payStyles" @click="submitWithdraw">
				提现
			</u-button>

		</view>
	</view>
</template>

<script>
	import {
		getMyWallet,
		applyWithdraw,
		withdrawalCancel
	} from '@/api/user.js';
	export default {
		data() {
			return {
				withdrawAmount: '',
				walletDetail: {},
				showWithdrawPopup: false
			}
		},
		computed: {
			payStyles() {
				return {
					height: '98rpx',
					fontWeight: '600',
					fontSize: '36rpx',
					color: '#FFFFFF',
					borderRadius: '14rpx'
				}
			}
		},
		onShow() {
			this.initData();
		},
		methods: {

			handleWithdraw() {
				// uni.$u.route('/pages/withdrawRecord/index');
				this.withdrawAmount = '';
				this.showWithdrawPopup = true;
			},
			async submitWithdraw() {
				const reg = /^(0|[1-9]\d*)(\.\d{1,2})?$/;
				const withdrawalAmount = Number(this.withdrawAmount);
				const limitAmount = 200;
				const maxAmount = this.walletDetail.withdrawalAmount || 0;

				if (!reg.test(withdrawalAmount)) {
				  return uni.$u.toast('请输入有效的金额，最多两位小数');
				}

				if (!withdrawalAmount || withdrawalAmount <= 0) {
					return uni.$u.toast('请输入有效金额');
				}

				if (withdrawalAmount > maxAmount) {
					return uni.$u.toast('金额超过可提现余额');
				}
				
				if (withdrawalAmount > limitAmount) {
					return uni.$u.toast('单笔提现金额不能超过200元');
				}



				this.submitLoading = true;

				const res = await applyWithdraw({
					withdrawalAmount
				});
				if (res.code === 200 && res.data) {
					// console.log("data", res.data);
					const mchId = res.data?.mchId;
					const appId = res.data?.appId;
					const packageInfo = res.data?.packageInfo;

					this.withdrawalRecordId = res.data?.withdrawalRecordId;
					this.showWithdrawPopup = false;

					// 调用微信收款界面
					uni.requestMerchantTransfer({
						mchId: mchId,
						appId: appId,
						package: packageInfo,
						success: (res) => {
							console.log('res: ', res)
							uni.$u.toast('提现成功');

							this.initData(); // 刷新钱包
						},
						fail: (err) => {
							let errMsg = err.errMsg;
							if (errMsg == "requestMerchantTransfer:fail cancel") {
								errMsg = "取消转账";
							}
							uni.showToast({
								title: errMsg,
								icon: 'none'
							});
							withdrawalCancel({
								withdrawalRecordId: this.withdrawalRecordId
							});
							console.error('提现失败：', err);
						},
						complete: () => {
							this.submitLoading = false;
						}
					});
				} else {
					this.submitLoading = false;
					uni.$u.toast(res.msg || '提现失败');
				}
			},
			initData() {
				uni.showLoading({
					title: '加载中'
				});
				getMyWallet().then(res => {
					this.walletDetail = res.code === 200 ? res.data : {}
					uni.hideLoading();
				}).catch(() => {
					uni.hideLoading();
				})

			}

		}
	}
</script>

<style lang="scss" scoped>
	.page_wrap {
		// background-color: #fff;
		min-height: 100vh;

		.wallet_wrap {
			padding: 30rpx 30rpx 0 30rpx;

			.tip {
				padding: 10rpx 0;
				font-size: 34rpx;
				font-weight: 500;
			}

			.form_wrap {

				margin-top: 30rpx;
				.form_item {
					.label {
						font-size: 30rpx;
						padding: 10rpx 0;
					}

					.item_con {}

					.item_tip {
						font-size: 24rpx;
						margin-top: 10rpx;
						color: #929292;
					}
				}
			}



		}

		.footer_wrap {

			box-sizing: border-box;
			position: fixed;
			left: 0;
			bottom: 70rpx;
			width: 100%;
			padding: 0 20rpx;

			// .footer_tip {
			// 	text-align: center;
			// 	font-weight: 500;
			// 	font-size: 26rpx;
			// 	color: rgba(51, 51, 51, 0.4);
			// 	margin-bottom: 28rpx;
			// }
		}

	}
</style>