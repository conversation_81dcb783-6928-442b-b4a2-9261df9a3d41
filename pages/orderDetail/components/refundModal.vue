<template>
	<u-popup :show="show" mode="bottom" round="16rpx">
		<view class="refund_con">
			<view class="header">
				申请退款
			</view>
			<view class="tip">
				因为是虚拟产品因此我们需要审核情况，1-3个工作日内完成审核
			</view>
			<view class="amount_cell">
				<view class="amout_label">
					退款金额
				</view>
				<view class="amout_value">
					￥{{orderDetail.payAmount}}
				</view>
			</view>
			<u-line></u-line>
			<view class="header">
				退款原因
			</view>
			<view>
				<u--textarea :style="{background: '#FAFAFA',
			borderRadius: '12rpx'}" v-model="refundReason" border="none" placeholder="填写退款原因，有助于更好的处理售后问题"></u--textarea>
			</view>
			<view class="action_wrap" >
				<u-button :plain="true" text="取消" :customStyle="cancelStyles" @click="handleCancel"></u-button>
				<u-button :loading="btnLoading" color="#117ACD" :customStyle="payStyles" @click="handleRefund">
					申请退款
				</u-button>
			</view>
		</view>
	</u-popup>
</template>

<script>
	import {
		refundOrder
	} from '@/api/order.js'
	export default {
		data() {
			return {
				orderDetail: {},
				btnLoading: false,
				show: false,
				refundReason: ''
			}
		},
		computed: {
			payStyles() {
				let styles = {
					width: '315rpx',
					height: '98rpx',
					borderRadius: '14rpx',
					fontWeight: '500',
					fontSize: '30rpx',
					color: '#FFFFFF',
					marginLeft: '20rpx'
				}
				return styles;
			},
			cancelStyles() {
				let styles = {
					width: '315rpx',
					height: '98rpx',
					borderRadius: '14rpx',
					fontWeight: '400',
					fontSize: '30rpx',
					color: '#666666'
				}
				return styles;
			},
		},
		methods: {
			handleCancel() {
				this.show = false
			},
			handleRefund() {
				let {
					refundReason,
					orderDetail
				} = this;
				if (!refundReason) {
					return uni.$u.toast('请填写退款原因');
				}
				this.btnLoading = true;
				refundOrder({
					id: orderDetail.orderId,
					refundReason
				}).then(res => {
					this.btnLoading = false;
					console.log('refundOrder', res);
					if (res.code === 200) {
						this.show = false
						uni.$u.toast('退款申请已提交，请等待管理员审核');
						setTimeout(() => {
							this.$emit('onSuccess')
						}, 500)
					} else {
						uni.$u.toast(res.msg || '操作失败');
					}
				})
			},
			open(orderDetail) {
				this.orderDetail = orderDetail
				this.show = true
			}
		},
	}
</script>

<style lang="scss" scoped>
	.refund_con {
		box-sizing: border-box;
		width: 100%;
		padding: 30rpx;

		.header {
			font-weight: 600;
			font-size: 34rpx;
			color: #333333;
			line-height: 40rpx;
			padding: 30rpx 0;
		}

		.tip {
			margin-top: 15rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: rgba(51, 51, 51, 0.59);
			line-height: 28rpx;
		}


		.amount_cell {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 0;

			.amout_label {
				font-weight: 400;
				font-size: 30rpx;
				color: #333333;
			}

			.amout_value {
				font-weight: 600;
				font-size: 34rpx;
				color: #FD3232;
			}
		}

		.action_wrap {
			margin-top: 40rpx;
			box-sizing: border-box;
			width: 100%;
			display: flex;
			overflow: hidden;
		}
	}
</style>