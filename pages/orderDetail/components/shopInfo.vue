<template>
    <view class="shop_info">
        <view class="item_content" @click="handleDetail" v-for="item in orderInfo.orderDetailList" :key="item.id">
            <view :style="{ position: 'relative' }">
                <prd-label :label="item.tagName"></prd-label>
                <!-- height="130rpx" -->
                <u--image class="icon_img" width="168rpx" height="168rpx" :showLoading="true" mode="aspectFit"
                    :src="item.goodsPic"></u--image>
            </view>

            <view class="left_wrap">
                <view class="title">{{ item.goodsName }}</view>
                <view class="price_con">
                    <view class="price">
                        <view class="price_icon">￥</view>
                        <view class="amount">{{ item.goodsPrice }}</view>
                    </view>
                    <view class="count">
                        x{{ item.num }}
                    </view>
                </view>
            </view>
        </view>
        <u-line></u-line>
        <view class="footer_wrap">
            <view class="footer_left">
                {{ ['1', '3'].includes(orderInfo.status) ? '订单金额' : '实付款' }}
            </view>
            <view class="footer_right">
                <view class="unit">￥</view>
                <view class="money">{{ orderInfo.orderAmount }}</view>
            </view>

        </view>
    </view>
</template>

<script>
import {
    groupTypeEnums,
    cycleUnitEnums
} from '@/util/enums.js';
export default {
    props: {
        orderInfo: {
            type: Object,
            default() {
                return {

                }
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.shop_info {
    margin-top: -118rpx;
    // transform: translateY(-50%);
    background: #fff;
    padding: 0 30rpx;
    border-radius: 16rpx;
    overflow: hidden;

    .item_content {
        padding: 30rpx 0;
        display: flex;

        .left_wrap {
            padding-left: 20rpx;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;

            .title {
                font-weight: 500;
                font-size: 26rpx;
                color: #333333;
            }

            .time {
                font-weight: 500;
                font-size: 26rpx;
                color: rgba(51, 51, 51, 0.6);
            }

            .price_con {
                display: flex;

                align-items: center;
                justify-content: space-between;

                .price {
                    display: flex;
                    align-items: baseline;

                    .price_icon {
                        font-weight: bold;
                        font-size: 20rpx;
                        color: #FD3232;
                    }

                    .amount {
                        font-weight: bold;
                        font-size: 36rpx;
                        color: #FD3232;
                    }
                }

                .count {
                    font-weight: bold;
                    font-size: 36rpx;
                    color: #333333;
                }
            }
        }


    }

    .footer_wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;

        .footer_left {
            font-weight: 600;
            font-size: 28rpx;
            color: #FD3232;
        }

        .footer_right {
            font-weight: bold;
            font-size: 20rpx;
            color: #FD3232;
            display: flex;
            align-items: baseline;

            .unit {
                font-size: 20rpx;
            }

            .money {
                font-size: 36rpx;
            }
        }

    }


}
</style>