<template>
    <view class="page_wrap">
        <view class="header_wrap">
            <page-nav title="订单详情" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" bgColor="inherit"
                :border="false" url="/pages/orderList/orderList"></page-nav>
            <view class="status_con">
                <view class="status_label">
                    {{ statusDetail.label }}
                </view>
                <view class="expire-time" v-if="orderDetail.status == '1'">
                    <view>请在</view>
                    <u-count-down :time="diffTime(orderDetail.createTime)" format="mm:ss" @change="timeChange" autoStart
                        @finish="timeFinsh">
                        <view class="time">
                            <view class="time__custom">
                                <text class="time__custom__item">{{
                                    timeData.minutes >= 10 ? timeData.minutes : '0' + timeData.minutes }}</text>
                            </view>
                            <text class="time__doc">:</text>
                            <view class="time__custom">
                                <text class="time__custom__item">{{
                                    timeData.seconds >= 10 ? timeData.seconds : '0' + timeData.seconds }}</text>
                            </view>
                        </view>
                    </u-count-down>
                    <view>分钟内完成支付</view>
                </view>
                <!-- <view v-if="orderDetail.status == '3'&&tip" class="status_desc">
					{{tip}}
				</view> -->
            </view>
        </view>
        <view class="main_con">

            <!-- 订单的商品信息 -->
            <shop-info :orderInfo="orderDetail"></shop-info>
            <!-- 订单信息 -->
            <order-info :orderInfo="orderDetail"></order-info>
            <!-- 售后信息 -->
            <after-sale :orderInfo="orderDetail" @onRefund="handleRefund"></after-sale>
            <!-- 底部的操作 -->
            <view v-if="orderDetail.status === '1'" class="footer_wrap">
                <view class="footer_content">
                    <u-button :plain="true" text="取消订单" :customStyle="cancelStyles" @click="handleCancel"></u-button>
                    <u-button color="#117ACD" :customStyle="payStyles" @click="handleGoPay">
                        继续付款
                    </u-button>
                </view>
            </view>
        </view>
        <!-- 申请退款弹框 -->
        <refund-modal ref="refundModal" @onSuccess="getOrderDetail"></refund-modal>
        <!-- 取消订单弹框 -->
        <cancel-order-modal ref="cancelOrderModal" @onSuccess="getOrderDetail"></cancel-order-modal>
    </view>
</template>

<script>
import ShopInfo from './components/shopInfo.vue';
import OrderInfo from './components/orderInfo.vue';
import AfterSale from './components/afterSale.vue';
import RefundModal from './components/refundModal.vue'
import {
    orderStatusEnums,
} from '@/util/enums.js';
import {
    findOrder,
    closeOrder
} from '@/api/order.js';
import {
    weixinPay
} from '@/common/home.js'
import dayjs from 'dayjs';
export default {
    components: {
        ShopInfo,
        OrderInfo,
        AfterSale,
        RefundModal
    },
    data() {
        return {
            orderId: '',
            submitLoading: false,
            orderDetail: {},
            timeData: {},
        }
    },
    onLoad(options) {
        this.orderId = options.orderId;
        if (this.orderId) {
            this.getOrderDetail();
        }
    },
    computed: {
        statusDetail() {
            let item = orderStatusEnums.find(row => row.value === this.orderDetail.status);
            return item || orderStatusEnums[0]
        },
        // tip() {
        // 	console.log('diffTime(orderDetail.createTime)', this.diffTime(this.orderDetail.createTime))
        // 	let {
        // 		createTime,
        // 		status
        // 	} = this.orderDetail;
        // 	let diff = this.diffTime(createTime)
        // 	let tips = {
        // 		'3': diff > 0 ? '支付超时，已自动取消' : '',
        // 		'1': '请在15:00分钟内完成支付',
        // 	}
        // 	return tips[status]
        // },
        payStyles() {
            let styles = {
                width: '315rpx',
                height: '98rpx',
                borderRadius: '14rpx',
                fontWeight: '500',
                fontSize: '30rpx',
                color: '#FFFFFF',
                marginLeft: '20rpx'
            }
            return styles;
        },
        cancelStyles() {
            let styles = {
                width: '315rpx',
                height: '98rpx',
                borderRadius: '14rpx',
                fontWeight: '400',
                fontSize: '30rpx',
                color: '#666666'
            }
            return styles;
        },
    },
    methods: {
        timeChange(e) {
            this.timeData = e
        },
        diffTime(createTime) {
            const expireTime = dayjs(createTime).add('15', 'minute').format('YYYY-MM-DD HH:mm:ss');
            const curTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
            const diff = dayjs(expireTime).diff(curTime, 'seconds') * 1000
            console.log('diff', diff);
            return diff;
        },
        timeFinsh() {
            this.getOrderDetail();
            // // 倒计时结束
            // closeOrder(this.orderId).then(res => {
            // 	if (res.code === 200) {
            // 		this.getOrderDetail();
            // 	}
            // }).catch(() => {
            // 	uni.showToast({
            // 		title: '操作失败',
            // 		icon: 'none',
            // 		duration: 2000
            // 	});
            // })
        },
        handleRefund() {
            this.$refs.refundModal.open(this.orderDetail);
        },
        handleCancel() {
            this.$refs.cancelOrderModal.open(this.orderDetail.orderId);
        },
        getOrderDetail() {
            uni.showLoading({
                title: '加载中'
            });
            findOrder(this.orderId).then(res => {
                uni.hideLoading();
                this.orderDetail = res.code === 200 ? res.data : {};
            }).catch(() => {
                uni.hideLoading();
            })
        },
        // 继续支付
        handleGoPay() {
            console.log('orderDetail', this.orderDetail);
            const {
                id,
                orderDetailList
            } = this.orderDetail;
            // this.$refs.paytypeModal.open();
            const params = {
                orderId: id,
                goodsItemList: orderDetailList.map(item => ({
                    goodsId: item.goodsId,
                    num: item.num * 1,
                }))
            }
            weixinPay(params).then(payData => {
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: payData.timeStamp,
                    nonceStr: payData.nonceStr,
                    package: payData.packageValue,
                    paySign: payData.paySign,
                    signType: payData.signType,
                    success: res => {
                        console.log('ppppppppp', res);
                        if (res.errMsg === 'requestPayment:ok') {
                            uni.$u.route({
                                url: "/pages/paySuccess/paySuccess",
                            });
                        }
                        // this.queryWXXCXpayResultData()
                    },
                    fail: err => {
                        uni.$u.toast('支付失败');

                    }
                })
            })
        }
    },
}
</script>

<style lang="scss" scoped>
.page_wrap {
    background-color: #F8F9F9;
    min-height: 100vh;

    .header_wrap {
        width: 100%;
        height: 457rpx;
        background: linear-gradient(180deg, #117ACD 43%, rgba(17, 122, 205, 0.67) 71%, #F8F9F9 100%);

        .status_con {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 40rpx;
            padding: 0 20rpx;

            .expire-time {
                display: flex;
                align-items: center;
                background: rgba(255, 255, 255, 0.11);
                border-radius: 12rpx 12rpx 12rpx 12rpx;
                font-weight: 600;
                font-size: 32rpx;
                color: #FFFFFF;
                padding: 10rpx 61rpx;

                .time {
                    display: flex;
                    align-items: center;
                }
            }

            .status_label {
                font-weight: 600;
                font-size: 64rpx;
                color: #FFFFFF;
            }

            .status_desc {
                background: rgba(255, 255, 255, 0.11);
                border-radius: 12rpx 12rpx 12rpx 12rpx;
                font-weight: 600;
                font-size: 32rpx;
                padding: 6rpx 40rpx;
                color: #FFFFFF;
            }
        }
    }

    .main_con {
        padding: 0 20rpx;
    }



    .footer_wrap {
        height: 156rpx;

        .footer_content {
            box-sizing: border-box;
            position: fixed;
            left: 0;
            bottom: 0;
            width: 100%;
            padding: 30rpx 30rpx 48rpx 30rpx;
            // background-color: #fff;
            display: flex;
            overflow: hidden;
        }
    }
}
</style>