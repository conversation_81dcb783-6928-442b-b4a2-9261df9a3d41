<template>
    <view class="container">
        <!-- 顶部导航 -->
        <page-nav title="确认订单" bgColor="transparent" :border="false"></page-nav>

        <!-- 订单内容 -->
        <view class="order-content">
            <!-- 收货地址区域 -->
            <view class="address-content">
                <view class="section-title">收货地址</view>

                <view class="address-form">
                    <!-- 收货人姓名 -->
                    <view class="form-item">
                        <u-input v-model="addressForm.name" placeholder="请输入收货人姓名" border="none"></u-input>
                    </view>

                    <!-- 手机号码 -->
                    <view class="form-item">
                        <u-input v-model="addressForm.phone" placeholder="请输入手机号码" type="number"
                            border="none"></u-input>
                    </view>

                    <!-- 所在地区 -->
                    <view class="form-item" @click="showAddressPicker = true">
                        <view class="region-selector">
                            <text class="region-text" :class="{ placeholder: !addressForm.region }">
                                {{ addressForm.region || '请选择省市区' }}
                            </text>
                            <u-icon name="arrow-right" color="#999" size="12"></u-icon>
                        </view>
                    </view>

                    <!-- 地区选择器 -->
                    <u-picker ref="uPicker" :show="showAddressPicker" :columns="addressColumns"
                        @confirm="confirmAddress" @cancel="showAddressPicker = false" @change="changeAddress"
                        keyName="label" :defaultIndex="defaultIndex"></u-picker>

                    <!-- 详细地址 -->
                    <view class="form-item">
                        <u-textarea v-model="addressForm.detail" placeholder="请输入详细地址信息" :autoHeight="true"
                            :border="false" maxlength="200"></u-textarea>
                    </view>

                </view>
            </view>


            <!-- 商品列表区域 -->
            <view class="goods-section">
                <view class="goods-list">
                    <order-item v-for="item in orderItems" :key="item.id" :item="item"></order-item>
                </view>
            </view>


        </view>

        <!-- 底部提交区域 -->
        <view class="bottom-bar">
            <view class="total-section">
                <view class="total-info">
                    <text class="total-label">合计：</text>
                    <text class="total-price"><text class="price-icon">¥</text>{{ totalAmount.toFixed(2) }}</text>
                    <text class="shipping-fee">(含运费¥{{ shippingFee.toFixed(2) }})</text>
                </view>
            </view>
            <view class="submit-btn" :class="{ loading: payLoading }" @click="submitOrder">
                <u-loading-icon v-if="payLoading" mode="circle" color="#fff" size="16"></u-loading-icon>
                <text class="submit-text" v-if="!payLoading">立即支付</text>
                <text class="submit-text" v-else>支付中...</text>
            </view>
        </view>
    </view>
</template>

<script>
import PageNav from '@/components/page-nav/page-nav.vue'
import OrderItem from './components/order-item.vue'
import { weixinPay } from '@/common/home.js'
import { addAddress } from '@/api/order.js'
import { generateConfirmOrder } from '@/api/cart.js'

export default {
    name: 'OrderConfirm',
    components: {
        PageNav,
        OrderItem
    },
    data() {
        return {
            // 订单商品列表
            orderItems: [],
            // 收货地址表单
            addressForm: {
                name: '',
                phone: '',
                region: '',
                detail: ''
            },
            // 运费
            shippingFee: 0,
            goodsAmount: 0,
            // 地址选择器相关
            showAddressPicker: false,
            addressColumns: [[], [], []],
            addressData: [],
            selectedAddress: [],
            addressObj: {},
            defaultIndex: [],
            // 支付相关状态
            payLoading: false,
            weixinInfo: {}
        }
    },
    computed: {
        // 总金额
        totalAmount() {
            return this.goodsAmount + this.shippingFee;
        }
    },
    onLoad(options) {
        // 获取用户信息
        this.weixinInfo = uni.getStorageSync('weixin') || {};
        if (this.weixinInfo && this.weixinInfo.phone) {
            this.addressForm.phone = this.weixinInfo.phone;
        }

        // 接收购物车传递的商品数据
        this.initOrderData(options);
        // 初始化地址选择器
        this.initAddressPicker();
    },
    methods: {
        // 初始化订单数据
        initOrderData(options) {
            // 从购物车页面接收选中商品数据
            console.log('订单页面参数：', options);

            // 从存储中获取选中的商品数据
            const orderItems = uni.getStorageSync('orderItems');

            const params = {
                cartIds: orderItems.map(item => item.id).join(',')
            };
            console.log(params)
            generateConfirmOrder(params).then(res => {
                const { code, data } = res

                console.log('orderItems', res);
                if (code === 200 && data) {
                    this.orderItems = data.cartList;
                    this.goodsAmount = data.totalAmount;
                    if (data.receiveAddressList && data.receiveAddressList.length > 0) {
                        this.addressForm.phone = data.receiveAddressList[0].receiverPhone;
                        this.addressForm.name = data.receiveAddressList[0].receiverName;
                        this.addressForm.region = data.receiveAddressList[0].province + ' ' + data.receiveAddressList[0].city + ' ' + data.receiveAddressList[0].district;
                        this.addressForm.detail = data.receiveAddressList[0].detailAddress;
                    }
                    // 清除存储的数据
                } else {
                    // 如果没有商品数据，返回购物车页面
                    uni.showToast({
                        title: '订单商品数据异常',
                        icon: 'none'
                    });
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1500);
                }
            })


        },

        // 初始化地址选择器
        initAddressPicker() {
            const addressLists = uni.getStorageSync('addressLists');
            if (addressLists) {
                this.addressData = addressLists;
                this.processTree(addressLists);
                console.log('addressColumns', this.addressColumns);
            }
        },

        // 处理地址数据树结构
        processTree(nodes, level = 0) {
            nodes.forEach(node => {
                // 根据层级存储到不同的数组
                if (level === 0) {
                    this.addressColumns[0].push({
                        value: node.code,
                        label: node.name
                    });
                } else if (level === 1) {
                    this.addressColumns[1].push({
                        value: node.code,
                        label: node.name,
                        parentId: node.pid
                    });
                } else if (level === 2) {
                    this.addressColumns[2].push({
                        value: node.code,
                        label: node.name,
                        parentId: node.pid
                    });
                }

                // 如果有子节点，递归处理
                if (node.children && node.children.length > 0) {
                    this.processTree(node.children, level + 1);
                }
            });
        },

        // 地址选择器变化事件
        changeAddress(e) {
            const {
                columnIndex,
                index,
                indexs,
                picker = this.$refs.uPicker
            } = e
            if (columnIndex === 0) {
                let children1 = this.addressData[index].children.map(e => { return { label: e.name, value: e.code } })
                picker.setColumnValues(1, children1)
                let children2 = this.addressData[index].children[indexs[1]].children.map(e => { return { label: e.name, value: e.code } })
                picker.setColumnValues(2, children2)
            }
            if (columnIndex === 1) {
                let children3 = this.addressData[indexs[0]].children[indexs[1]].children.map(e => { return { label: e.name, value: e.code } })
                picker.setColumnValues(2, children3)
            }
        },

        // 确认地址选择
        confirmAddress(e) {
            console.log('选择地址：', e);
            const values = e.value;
            this.addressForm.region = values.map(e => e.label).join(' ');
            this.showAddressPicker = false;
            this.addressObj = {
                ...this.addressObj,
                province: values[0].label,
                provinceCode: values[0].value,
                city: values[1].label,
                cityCode: values[1].value,
                district: values[2].label,
                districtCode: values[2].value
            }
        },

        // 验证地址表单
        validateAddressForm() {
            const { name, phone, region, detail } = this.addressForm;

            if (!name.trim()) {
                uni.showToast({
                    title: '请输入收货人姓名',
                    icon: 'none'
                });
                return false;
            }

            if (!phone.trim()) {
                uni.showToast({
                    title: '请输入手机号码',
                    icon: 'none'
                });
                return false;
            }

            // 简单的手机号验证
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(phone)) {
                uni.showToast({
                    title: '请输入正确的手机号码',
                    icon: 'none'
                });
                return false;
            }

            if (!region.trim()) {
                uni.showToast({
                    title: '请选择所在地区',
                    icon: 'none'
                });
                return false;
            }

            if (!detail.trim()) {
                uni.showToast({
                    title: '请输入详细地址',
                    icon: 'none'
                });
                return false;
            }

            return true;
        },

        // 构建支付参数
        buildPaymentParams() {
            // 构建完整地址字符串
            const fullAddress = `${this.addressForm.region} ${this.addressForm.detail}`;

            // 构建地址参数（用于保存地址）
            const addressParams = {
                ...this.addressObj,
                userId: this.weixinInfo.id,
                receiverName: this.addressForm.name,
                receiverPhone: this.addressForm.phone,
                detailAddress: this.addressForm.detail
            };

            // 构建支付参数
            const paymentParams = {
                // 多商品情况下可能需要的参数
                goodsItemList: this.orderItems.map(item => ({
                    goodsId: item.goodsId,
                    num: item.quantity,
                })),
                receiverName: this.addressForm.name,
                receiverPhone: this.addressForm.phone,
                address: fullAddress
            };

            return {
                addressParams,
                paymentParams
            };
        },

        // 微信支付方法
        async wxPay() {
            try {
                // 构建支付参数
                const { addressParams, paymentParams } = this.buildPaymentParams();

                // 保存地址信息（如果有用户ID）
                if (this.weixinInfo.id && addressParams.userId) {
                    try {
                        const addressRes = await addAddress(addressParams);
                        console.log('地址保存结果：', addressRes);
                    } catch (error) {
                        console.warn('地址保存失败：', error);
                        // 地址保存失败不影响支付流程
                    }
                }

                // 设置支付loading状态
                this.payLoading = true;

                console.log('调用支付接口，参数：', paymentParams);

                // 调用支付接口
                const payData = await weixinPay(paymentParams);

                console.log('支付接口响应：', payData);

                if (payData.code === 500) {
                    uni.showToast({
                        title: payData.msg || '支付失败',
                        icon: 'none'
                    });
                    return;
                }

                // 调用微信支付
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: payData.timeStamp,
                    nonceStr: payData.nonceStr,
                    package: payData.packageValue,
                    paySign: payData.paySign,
                    signType: payData.signType,
                    success: (res) => {
                        console.log('支付成功：', res);
                        if (res.errMsg === 'requestPayment:ok') {
                            // 支付成功，跳转到支付成功页面
                            uni.navigateTo({
                                url: `/pages/paySuccess/paySuccess?orderSpecial=${payData.orderSpecial || ''}`
                            });
                        }
                    },
                    fail: (error) => {
                        console.log('支付失败：', error);
                        uni.showToast({
                            title: '取消支付',
                            icon: 'none'
                        });
                    }
                });

            } catch (error) {
                console.error('支付异常：', error);

                let errorMessage = '支付异常，请稍后重试';
                if (error.message) {
                    errorMessage = error.message;
                } else if (error.data && error.data.message) {
                    errorMessage = error.data.message;
                }

                uni.showToast({
                    title: errorMessage,
                    icon: 'none'
                });

            } finally {
                // 移除支付loading状态
                this.payLoading = false;
            }
        },

        // 提交订单（立即支付）
        submitOrder() {
            // 防止重复提交
            if (this.payLoading) {
                console.log('支付进行中，跳过重复请求');
                return;
            }

            // 验证地址表单
            if (!this.validateAddressForm()) {
                return;
            }

            if (this.orderItems.length === 0) {
                uni.showToast({
                    title: '订单商品不能为空',
                    icon: 'none'
                });
                return;
            }

            // 调用微信支付
            this.wxPay();
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep {

    .u-input,
    .u-textarea {
        font-size: 20rpx !important;
        padding: 16rpx 20rpx !important;
        background-color: #FAFAFA !important;
    }
}


.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: linear-gradient(to bottom,
            #e3c997,
            #ebd7b1,
            #f7edda,
            #fbf5ea,
            #fbf6ed,
            #faf7f5);
    overflow: hidden;
}

.order-content {
    flex: 1;
    padding: 25rpx;
    overflow-y: auto;
}

// 通用区域样式
.section-title {
    padding: 20rpx 40rpx;
    font-size: 28rpx;
    font-weight: bold;
    color: #222;
    border-bottom: 1rpx solid #f5f5f5;
}

// 收货地址区域 
.address-content {
    margin-bottom: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;

    .address-form {
        padding: 30rpx 40rpx;
    }

    .form-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 30rpx;

        &:last-child {
            margin-bottom: 0;
        }

        .form-label {
            font-size: 28rpx;
            color: #222;
            width: 160rpx;
            flex-shrink: 0;
            line-height: 60rpx;
        }

        .region-selector {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #FAFAFA;
            padding: 16rpx 20rpx;
            border-radius: 8rpx;

            .region-text {
                font-size: 30rpx;
                color: #222;

                &.placeholder {
                    color: #CACBCC;
                }
            }
        }
    }
}


// 商品列表区域
.goods-section {
    .goods-list {
        background-color: #fff;
        border-radius: 20rpx;
        overflow: hidden;
    }
}

// 底部操作栏
.bottom-bar {
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40rpx 30rpx;

    .total-section {
        .total-info {
            display: flex;
            align-items: baseline;

            .total-label {
                font-size: 24rpx;
                font-weight: bold;
                color: #000;
            }

            .total-price {
                font-size: 38rpx;
                font-weight: bold;
                color: #E5B05E;
                margin-right: 20rpx;

                .price-icon {
                    font-size: 27rpx;
                    margin-right: 6rpx;
                }
            }

            .shipping-fee {
                font-size: 21rpx;
                color: #3D3D3D;
            }
        }
    }

    .submit-btn {
        width: 200rpx;
        height: 72rpx;
        line-height: 72rpx;
        background-color: #E5B05E;
        border-radius: 50rpx;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;

        &.loading {
            background-color: #ccc;
        }

        .submit-text {
            font-size: 26rpx;
            color: #fff;
            font-weight: bold;
            margin-left: 8rpx;
        }
    }
}
</style>
