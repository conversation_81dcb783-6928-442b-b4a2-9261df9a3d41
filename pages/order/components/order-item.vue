<template>
    <view class="order-item">
        <!-- 商品信息 -->
        <view class="product-info">
            <!-- 商品图片 -->
            <view class="product-image">
                <u-image :src="item.goodsImage" width="160rpx" height="160rpx" border-radius="8rpx"></u-image>
            </view>

            <!-- 商品详情 -->
            <view class="product-details">
                <view class="product-name u-line-2">{{ item.goodsName }}</view>

                <!-- 价格和数量信息 -->
                <view class="price-quantity">
                    <!-- 价格信息 -->
                    <view class="price-section">
                        <view class="original-price" v-if="item.originalPrice && item.originalPrice > item.price">
                            <text class="price-icon">¥</text>{{ item.originalPrice }}
                        </view>
                        <view class="current-price">
                            <text class="price-icon">¥</text>{{ item.price }}
                        </view>
                    </view>

                    <!-- 数量信息 -->
                    <view class="quantity-info">
                        <text class="quantity-text">x{{ item.quantity }}</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'OrderItem',
    props: {
        item: {
            type: Object,
            required: true,
            default: () => ({})
        }
    }
}
</script>

<style lang="scss" scoped>
.order-item {
    margin: 20rpx 0;
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #f5f5f5;
    background-color: #fff;
    border-radius: 16rpx;

    &:last-child {
        border-bottom: none;
    }

    .product-info {
        display: flex;

        .product-image {
            margin-right: 20rpx;
            border-radius: 8rpx;
            overflow: hidden;
        }

        .product-details {
            flex: 1;

            .product-name {
                font-size: 24rpx;
                font-weight: bold;
                color: #222;
                margin-bottom: 20rpx;
                line-height: 35rpx;
            }

            .price-quantity {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;

                .price-section {
                    .original-price {
                        font-size: 20rpx;
                        color: #929292;
                        text-decoration: line-through;
                        margin-bottom: 5rpx;

                        .price-icon {
                            font-size: 16rpx;
                            margin-right: 2rpx;
                        }
                    }

                    .current-price {
                        font-size: 28rpx;
                        color: #E5B05E;
                        font-weight: bold;

                        .price-icon {
                            font-size: 20rpx;
                            margin-right: 2rpx;
                        }
                    }
                }

                .quantity-info {
                    .quantity-text {
                        font-size: 24rpx;
                        color: #666;
                    }
                }
            }
        }
    }
}
</style>
