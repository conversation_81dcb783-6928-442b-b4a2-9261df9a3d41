<template>
  <view class="bottom-ads">
    <view class="ad-container">
      <image class="ad-image" src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/btm_left.png" mode="widthFix" @click="handleClick(0)"></image>
      <image class="ad-image" src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/btn_right.png" mode="widthFix" @click="handleClick(1)"></image>
    </view>
  </view>
</template>

<script>
export default {
  name: 'BottomAds',
  data() {
    return {
      adLinks: [
        { type: 'product', id: '1001' }, // 左侧广告
        { type: 'service', id: '2001' }  // 右侧广告
      ]
    }
  },
  methods: {
    handleClick(index) {
      this.$emit('adClick', this.adLinks[index]);
    }
  }
}
</script>

<style lang="scss" scoped>
.bottom-ads {
  padding: 0 30rpx;
  margin-bottom: 40rpx;
  
  .ad-container {
    display: flex;
    justify-content: space-between;
    gap: 10rpx;
    
    .ad-image {
      width: 340rpx;
      height: 240rpx;
    }
  }
}
</style> 