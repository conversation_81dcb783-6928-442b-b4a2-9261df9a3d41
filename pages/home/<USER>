<template>
  <view class="home">
    <view class="header">
      <view class="title">爱美颐年</view>
    </view>

    <view style="padding: 20rpx 30rpx">
      <FakeSearchBox placeholder="搜索商品-好货等你" url="/pages/shop/index" />
    </view>
    <view class="swiper-container">
      <swiper
        class="swiper"
        :indicator-dots="true"
        :autoplay="true"
        :interval="3000"
        :duration="500"
        :circular="true"
        indicator-active-color="#FFFFFF"
        indicator-color="rgba(255,255,255,0.5)"
      >
        <swiper-item
          v-for="(item, index) in swiperList"
          :key="index"
          @click="handleSwiper(index)"
        >
          <image :src="item" mode="aspectFill" class="swiper-image"></image>
        </swiper-item>
      </swiper>
    </view>

    <FunctionButtons @buttonClick="goToFunction" />

    <IconFunctions @iconClick="goToFunction" />

    <BottomAds @adClick="handleAdClick" />
  </view>
</template>

<script>
import ProductCard from "./components/ProductCard.vue";
import ButtonAction from "./components/ButtonAction.vue";
import FunctionButtons from "./components/FunctionButtons.vue";
import IconFunctions from "./components/IconFunctions.vue";
import BottomAds from "./components/BottomAds.vue";
import SearchBox from "./components/SearchBox.vue";
// import ZPMixin from "z-paging/components/z-paging/js/z-paging-mixin";
import { getPackageList } from "@/api/home";
import { getSwiper } from "@/common/home.js";
import FakeSearchBox from "@/pages/user/components/FakeSearchBox.vue";
import { question } from "@/common/config";
import { getQuestion } from "@/api/question.js";
export default {
  // mixins: [ZPMixin],
  components: {
    ProductCard,
    ButtonAction,
    FunctionButtons,
    IconFunctions,
    BottomAds,
    SearchBox,
    FakeSearchBox,
  },
  data() {
    return {
      current: 0,
      swiperList: [],
      list: [],
      products: [],
      baseUrl: process.env.VUE_APP_STATIC_FILE_URL,
      searchKeyword: "",
    };
  },
  onReady() {
    getSwiper().then((res) => {
      this.swiperList = res.data.map((item) => item.imageUrl);
      this.list = res.data;
    });
  },
  methods: {
    refresh() {
      getSwiper().then((res) => {
        this.swiperList = res.data.map((item) => item.imageUrl);
        this.list = res.data;
      });
    },
    handleSwiper(e) {
      const { goodsId } = this.list[e];
      if (!goodsId) return;
      uni.$u.route({
        type: "navigateTo",
        url: "/pages/prdDetail/index",
        params: {
          id: goodsId,
        },
      });
    },
    goAll() {
      uni.$u.route({
        type: "switchTab",
        url: "/pages/server/index",
      });
    },
    queryList(pageNo, pageSize) {
      const params = {
        pageNum: pageNo,
        pageSize: pageSize,
        isDisplay: "1",
        reasonable: false,
        // type: this.tabIndex + 1
      };
      getPackageList(params)
        .then((res) => {
          // 将请求的结果数组传递给z-paging
          this.$refs.paging.complete(res.rows);
        })
        .catch((res) => {
          // 如果请求失败写this.$refs.paging.complete(false);
          // 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
          // 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
          this.$refs.paging.complete(false);
        });
    },
    itemClick(item) {
      uni.$u.route({
        type: "navigateTo",
        url: "/pages/prdDetail/index",
        params: {
          id: item.id,
        },
      });
    },
    handleHealthManage() {
      getQuestion(question.singleId).then((res) => {
        if (res.code === 200) {
          if (res.data.isCommit) {
            uni.$u.route({
              url: "pagesA/healthManage/index",
              params: {
                sourcePage: "pages/home/<USER>",
              },
            });
          } else {
            uni.$u.route({
              url: "pages/question/sinple",
              params: {
                sourcePage: "pages/home/<USER>",
                questionId: question.singleId,
              },
            });
          }
        }
      });
    },
    goToFunction(type) {
      console.log("前往功能:", type);
      // 根据类型跳转到不同页面
      switch (type) {
        case "jcjh":
          // 检测激活
          uni.$u.route({
            type: "switchTab",
            url: "/pages/detectActivation/index",
          });
          break;
        case "bgcx":
          // 报告查询
          uni.$u.route({
            type: "navigateTo",
            url: "/pages/reportListFather/index",
          });
          break;
        case "jkdk":
          // 健康打卡
          this.handleHealthManage();
          break;
        case "dxgl":
          // 代谢管理
          break;
        case "xtgl":
          // 血糖管理
          break;
        case "cdgl":
          // 肠道管理
          break;
        case "jqyz":
          // 菌群移植
          break;
        default:
          break;
      }
    },
    handleAdClick(adInfo) {
      return;
      // 根据广告信息跳转到相应页面
      if (adInfo.type === "product") {
        // 产品广告跳转
        uni.$u.route({
          type: "navigateTo",
          url: "/pages/prdDetail/index",
          params: {
            id: adInfo.id,
          },
        });
      } else if (adInfo.type === "service") {
        // 服务广告跳转
        uni.$u.route({
          type: "navigateTo",
          url: "/pages/service/index",
          params: {
            id: adInfo.id,
          },
        });
      }
    },
    goToMall() {
      uni.switchTab({
        url: "/pages/shop/index",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.home {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f0d7;
  background: linear-gradient(
    to bottom,
    #e3c997,
    #ebd7b1,
    #f7edda,
    #fbf5ea,
    #fbf6ed,
    #faf7f5
  );
}

.header {
  position: relative;
  padding-top: 105rpx;
  padding-bottom: 20rpx;

  .title {
    margin-left: 45rpx;
    color: #595757;
    font-weight: 600;
    font-size: 36rpx;
  }
}

.search-box {
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}

.swiper-container {
  padding: 0 30rpx;
  margin-bottom: 40rpx;

  .swiper {
    height: 300rpx;
    border-radius: 20rpx;
    overflow: hidden;

    .swiper-image {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
