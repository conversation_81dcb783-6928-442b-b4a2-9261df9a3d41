<template>
  <view class="icon-functions">
    <view class="icon-function-item" @click="handleClick('dxgl')">
      <view class="icon-container bg-dxgl">
        <image class="icon-image" src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/dxgl.png" mode="aspectFit"></image>
      </view>
      <text class="icon-text">健康管理</text>
    </view>
    <view class="icon-function-item" @click="handleClick('xtgl')">
      <view class="icon-container bg-xtgl">
        <image class="icon-image" src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/xtgl.png" mode="aspectFit"></image>
      </view>
      <text class="icon-text">菌群移植</text>
    </view>
    <view class="icon-function-item" @click="handleClick('cdgl')">
      <view class="icon-container bg-cdgl">
        <image class="icon-image" src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/cdgl.png" mode="aspectFit"></image>
      </view>
      <text class="icon-text">益生菌</text>
    </view>
    <view class="icon-function-item" @click="handleClick('jqyz')">
      <view class="icon-container bg-jqyz">
        <image class="icon-image" src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/jqyz.png" mode="aspectFit"></image>
      </view>
      <text class="icon-text">健康周边</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'IconFunctions',
  methods: {
    handleClick(type) {
      this.$emit('iconClick', type);
    }
  }
}
</script>

<style lang="scss" scoped>
.icon-functions {
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
  
  .icon-function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    .bg-dxgl {
        background-color: #F9E6CF;
    }
    .bg-xtgl {
        background-color: #F9E6CF;
    }
    .bg-cdgl {
        background-color: #E0E6E2;
    }
    .bg-jqyz {
        background-color: #DBDFE2;
    }
    
    .icon-container {
      width: 140rpx;
      height: 140rpx;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 15rpx;
      
      .icon-image {
        width: 75rpx;
        height: 75rpx;
      }
    }
    
    .icon-text {
      font-size: 28rpx;
      color: #333333;
    }
  }
}
</style> 