<template>
	<view class="product-card">

		<view :style="{position:'relative'}">
			<prd-label :label="product.tagName"></prd-label>
			<!-- height="328rpx" -->
			<u-image :src="product.image" width="100%" height="260rpx" mode="aspectFit"></u-image>
		</view>
		<view class="product-info">
			<view class="price">
				<view class="new">￥{{ product.price }}</view>
				<view class="old" v-if="product.originalPrice">￥{{ product.originalPrice }}</view>
			</view>
			<u-text :text="product.name" size="28rpx" :lines="1" margin-bottom="10rpx"></u-text>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'ProductCard',
		props: {
			product: {
				type: Object,
				required: true,
				default: () => ({}),
			}
		}
	}
</script>

<style lang="scss" scoped>
	.product-card {
		background-color: #ffffff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
		// margin-bottom: 20rpx;

		.product-info {
			padding: 16rpx;

			.price {
				display: flex;
				align-items: center;
				margin-bottom: 10rpx;

				.new {
					color: #AC413E;
					font-size: 32rpx;
					font-weight: 600;
					margin-right: 16rpx;
				}

				.old {
					color: #DBDBDB;
					font-size: 24rpx;
					font-weight: 400;
					text-decoration: line-through
				}

				// justify-content: space-between;
			}

		}
	}
</style>