<template>
    <view class="button-action">
      <view class="button-action-title">
        <view class="title">检测激活</view>
        <view class="sub-title">快速检测激活</view>
      </view>
      <u--image :showLoading="true" src="https://cdn.uviewui.com/uview/album/1.jpg" width="60px" height="60px" radius="10" @click="click"></u--image>
    </view>
  </template>
  
  <script>
  export default {
    name: 'ButtonAction',
    props: {
      title: {
        type: String,
      },

    }
  }
  </script>
  
  <style lang="scss" scoped>
.button-action {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border-radius: 20rpx;
    padding: 16rpx;
    .title {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
    }
}
  </style>