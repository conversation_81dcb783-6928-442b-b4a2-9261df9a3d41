<template>
  <view class="function-buttons">
    <view class="function-wrap">
        <view class="function-button green" @click="handleClick('jcjh')">
          <text class="button-text">检测激活</text>
          <image
            class="button-icon"
            src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/jcjh.png"
            mode="aspectFit"
          ></image>
        </view>
    </view>
    <view class="function-wrap">
      <get-phone @onSuccess="handleClick('bgcx')">
        <view class="function-button blue" @click="handleClick('bgcx')">
          <text class="button-text">报告查询</text>
          <image
            class="button-icon"
            src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/bgcx.png"
            mode="aspectFit"
          ></image>
        </view>
      </get-phone>
    </view>
    <view class="function-wrap">
      <get-phone @onSuccess="handleClick('jkdk')">
        <view class="function-button yellow" @click="handleClick('jkdk')">
          <text class="button-text">健康打卡</text>
          <image
            class="button-icon"
            src="https://millenia.oss-cn-wuhan-lr.aliyuncs.com/minimall/tinified/jkdk.png"
            mode="aspectFit"
          ></image>
        </view>
      </get-phone>
    </view>
  </view>
</template>

<script>
export default {
  name: "FunctionButtons",
  data() {
    return {
      baseUrl: process.env.VUE_APP_STATIC_FILE_URL,
    };
  },
  methods: {
    handleClick(type) {
      this.$emit("buttonClick", type);
    },
  },
};
</script>

<style lang="scss" scoped>
.function-buttons {
  display: flex;
  gap: 20rpx;
  justify-content: space-between;
  padding: 0 30rpx;
  margin-bottom: 40rpx;

  .function-wrap {
    flex: 1;
    .function-button {
      width: 100%;
      height: 270rpx;
      border-radius: 20rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx;
      box-sizing: border-box;

      &.green {
        background: linear-gradient(to bottom, #57a17e, #66ac8b, #84c1a5, #a6dac3);
      }

      &.blue {
        background: linear-gradient(to bottom, #55779b, #6589ad, #80a9cd, #9ccaed);
      }

      &.yellow {
        background: linear-gradient(to bottom, #d8ab6a, #deb77d, #ebcea4, #f3debe);
      }

      .button-icon {
        margin-top: 15rpx;
        width: 100%;
        height: 270rpx;
      }

      .button-text {
        font-size: 36rpx;
        color: #ffffff;
        font-weight: 500;
      }
    }
  }
}
</style>
