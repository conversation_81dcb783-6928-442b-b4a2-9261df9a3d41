<template>
	<view class="search-box">
		<u-search
			v-model="searchValue"
			:show-action="false"
			:placeholder="placeholder"
			:clearabled="true"
			:height="40"
			@search="onSearch"
			@custom="onSearch"
            bgColor="#fff"
		></u-search>
	</view>
</template>

<script>
export default {
	name: 'SearchBox',
	props: {
		placeholder: {
			type: String,
			default: '搜索商品-好货等你'
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			searchValue: this.value
		}
	},
	watch: {
		value(newVal) {
			this.searchValue = newVal
		},
		searchValue(newVal) {
			this.$emit('input', newVal)
		}
	},
	methods: {
		onSearch() {
			this.$emit('search', this.searchValue)
		}
	}
}
</script>

<style lang="scss">
.search-box {
	padding: 20rpx 30rpx;
	margin-bottom: 20rpx;
   
}
</style> 