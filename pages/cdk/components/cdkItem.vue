<template>
	<view class="order_item">
		<view class="header_wrap">
			<view class="orderNum u-line-1">
				订单号{{ item.orderNumber }}
			</view>
			<view class="status" :style="{color:statusDetail.color}">
				{{statusDetail.label}}
			</view>
		</view>
		<u-line></u-line>
		<view class="item_content">
			<u--image class="icon_img" radius="16rpx" width="168rpx" height="130rpx" :showLoading="true"
				:src="item.image"></u--image>
			<view class="left_wrap">
				<view class="title">{{ item.goodsName }}</view>
				<view class="time">{{ item.createTime }}</view>
				<view class="card_no">
					卡密编号：{{item.accountNumber}}
				</view>
			</view>
		</view>
		<u-button v-if="item.status === '1'"  color="#117ACD" :customStyle="payStyles"
			@click="handleGetCode">
			获取卡密</u-button>
	</view>
</template>

<script>
	import {
		useStatusEnums
	} from '@/util/enums.js';
	import { getCard } from '@/common/home.js'

	export default {
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {

			}
		},
		computed: {
			statusDetail() {
				let item = useStatusEnums.find(row => row.value === this.item.status);
				return item || useStatusEnum[0]
			},
			payStyles() {
				let styles = {
					width: '100%',
					height: '98rpx',
					borderRadius: '14rpx',
					fontWeight: '500',
					fontSize: '30rpx',
					color: '#FFFFFF'
				}
				return styles
			},

		},
		methods: {
			handleGetCode() {
				getCard({ account: this.item.accountNumber}).then(res => {
					if (res.code === 200) {
						uni.showToast({
							title: res.msg,
							icon: 'none',
						})
					}
				})
			}
		},
	}
</script>

<style lang="scss" scoped>
	.order_item {
		background: #fff;
		margin-top: 20rpx;
		padding: 0 20rpx 30rpx 20rpx;
		border-radius: 16rpx;
		overflow: hidden;

		.header_wrap {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 28rpx 0;

			.orderNum {
				font-weight: 500;
				font-size: 30rpx;
				color: #142635;
				flex: 1;
				padding-right: 15rpx;
			}

			.status {
				font-weight: 500;
				font-size: 30rpx;
			}
		}


		.item_content {
			padding: 30rpx 0;
			display: flex;

			.left_wrap {
				padding-left: 20rpx;
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-around;

				.title {
					font-weight: 500;
					font-size: 26rpx;
					color: #333333;
				}

				.time {
					font-weight: 500;
					font-size: 26rpx;
					color: rgba(51, 51, 51, 0.6);
				}

				.card_no {
					font-weight: 600;
					font-size: 28rpx;
					color: #C9C9C9;
				}
			}


		}

		.footer_wrap {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx 0;

			.money_wrap {
				font-weight: bold;
				font-size: 20rpx;
				color: #FD3232;
				display: flex;
				align-items: baseline;

				.unit {
					font-size: 20rpx;
				}

				.money {
					font-size: 36rpx;
				}
			}

			.action_wrap {
				display: flex;
				align-items: center;
			}
		}


	}
</style>