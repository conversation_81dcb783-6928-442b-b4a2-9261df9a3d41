<template>
	<u-loading-page iconSize="60" :loading="true" fontSize="18" color="#fff" bgColor="rgba(0,0,0,0.3)"
		:image="`${baseUrl}/common/logo.png`" loadingText="正在获取用户信息..."></u-loading-page>
</template>

<script>
	import {
		mapActions
	} from 'vuex';
	export default {
		data(){
			return: {
				baseUrl: process.env.VUE_APP_STATIC_FILE_URL
			}
		},
		onShow() {
			// this.getInfo()
		},
		methods: {
			// ...mapActions(['login']),
			// getInfo() {
			// 	this.loading = true;
			// 	this.login().then(res => {
			// 		uni.$u.route('/pages/buyVip/buyVip');
			// 	})
			// }
		},
	}
</script>

<style lang="scss" scoped>

</style>