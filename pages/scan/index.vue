<template>
	<view class="container">
		<page-nav title="扫码激活" navigatorType="navigateBack" :border="false"></page-nav>
		<view class="content">
			<u--input v-model="form.witqdQrCode" :customStyle="scanStyle" border="none" fontSize="35rpx"
				inputAlign="center" placeholder="请输入样本编码" prefixIconStyle="font-size: 22px;color: #909399"></u--input>
			<u-button color="#117ACD" :customStyle="{...btnStyles,marginBottom:'68rpx'}" @click="handleScan">
				扫码识别
			</u-button>
			<u--form :model="form" ref="uForm" :rules="rules" labelPosition="top" label-width="100%" :labelStyle="{fontSize: '30rpx',fontWeight: '600',
color: '#333333'}">
				<u-form-item label="1、检测前5天是否吃药" prop="takeMedicine">
					<u-radio-group v-model="form.takeMedicine" iconPlacement="right" placement="column">
						<u-radio v-for="item in yesNoEnums" :key="item.value" :name="item.value"
							:label="`${item.prefix}${item.label}`"
							:customStyle="{width: '100%',padding:'30rpx 0',color:''}" activeColor="#117ACD"></u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item v-if="form.takeMedicine === '02'" label-width="0" prop="drugName">
					<u--textarea v-model="form.drugName" :customStyle="textareaStyle" border="none"
						placeholder="请输入用药情况" count></u--textarea>
				</u-form-item>
				<u-form-item label="2、是否有疾病史" prop="medicalHistory">
					<u-radio-group v-model="form.medicalHistory" iconPlacement="right" placement="column">
						<u-radio v-for="item in yesNoEnums" :key="item.value" :name="item.value"
							:label="`${item.prefix}${item.label}`"
							:customStyle="{width: '100%',padding:'30rpx 0',color:''}" activeColor="#117ACD"></u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item v-if="form.medicalHistory === '02'" label-width="0" prop="diseaseName">
					<u--textarea v-model="form.diseaseName" :customStyle="textareaStyle" border="none"
						placeholder="请输入疾病情况" count></u--textarea>
				</u-form-item>
			</u--form>
			<view class="footer_wrap">
				<u-button color="#117ACD" :loading="submitLoading" :customStyle="btnStyles" @click="handleActive">
					立即激活
				</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		yesNoEnums
	} from '@/util/enums.js'
	import {
		detectionActivation
	} from '@/api/activation.js'
	export default {
		components: {},
		data() {
			return {
				submitLoading: false,
				yesNoEnums,
				form: {
					witqdQrCode: '',
					takeMedicine: '',
					medicalHistory: '',
					diseaseName: '',
					drugName: '',
				},
				rules: {
					takeMedicine: {
						type: 'string',
						required: true,
						message: '请选择',
						trigger: ['blur', 'change']
					},
					medicalHistory: {
						type: 'string',
						required: true,
						message: '请选择',
						trigger: ['blur', 'change']
					},
					drugName: {
						type: 'string',
						required: true,
						message: '请输入用药情况',
						trigger: ['blur', 'change']
					},
					diseaseName: {
						type: 'string',
						required: true,
						message: '请输入疾病情况',
						trigger: ['blur', 'change']
					},
				}
			}
		},
		onReady() {
			//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
			this.$refs.uForm.setRules(this.rules)
		},
		onLoad(options) {
			console.log(decodeURIComponent(options.baseInfo));
			this.baseInfo = JSON.parse(decodeURIComponent(options.baseInfo));
			console.log('this.baseInfo', this.baseInfo);
		},
		computed: {
			scanStyle() {
				return {
					height: '84rpx',
					background: 'rgba(74,154,216,0.1)',
					borderRadius: '6rpx',
					marginBottom: '30rpx'
				}
			},
			btnStyles() {
				return {
					height: '98rpx',
					fontWeight: '600',
					fontSize: '36rpx',
					color: '#FFFFFF',
					borderRadius: '14rpx'
				}
			},
			textareaStyle() {
				return {
					background: '#F3F7FA',
				}
			}
		},
		methods: {
			handleScan() {
				// 调起条码扫描
				uni.scanCode({
					scanType: ['qrCode'],
					success: (res) => {
						this.form.witqdQrCode = res.result;
					}
				});
			},
			handleActive() {
				console.log(this.form);
				this.$refs.uForm.validate().then(res => {
					let {
						form
					} = this;
					if (!form.witqdQrCode) {
						uni.$u.toast('样本编码不能为空！')
					}
					let params = {
						...this.baseInfo,
						...form
					}
					this.submitLoading = true
					detectionActivation(params).then(res => {
						this.submitLoading = false
						if (res.code == 200) {
							uni.$u.toast('激活成功')
							setTimeout(() => {
								uni.$u.route({
									type: 'switchTab',
									url: "pages/detectActivation/index"
								});
							}, 500)

						} else {
							uni.$u.toast(res.msg || '保存用户信息失败')
						}
					})

				}).catch(errors => {
					uni.$u.toast('请完善表单信息')
				})
			}
		},
	}
</script>
<style>
	.u-form-item__body {
		padding: 20rpx 30rpx !important;
	}
</style>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		padding-bottom: 180rpx;
		box-sizing: border-box;

		.content {
			padding: 78rpx 30rpx 0 30rpx;

			.footer_wrap {
				box-sizing: border-box;
				position: fixed;
				left: 0;
				bottom: 70rpx;
				width: 100%;
				padding: 0 20rpx;
			}
		}
	}
</style>