<template>
	<view class="order_item">
		<view class="header_wrap">
			<view class="orderNum">
				订单号{{ item.orderNumber }}
			</view>
			<view class="status" :style="{color:statusDetail.color}">
				{{statusDetail.label}}
			</view>
		</view>
		<u-line></u-line>
		<view class="item_content">

			<view class="left_wrap">
				<view class="title">下单人：{{item.userName }}</view>
				<view class="time">下单时间：{{ item.createTime }}</view>

			</view>
		</view>
		<u-line></u-line>
		<view class="footer_wrap">
			<view class="money_wrap">
				<view>分佣金额：</view>
				<view class="unit">￥</view>
				<view class="money">{{ item.brokerage }}</view>
			</view>
			<view class="price_con">
				<view class="">
					订单金额：
				</view>
				<view class="price">
					<view class="price_icon">￥</view>
					<view class="amount">{{ item.orderAmount }}</view>
				</view>
				<!-- <view class="count">
					x{{ item.num }}
				</view> -->
			</view>

		</view>
	</view>
</template>

<script>
	import {
		orderStatusEnums
	} from '@/util/enums.js';

	export default {
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {

			}
		},
		computed: {
			statusDetail() {
				let item = orderStatusEnums.find(row => row.value === this.item.status);
				return item || orderStatusEnums[0]
			},
			btnStyles() {
				let styles = {
					height: 'auto',
					padding: '12rpx 20rpx',
					fontWeight: '400',
					fontSize: '28rpx',
					color: '#666666',
				}
				return styles;
			},
		},
		methods: {
			handleDetail() {
				console.log('click');
				uni.$u.route('/pages/orderDetail/orderDetail', {
					orderId: this.item.orderId
				});

			},
			handleCancel() {
				this.$emit('onCancel')
			},
		},
	}
</script>

<style lang="scss" scoped>
	.order_item {
		background: #fff;
		margin-top: 20rpx;
		padding: 0 20rpx;
		border-radius: 16rpx;
		overflow: hidden;

		.header_wrap {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 28rpx 0;

			.orderNum {
				font-weight: 500;
				font-size: 30rpx;
				color: #142635;
			}

			.status {
				font-weight: 500;
				font-size: 30rpx;
			}
		}


		.item_content {
			padding: 30rpx 0;
			display: flex;

			.left_wrap {
				// padding-left: 20rpx;
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-around;
				color: #333333;

				.title {
					font-weight: 500;
					font-size: 26rpx;
					margin-bottom: 20rpx;
				}

				.time {
					font-weight: 500;
					font-size: 26rpx;
					// color: rgba(51, 51, 51, 0.6);
					margin-bottom: 10rpx;
				}


			}


		}

		.footer_wrap {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx 0;

			.price_con {
				display: flex;

				align-items: center;
				// justify-content: space-between;

				.price {
					display: flex;
					align-items: baseline;

					.price_icon {
						font-weight: bold;
						font-size: 20rpx;
						color: #FD3232;
					}

					.amount {
						font-weight: bold;
						font-size: 36rpx;
						color: #FD3232;
					}
				}

				.count {
					font-weight: bold;
					font-size: 36rpx;
					color: #333333;
				}
			}

			.money_wrap {
				font-weight: bold;
				// font-size: 20rpx;
				color: #FD3232;
				display: flex;
				align-items: baseline;

				.unit {
					font-size: 20rpx;
				}

				.money {
					font-size: 36rpx;
				}
			}

			.action_wrap {
				display: flex;
				align-items: center;
			}
		}


	}
</style>