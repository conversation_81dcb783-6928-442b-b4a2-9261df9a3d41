<!-- 普通模式演示(vue) -->
<template>
	<view class="content">
		<view class="header">
			<view class="title">健康周边</view>
		</view>

		<SearchBox v-model="searchKeyword" @search="handleSearch" />
		<view class="main-container">
			<scroll-view scroll-y class="category-list">
				<u-collapse :value="activeCollapse" @change="classifyChange">
					<u-collapse-item v-for="(item, index) in tabList" :key="index" :name="index" :duration="0"
						:title="item.name" :border="false" :is-link="item.children && item.children.length > 0">
						<view v-if="!item.children || item.children.length === 0" class="empty-content"
							@click.stop="tabsChange(item)">
						</view>
						<view v-else class="sub-category-list">
							<view v-for="(subItem, subIndex) in item.children" :key="subIndex" class="sub-category-item"
								:class="{ active: tabObj.id === subItem.id }" @click.stop="tabsChange(subItem)">
								<text class="sub-category-text">{{ subItem.name }}</text>
								<block v-if="isDraw&&activeCollapse.includes(index)&&subItem.id === tabObj.id">
									<view class="sub-category-suffix-top">
										<InnerRadius :key="`${subItem.id}-top`" :canvasId="`canvas-top-${subItem.id}`"
											direction="left" :triangleWidth="20" :triangleHeight="20" :radius="22"
											fillColor="white" />
									</view>
									<view class="sub-category-suffix-btm">
										<InnerRadius :key="`${subItem.id}-bottom`"
											:canvasId="`canvas-bottom-${subItem.id}`" direction="bottom"
											:triangleWidth="20" :triangleHeight="20" :radius="22" fillColor="white" />
									</view>
								</block>
							</view>
						</view>
					</u-collapse-item>
				</u-collapse>
			</scroll-view>

			<view class="product-list-container">
				<z-paging ref="paging" v-model="dataList" @query="queryList" :fixed="false">
					<view class="product-list">
						<view class="product-grid">
							<view v-for="(item, index) in dataList" :key="index" class="product-item"
								@click="itemClick(item, index)">
								<ProductItem :product="item"></ProductItem>
							</view>
						</view>
					</view>
				</z-paging>
			</view>
		</view>
	</view>
</template>

<script>
	import ZPMixin from "@/uni_modules/z-paging/components/z-paging/js/z-paging-mixin";
	import ProductItem from "../home/<USER>/ProductCard.vue";
	import SearchBox from "../home/<USER>/SearchBox.vue";
	import InnerRadius from "@/components/InnerRadius.vue";
	import {
		getCategoryList,
		getPackageList
	} from "@/api/home";
	export default {
		mixins: [ZPMixin],
		components: {
			ProductItem,
			SearchBox,
			InnerRadius
		},
		data() {
			return {
				// v-model绑定的这个变量不要在分页请求结束中自己赋值！！！
				dataList: [],
				tabList: [],
				tabObj: {},
				baseUrl: process.env.VUE_APP_STATIC_FILE_URL,
				searchKeyword: "",
				// 当前展开的分类索引数组
				activeCollapse: [0],
				isDraw: true
			};
		},
		onLoad() {
			this.getCategory();
		},

		methods: {
			reDraw() {
				this.isDraw = false;
				// this.$nextTick(() => {
				// 	this.isDraw = true;
				// })
				setTimeout(() => {
					this.isDraw = true;
				}, 50)

			},
			classifyChange(activeNames) {
				console.log('activeNames', activeNames);
				this.activeCollapse = activeNames.reduce((prev, next) => {
					if (next.status === 'open') {
						prev.push(next.name)
					}
					return prev
				}, [])
				this.reDraw()
			},
			handleSearch() {
				// if (!this.searchKeyword.trim()) return;
				this.searchKeyword = this.searchKeyword.trim();
				this.tabObj = {};
				this.$refs.paging.reload();
			},
			getCategory() {
				getCategoryList().then((res) => {
					console.log("res.rows", res.rows);
					this.tabList = res.rows;
					// if (this.tabList.length > 0) {
					// 	// 如果第一个分类有子分类，选中第一个子分类
					// 	if (this.tabList[0].children && this.tabList[0].children.length > 0) {
					// 		this.tabObj = this.tabList[0].children[0];
					// 	} else {
					// 		this.tabObj = this.tabList[0];
					// 	}
					// 	// 默认打开第一个分类
					// 	this.$nextTick(() => {
					// 		this.activeCollapse = [0];
					// 	});
					// }
				});
			},
			// 获取标题样式
			getTitleStyle(item) {
				return {
					color: '#835D31',
					fontSize: '24rpx',
					fontWeight: 'normal',
					backgroundColor: this.tabObj.id === item.id ? 'transparent' : 'transparent',
					padding: '10rpx 0',
					textAlign: 'center'
				};
			},

			tabsChange(obj) {
				console.log("选择分类", obj);
				// 先将tabObj设为null，强制销毁当前激活的InnerRadius组件
				const oldObj = this.tabObj;
				this.tabObj = {};

				// 使用nextTick确保组件销毁后再设置新的tabObj
				this.$nextTick(() => {
					this.tabObj = obj;
					console.log('cate', this.tabObj);
					
					// 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
					this.$refs.paging.reload();
				});
			},
			queryList(pageNo, pageSize) {
				// 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
				// 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
				// 模拟请求服务器获取分页数据，请替换成自己的网络请求
				// if (!this.tabObj.id) {
				// 	this.$refs.paging.complete([]);
				// 	return;
				// }
				const cate =
					this.tabObj.id === "all" ?
					null : {
						categoryId: this.tabObj.id,
					};
				const params = {
					pageNum: pageNo,
					pageSize: pageSize,
					reasonable: false,
					name: this.searchKeyword,
					...cate,
				};
				getPackageList(params)
					.then((res) => {
						// 将请求的结果数组传递给z-paging
						this.$refs.paging.complete(res.rows);
					})
					.catch((res) => {
						// 如果请求失败写this.$refs.paging.complete(false);
						// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
						// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
						this.$refs.paging.complete(false);
					});
			},
			itemClick(item, index) {
				uni.$u.route({
					type: "navigateTo",
					url: "/pages/prdDetail/index",
					params: {
						id: item.id,
					},
				});
			},
		},
	};
</script>

<style scoped lang="scss">
	.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background: linear-gradient(to bottom,
				#e3c997,
				#ebd7b1,
				#f7edda,
				#fbf5ea,
				#fbf6ed,
				#faf7f5);
		overflow: hidden;

		.header {
			padding: 30rpx;
			padding-top: 100rpx;

			.title {
				font-size: 36rpx;
				font-weight: 600;
				color: #333;
				text-align: center;
			}
		}

		.main-container {
			display: flex;
			flex: 1;
			margin: 0 20rpx;
			// padding: 0 20rpx;
			overflow: hidden;

			.category-list {
				width: 175rpx;
				min-width: 175rpx;
				background: linear-gradient(to bottom, #E9C58C, #EDCF9E, #F2DCB7, #F4E3C3);
				padding: 10rpx 0;
				margin-bottom: 40rpx;
				border-top-left-radius: 20rpx;
				border-bottom-left-radius: 20rpx;

				/deep/ .u-collapse-item {
					background-color: transparent;

					.uicon-arrow-right {
						font-size: 26rpx !important;
					}

					.u-cell__body {
						padding: 20rpx;
					}

					.u-cell__title-text {
						color: #835D31;
						font-size: 22rpx;
					}

					.u-collapse-item__content__text {
						padding: 0;
					}

					.u-collapse-item__title {
						padding: 0 !important;
						background-color: transparent !important;

						&:active {
							background-color: transparent !important;
						}

						&.u-collapse-item__title--active {
							background: linear-gradient(to bottom, #dab073, #e7c798, #f1dbba);
							border-radius: 20rpx;
						}
					}

					.u-cell {
						background-color: transparent !important;

						&:active {
							background-color: transparent !important;
						}
					}

					.u-collapse-item__content {
						background-color: transparent;
						padding: 0;
					}

					.u-icon {
						width: 20rpx;
						height: 20rpx;
					}
				}

				.empty-content {
					height: 10rpx;
				}

				.sub-category-list {
					// padding: 0 10rpx;
					padding: 30rpx 0 30rpx 20rpx;

					.sub-category-item {
						padding: 20rpx 10rpx 20rpx 0;
						margin-bottom: 10rpx;
						position: relative;
						display: flex;

						&:last-child {
							margin-bottom: 0;
						}

						.sub-category-text {
							color: #595757;
							padding-left: 20rpx;
							font-size: 22rpx;
							text-align: left;
							display: block;
							flex: 1;
						}

						&.active {
							background: #fff;
							border-top-left-radius: 50rpx;
							border-bottom-left-radius: 50rpx;
							position: relative;
							z-index: 2;

							.sub-category-text {
								color: #595757;
								padding-left: 20rpx;
							}

							.sub-category-suffix-top {
								position: absolute;
								top: -35rpx;
								right: -0;
							}

							.sub-category-suffix-btm {
								position: absolute;
								bottom: -35rpx;
								right: -0;
							}
						}
					}
				}
			}

			.product-list-container {
				position: relative;
				left: -4rpx;
				flex: 1;
				height: calc(100vh - 346rpx);
				background-color: #fff;
				// margin-bottom: 40rpx;
				padding: 10rpx 20rpx;
				border-top-right-radius: 20rpx;
				border-bottom-right-radius: 20rpx;
			}

			.product-list {
				flex: 1;
				background-color: #fff;

				.product-grid {
					display: grid;
					grid-template-columns: 1fr 1fr;
					gap: 10rpx;

					.product-item {
						// flex: 1;
						overflow: hidden;
						// width: 50%;
						// padding: 10rpx;
					}
				}
			}
		}
	}

	.cro {
		width: 20px;
		height: 100px;
		border: 1px solid #58C4E6;
		position: relative;
		background: red;
	}

	.cro_left_top,
	.cro_right_top,
	.cro_left_bottom,
	.cro_right_bottom {
		position: absolute;
		width: 20px;
		height: 20px;
		border: 1px solid #fff;
		z-index: 1;
		background: #fff;

	}

	.cro_left_top {
		top: -1px;
		left: -1px;
		border-radius: 0px 0px 20px 0px;
		border-bottom: 1px solid #58C4E6;
		border-right: 1px solid #58C4E6;
	}

	.cro_right_top {
		top: -1px;
		right: -1px;
		border-radius: 0px 0px 0px 20px;
		border-bottom: 1px solid #58C4E6;
		border-left: 1px solid #58C4E6;
	}

	.cro_left_bottom {
		left: -1px;
		bottom: -1px;
		border-radius: 0px 20px 0px 0px;

		border-top: 1px solid #58C4E6;

		border-right: 1px solid #58C4E6;

	}

	.cro_right_bottom {

		right: -1px;

		bottom: -1px;

		border-radius: 20px 0px 0px 0px;

		border-top: 1px solid #58C4E6;

		border-left: 1px solid #58C4E6;

	}
</style>