export const getOrderList = (params = {}) => {
  return uni.$u.http.get('/app/orderInfo/list', {
    params
  })
}


export const findOrder = (id) => {
  return uni.$u.http.get(`app/orderInfo/${id}`)
}

/**
 * 申请退款
 */
export const refundOrder = (params) => {
  return uni.$u.http.post(`/app/orderInfo/refund`, params)
}

// 取消订单
export const closeOrder = (id) => {
  return uni.$u.http.get(`app/orderInfo/cancel/${id}`)
}

// 查看分销订单
export const getDistributeOrderList = (params = {}) => {
  return uni.$u.http.get('/app/orderInfo/distribution/list', {
    params
  })
}

// 确认收货
export const confirmReceive = (id) => {
  return uni.$u.http.put(`/app/orderInfo/confirmReceipt/${id}`)
}

// 获取地址列表
export const getDistrictList = () => {
  return uni.$u.http.get(`app/district/list`)
}

export const addAddress = (params) => {
  return uni.$u.http.post(`/app/address`, params)
}

// 获取地址详情
export const getAddressDetail = (params) => {
  return uni.$u.http.get(`/app/address/list`, {
    params
  })
}
