
/**
 * 更新用户信息
 */
export const updateBaseInfo = (params) => {
	return uni.$u.http.put(`/app/health/updateBaseInfo`, params)
}

/**
 * 注册分销商
 */
export const registerDistribute = (params) => {
	return uni.$u.http.post(`/app/distributor/register`, params)
}

/**
 * 获取分销商详情
 */
export const getDistributeDetail = (id) => {
	return uni.$u.http.get(`/app/user/getAuditStatus/${id}`)
}


/**
 * 获取我的钱包的数据
 */
export const getMyWallet = (id) => {
	return uni.$u.http.get(`/app/distributor/myWallet`)
}

/**
 * 获取提示记录
 */
export const getWithdrawRecord = (params) => {
	return uni.$u.http.get(`/app/distributor/withdrawalRecord`,params)
}

/**
 * 获取未读消息数量
 */
export const getUnreadMessageTotal = (params) => {
	return uni.$u.http.get(`/app/message/count`,params)
}

/**
 * 获取的我消息列表
 */
export const getMessageList = (params) => {
	return uni.$u.http.get(`/app/message/list`, { params })
}

/**
 * 获取消息详情
 */
export const getMessageDetail = (id) => {
	return uni.$u.http.get(`/app/message/${id}`)
}

/**
 * 申请提现
 */
export const applyWithdraw = (params) => {
	return uni.$u.http.post(`/app/distributor/withdrawalApply`, params)
}

/**
 * 取消提现
 */
export const withdrawalCancel = (params) => {
	return uni.$u.http.post(`/app/distributor/withdrawalCancel`, params)
}

/**
 * 获取分销中心数据
 */
export const getDistributeCenter = (id) => {
	return uni.$u.http.get(`/app/distributor/center/${id}`)
}

