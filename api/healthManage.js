/**
 * 新增血压脉搏记录
 */
export const insertBlood = (params) => {
  return uni.$u.http.post(`/app/bloodPressure`, params)
}


/**
 * 新增体重记录
 */
export const insertWeight = (params) => {
  return uni.$u.http.post(`/app/weight`, params)
}

/**
 * 新增睡眠记录
 */
export const insertSleep = (params) => {
  return uni.$u.http.post(`/app/sleep`, params)
}

/**
 * 新增饮酒记录
 */
export const insertWine = (params) => {
  return uni.$u.http.post(`/app/drink`, params)
}

/**
 * 新增排便记录
 */
export const insertDefeca = (params) => {
  return uni.$u.http.post(`/app/defeca`, params)
}

/**
 * 新增血糖记录
 */
export const insertBloodSugar = (params) => {
  return uni.$u.http.post(`/app/bloodSugar`, params)
}

/**
 * 新增运动记录
 */
export const insertSport = (params) => {
  return uni.$u.http.post(`/app/sport`, params)
}

/**
 * 新增饮食记录
 */
export const insertFood = (params) => {
  return uni.$u.http.post(`/app/meals`, params)
}

/**
 * 新增健康心率记录
 */
export const insertHeartRate = (params) => {
  return uni.$u.http.post(`/app/heartRate`, params)
}

/**
 * 新增健康血氧记录
 */
export const insertBloodOxygen = (params) => {
  return uni.$u.http.post(`/app/oximetry`, params)
}

/**
 * 获取用户填写问卷的身高信息
 */
export const findUserHeight = (id) => {
  return uni.$u.http.get(`/app/questionnaire/getUserQuestionnaireHeight/${id}`)
}


/**
 * 获取可打卡的类型
 */
export const getAvailableCheckinCategories = () => {
  return uni.$u.http.get(`/app/healthCheck/getAvailableCheckinCategories`)
}


/**
 * 获取打卡记录
 */
export const getClockinList = (params) => {
  return uni.$u.http.get(`/app/healthCheck/getCheckinHistoryRecord`, { params })
}

/**
 * 获取睡眠历史数据
 */
export const getSleepHistoryData = () => {
  return uni.$u.http.get(`/app/sleep/sleep`)
}

/**
 * 获取用户打卡历史记录
 */
export const getHealthCheckHistory = (params) => {
  return uni.$u.http.get(`/app/healthCheck/getHistory`, { params })
}





