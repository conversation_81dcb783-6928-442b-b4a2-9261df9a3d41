/**
 * 购物车相关API接口
 */

/**
 * 添加商品到购物车
 * @param {Object} params 请求参数
 * @param {number} params.goodsId 商品ID（必填）
 * @param {number} params.quantity 商品数量（必填）
 * @param {number} params.price 商品单价（必填）
 * @param {string} params.goodsName 商品名称
 * @param {string} params.goodsImage 商品封面图
 * @param {number} params.originalPrice 商品原价
 * @param {number} params.checked 是否选中：1-选中，0-未选中
 * @param {number} params.userId 用户ID
 * @returns {Promise} 接口响应
 */
export const addToCart = (params) => {
    return uni.$u.http.post('/app/shoppingCart/add', params);
};

/**
 * 获取购物车商品列表
 * @param {Object} params 请求参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页数量
 * @param {number} params.userId 用户ID
 * @returns {Promise} 接口响应
 */
export const getCartList = (params = {}) => {
    return uni.$u.http.get('/app/shoppingCart/list', {
        params,
    });
};

/**
 * 更新购物车商品选中状态
 * @param {Object} params 请求参数
 * @param {number} params.itemId 商品项ID
 * @param {boolean} params.selected 是否选中
 * @returns {Promise} 接口响应
 */
export const updateCartItemSelect = (params) => {
    return uni.$u.http.put('/app/cart/select', params);
};

/**
 * 更新购物车商品数量
 * @param {Object} params 请求参数
 * @param {number} params.id 商品项ID
 * @param {number} params.quantity 商品数量
 * @returns {Promise} 接口响应
 */
export const updateCartItemQuantity = (params) => {
    return uni.$u.http.post('/app/shoppingCart/updateQuantity', {}, { params });
};

/**
 * 删除购物车商品（单个或批量）
 * @param {number|number[]} itemIds 商品项ID或ID数组
 * @returns {Promise} 接口响应
 */
export const removeCartItems = (itemIds) => {
    // 确保itemIds是数组格式
    const idsArray = Array.isArray(itemIds) ? itemIds : [itemIds];

    // 将ID数组转换为逗号分隔的字符串
    const idsString = idsArray.join(',');

    // 使用query参数传递ID字符串
    return uni.$u.http.post(
        '/app/shoppingCart/delete',
        {},
        {
            params: {
                ids: idsString,
            },
        }
    );
};
// 确认订单页面获取最新的商品信息
export const generateConfirmOrder = (params) => {
    return uni.$u.http.post('/app/orderInfo/generateConfirmOrder', {}, { params });
};
